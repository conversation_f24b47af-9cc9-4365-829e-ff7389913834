{"numTotalTestSuites": 2, "numPassedTestSuites": 2, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 4, "numPassedTests": 1, "numFailedTests": 3, "numPendingTests": 0, "numTodoTests": 0, "startTime": 1753889568819, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["", "PdfReaderView"], "fullName": " PdfReaderView 应该正确导入vue3-pdf-app", "status": "failed", "title": "应该正确导入vue3-pdf-app", "duration": 129, "failureMessages": ["expected [Function] to not throw an error but 'ReferenceError: document is not defin…' was thrown"], "location": {"line": 91, "column": 12}}, {"ancestorTitles": ["", "PdfReaderView"], "fullName": " PdfReaderView 应该有正确的依赖版本", "status": "passed", "title": "应该有正确的依赖版本", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "PdfReaderView"], "fullName": " PdfReaderView 应该能够正确加载PDF相关的服务", "status": "failed", "title": "应该能够正确加载PDF相关的服务", "duration": 2, "failureMessages": ["expected [Function] to not throw an error but 'Error: Cannot find module \\'../src/re…' was thrown"], "location": {"line": 107, "column": 12}}, {"ancestorTitles": ["", "PdfReaderView"], "fullName": " PdfReaderView 应该正确配置vite以包含vue3-pdf-app", "status": "failed", "title": "应该正确配置vite以包含vue3-pdf-app", "duration": 135, "failureMessages": ["__dirname is not defined in ES module scope"], "location": {"line": 7, "column": 17}}], "startTime": 1753889569191, "endTime": 1753889569459, "status": "failed", "message": "", "name": "D:/reader/desktop/tests/PdfReaderView.test.ts"}]}