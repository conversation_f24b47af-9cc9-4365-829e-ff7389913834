import{d as B,b as i,e as y,c,f as s,a as e,w as v,u as a,g as C,r as h,h as V,s as E,F as I,i as L,t as l,j as $,o as u,_ as j}from"./index-DS8Rgqx0.js";const _="data:image/svg+xml,%3csvg%20width='120'%20height='160'%20viewBox='0%200%20120%20160'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3crect%20width='120'%20height='160'%20rx='8'%20fill='%23f5f5f5'%20stroke='%23e0e0e0'%20stroke-width='1'/%3e%3crect%20x='20'%20y='30'%20width='80'%20height='4'%20rx='2'%20fill='%23d0d0d0'/%3e%3crect%20x='20'%20y='45'%20width='60'%20height='3'%20rx='1.5'%20fill='%23e0e0e0'/%3e%3crect%20x='20'%20y='55'%20width='70'%20height='3'%20rx='1.5'%20fill='%23e0e0e0'/%3e%3crect%20x='35'%20y='80'%20width='50'%20height='50'%20rx='4'%20fill='%23e8e8e8'/%3e%3cpath%20d='M50%2095L60%20105L70%2095'%20stroke='%23c0c0c0'%20stroke-width='2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e",F={class:"bookshelf-home"},H={class:"bookshelf-content"},M={class:"quick-actions"},N={class:"action-icon"},q={class:"action-icon"},D={class:"action-icon"},R={class:"recent-books"},S={class:"books-grid"},T={class:"book-cover"},z=["src","alt"],A={class:"book-info"},G={class:"book-progress"},J={class:"stats-section"},K={class:"stats-grid"},O={class:"stat-card"},P={class:"stat-number"},Q={class:"stat-card"},U={class:"stat-number"},W={class:"stat-card"},X={class:"stat-number"},Y={class:"stat-card"},Z={class:"stat-number"},ss=B({__name:"BookshelfHomeView",setup(ts){const p=$(),g=i([{id:"1",title:"示例图书1",author:"作者1",cover:"",progress:65},{id:"2",title:"示例图书2",author:"作者2",cover:"",progress:30},{id:"3",title:"示例图书3",author:"作者3",cover:"",progress:90}]),f=i(12),k=i(5),m=i(3),w=i(28),r=n=>{p.push(n)},b=n=>{const t=n.target;t.src=_};return y(()=>{console.log("书架首页已加载")}),(n,t)=>{const d=h("el-icon"),x=h("el-progress");return u(),c("div",F,[t[12]||(t[12]=s("div",{class:"bookshelf-header"},[s("h1",{class:"page-title"},"我的书架"),s("p",{class:"page-description"},"管理您的电子书收藏")],-1)),s("div",H,[s("div",M,[s("div",{class:"action-card",onClick:t[0]||(t[0]=o=>r("/bookshelf/library"))},[s("div",N,[e(d,null,{default:v(()=>[e(a(C))]),_:1})]),t[3]||(t[3]=s("div",{class:"action-content"},[s("h3",null,"图书列表"),s("p",null,"浏览所有图书")],-1))]),s("div",{class:"action-card",onClick:t[1]||(t[1]=o=>r("/bookshelf/import"))},[s("div",q,[e(d,null,{default:v(()=>[e(a(V))]),_:1})]),t[4]||(t[4]=s("div",{class:"action-content"},[s("h3",null,"导入图书"),s("p",null,"添加新的电子书")],-1))]),s("div",{class:"action-card",onClick:t[2]||(t[2]=o=>r("/bookshelf/search"))},[s("div",D,[e(d,null,{default:v(()=>[e(a(E))]),_:1})]),t[5]||(t[5]=s("div",{class:"action-content"},[s("h3",null,"搜索图书"),s("p",null,"快速查找图书")],-1))])]),s("div",R,[t[6]||(t[6]=s("h2",null,"最近阅读",-1)),s("div",S,[(u(!0),c(I,null,L(g.value,o=>(u(),c("div",{key:o.id,class:"book-card"},[s("div",T,[s("img",{src:o.cover||a(_),alt:o.title,onError:b},null,40,z)]),s("div",A,[s("h3",null,l(o.title),1),s("p",null,l(o.author),1),s("div",G,[e(x,{percentage:o.progress,"show-text":!1},null,8,["percentage"]),s("span",null,l(o.progress)+"%",1)])])]))),128))])]),s("div",J,[t[11]||(t[11]=s("h2",null,"阅读统计",-1)),s("div",K,[s("div",O,[s("div",P,l(f.value),1),t[7]||(t[7]=s("div",{class:"stat-label"},"总图书数",-1))]),s("div",Q,[s("div",U,l(k.value),1),t[8]||(t[8]=s("div",{class:"stat-label"},"已读完",-1))]),s("div",W,[s("div",X,l(m.value),1),t[9]||(t[9]=s("div",{class:"stat-label"},"正在阅读",-1))]),s("div",Y,[s("div",Z,l(w.value),1),t[10]||(t[10]=s("div",{class:"stat-label"},"书签数",-1))])])])])])}}}),es=j(ss,[["__scopeId","data-v-0c68ebad"]]);export{es as default};
