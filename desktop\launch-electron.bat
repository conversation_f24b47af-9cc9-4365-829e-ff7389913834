@echo off
echo 🚀 启动 Yu Reader 桌面应用...

REM 设置环境变量
set NODE_ENV=development

REM 检查必要文件
if not exist "dist\main\app.js" (
    echo ❌ 主进程文件不存在，请先运行开发服务器
    echo 运行命令: npm run dev
    pause
    exit /b 1
)

if not exist "dist\preload\index.js" (
    echo ❌ 预加载脚本不存在，请先运行开发服务器
    echo 运行命令: npm run dev
    pause
    exit /b 1
)

echo ✅ 文件检查通过
echo 📱 启动Electron应用...

REM 启动Electron
node_modules\.bin\electron.cmd .

echo 📱 应用已退出
pause
