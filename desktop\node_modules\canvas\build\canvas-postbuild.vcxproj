<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{3777DBA3-5E23-4B86-4443-EB4D753BA394}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>canvas-postbuild</RootNamespace>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props"/>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Label="Locals">
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props"/>
  <ImportGroup Label="ExtensionSettings"/>
  <ImportGroup Label="PropertySheets">
    <Import Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props"/>
  </ImportGroup>
  <PropertyGroup Label="UserMacros"/>
  <PropertyGroup>
    <ExecutablePath>$(ExecutablePath);$(MSBuildProjectDirectory)\..\bin\;$(MSBuildProjectDirectory)\..\bin\</ExecutablePath>
    <IgnoreImportLibrary>true</IgnoreImportLibrary>
    <IntDir>$(Configuration)\obj\$(ProjectName)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</LinkIncremental>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.node</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.node</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">.node</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">.node</TargetExt>
    <TargetName>$(ProjectName)</TargetName>
    <TargetPath>$(OutDir)\$(ProjectName).node</TargetPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.electron-gyp\28.3.3\include\node;C:\Users\<USER>\.electron-gyp\28.3.3\src;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\config;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\openssl\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\uv\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\zlib;C:\Users\<USER>\.electron-gyp\28.3.3\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++17 %(AdditionalOptions)</AdditionalOptions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>false</ExceptionHandling>
      <MinimalRebuild>false</MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>false</OmitFramePointers>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=canvas-postbuild;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;V8_DEPRECATION_WARNINGS;V8_IMMINENT_DEPRECATION_WARNINGS;_GLIBCXX_USE_CXX11_ABI=1;ELECTRON_ENSURE_CONFIG_GYPI;USING_ELECTRON_CONFIG_GYPI;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;OPENSSL_NO_ASM;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;V8_ENABLE_CHECKS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\.electron-gyp\\28.3.3\\ia32\\node.lib&quot;</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)$(ProjectName).node</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetExt>.node</TargetExt>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.electron-gyp\28.3.3\include\node;C:\Users\<USER>\.electron-gyp\28.3.3\src;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\config;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\openssl\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\uv\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\zlib;C:\Users\<USER>\.electron-gyp\28.3.3\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=canvas-postbuild;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;V8_DEPRECATION_WARNINGS;V8_IMMINENT_DEPRECATION_WARNINGS;_GLIBCXX_USE_CXX11_ABI=1;ELECTRON_ENSURE_CONFIG_GYPI;USING_ELECTRON_CONFIG_GYPI;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;OPENSSL_NO_ASM;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;V8_ENABLE_CHECKS;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.electron-gyp\28.3.3\include\node;C:\Users\<USER>\.electron-gyp\28.3.3\src;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\config;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\openssl\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\uv\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\zlib;C:\Users\<USER>\.electron-gyp\28.3.3\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++17 %(AdditionalOptions)</AdditionalOptions>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>false</ExceptionHandling>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>true</OmitFramePointers>
      <Optimization>Full</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=canvas-postbuild;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;V8_DEPRECATION_WARNINGS;V8_IMMINENT_DEPRECATION_WARNINGS;_GLIBCXX_USE_CXX11_ABI=1;ELECTRON_ENSURE_CONFIG_GYPI;USING_ELECTRON_CONFIG_GYPI;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;OPENSSL_NO_ASM;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\.electron-gyp\\28.3.3\\ia32\\node.lib&quot;</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)$(ProjectName).node</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetExt>.node</TargetExt>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.electron-gyp\28.3.3\include\node;C:\Users\<USER>\.electron-gyp\28.3.3\src;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\config;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\openssl\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\uv\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\zlib;C:\Users\<USER>\.electron-gyp\28.3.3\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=canvas-postbuild;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;V8_DEPRECATION_WARNINGS;V8_IMMINENT_DEPRECATION_WARNINGS;_GLIBCXX_USE_CXX11_ABI=1;ELECTRON_ENSURE_CONFIG_GYPI;USING_ELECTRON_CONFIG_GYPI;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;OPENSSL_NO_ASM;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\binding.gyp"/>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\reader\desktop\node_modules\@electron\node-gyp\src\win_delay_load_hook.cc"/>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\GTK\bin\zlib1.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\zlib1.dll&quot; &quot;$(OutDir)zlib1.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/zlib1.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\zlib1.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libintl-8.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libintl-8.dll&quot; &quot;$(OutDir)libintl-8.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libintl-8.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libintl-8.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libpng14-14.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libpng14-14.dll&quot; &quot;$(OutDir)libpng14-14.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libpng14-14.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libpng14-14.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libpangocairo-1.0-0.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libpangocairo-1.0-0.dll&quot; &quot;$(OutDir)libpangocairo-1.0-0.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libpangocairo-1.0-0.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libpangocairo-1.0-0.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libpango-1.0-0.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libpango-1.0-0.dll&quot; &quot;$(OutDir)libpango-1.0-0.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libpango-1.0-0.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libpango-1.0-0.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libpangoft2-1.0-0.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libpangoft2-1.0-0.dll&quot; &quot;$(OutDir)libpangoft2-1.0-0.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libpangoft2-1.0-0.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libpangoft2-1.0-0.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libpangowin32-1.0-0.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libpangowin32-1.0-0.dll&quot; &quot;$(OutDir)libpangowin32-1.0-0.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libpangowin32-1.0-0.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libpangowin32-1.0-0.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libcairo-2.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libcairo-2.dll&quot; &quot;$(OutDir)libcairo-2.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libcairo-2.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libcairo-2.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libfontconfig-1.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libfontconfig-1.dll&quot; &quot;$(OutDir)libfontconfig-1.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libfontconfig-1.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libfontconfig-1.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libfreetype-6.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libfreetype-6.dll&quot; &quot;$(OutDir)libfreetype-6.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libfreetype-6.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libfreetype-6.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libglib-2.0-0.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libglib-2.0-0.dll&quot; &quot;$(OutDir)libglib-2.0-0.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libglib-2.0-0.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libglib-2.0-0.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libgobject-2.0-0.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libgobject-2.0-0.dll&quot; &quot;$(OutDir)libgobject-2.0-0.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libgobject-2.0-0.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libgobject-2.0-0.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libgmodule-2.0-0.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libgmodule-2.0-0.dll&quot; &quot;$(OutDir)libgmodule-2.0-0.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libgmodule-2.0-0.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libgmodule-2.0-0.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libgthread-2.0-0.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libgthread-2.0-0.dll&quot; &quot;$(OutDir)libgthread-2.0-0.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libgthread-2.0-0.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libgthread-2.0-0.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\GTK\bin\libexpat-1.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(OutDir)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\GTK\bin\libexpat-1.dll&quot; &quot;$(OutDir)libexpat-1.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:/GTK/bin/libexpat-1.dll to $(OutDir)</Message>
      <Outputs>$(OutDir)\libexpat-1.dll</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="canvas.vcxproj">
      <Project>{E2F309FC-3BDD-4AC5-C0E4-A3922029EE09}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets"/>
  <ImportGroup Label="ExtensionTargets"/>
</Project>
