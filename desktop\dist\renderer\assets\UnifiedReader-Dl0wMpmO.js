var It=Object.defineProperty;var Rt=(_,t,l)=>t in _?It(_,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):_[t]=l;var we=(_,t,l)=>Rt(_,typeof t!="symbol"?t+"":t,l);import{U as Tt,b as v,V as Et,q as ne,d as Le,W as At,e as Me,X as be,Y as We,v as Fe,c as x,f as e,k as q,a as n,w as b,l as W,u as h,Z as Te,r as j,t as U,$ as Ke,a0 as Qe,a1 as Ze,a2 as qe,a3 as Je,s as ye,a4 as St,a5 as tt,n as Ie,B as pe,a6 as ot,a7 as zt,C as Ft,F as Ue,i as Be,G as De,a8 as nt,a9 as Ut,aa as lt,Q as Ve,j as je,m as oe,o as P,_ as He,ab as at,ac as Bt,ad as Dt,ae as Vt,A as Re,D as Lt}from"./index-DS8Rgqx0.js";import{u as Mt}from"./unifiedLibrary-DwIwCSXr.js";function Ht(_){const{font:t,page:l}=_;let a;if(typeof document<"u"){const D=document.createElement("canvas").getContext("2d");D.font=`${t.weight} ${t.size}px ${t.family}`,a=D.measureText("中").width}else a=Nt(t.size,t.family);const f=t.size*t.lineHeight,r=l.width-l.marginLeft-l.marginRight,c=l.height-l.marginTop-l.marginBottom,s=Math.floor(r/a),i=Math.floor(c/f),w=s*i;return{charWidth:a,lineHeight:f,charsPerLine:s,linesPerPage:i,charsPerPage:w}}function Nt(_,t){let l=1;return t.includes("monospace")?l=.6:t.includes("serif")?l=.9:l=.85,_*l}function Xe(_,t){const l=Ht(t),a=[],f=_.replace(/\r\n/g,`
`).replace(/\r/g,`
`);let r=0,c=1;for(;r<f.length;){const i=Ot(f,r,c,l);a.push(i),r=i.endPosition,c++}const s=a.length>0?Math.round(f.length/a.length):0;return{totalPages:a.length,pages:a,averageCharsPerPage:s}}function Ot(_,t,l,a){const{charsPerLine:f,linesPerPage:r}=a;let c=t,s=0,i="";for(;s<r&&c<_.length;){const w=Gt(_,c,f);if(w.content.length===0)break;i+=w.content,c=w.nextPosition,s++,w.isNewParagraph&&(s+=.5)}return{pageNumber:l,startPosition:t,endPosition:c,content:i,lineCount:s}}function Gt(_,t,l){if(t>=_.length)return{content:"",nextPosition:t,isNewParagraph:!1};const a=_.indexOf(`
`,t),f=a!==-1;let r;if(f&&a-t<=l)r=a+1;else if(r=Math.min(t+l,_.length),r<_.length){const i=Wt(_,t,r);i>t&&(r=i)}const c=_.substring(t,r),s=f&&a<r;return{content:c,nextPosition:r,isNewParagraph:s}}function Wt(_,t,l){for(let a=l-1;a>t;a--){const f=_[a];if(f===" "||f==="	"||f==="-"||f==="，"||f==="。")return a+1}return l}function Ge(_,t){for(const l of t)if(_>=l.startPosition&&_<l.endPosition)return l.pageNumber;return t.length>0?t[t.length-1].pageNumber:1}function et(_,t){return t.find(l=>l.pageNumber===_)||null}function jt(_,t){return t===0?0:Math.round(_/t*100)}class Yt{constructor(t){we(this,"bookContent",null);we(this,"originalText","");we(this,"paginationResult",null);we(this,"currentSettings");we(this,"events",{});we(this,"encodingResult",null);this.currentSettings={font:{family:"Microsoft YaHei, SimSun, serif",size:16,weight:"normal",lineHeight:1.6},page:{width:800,height:600,marginTop:40,marginBottom:40,marginLeft:60,marginRight:60},theme:{mode:"light",backgroundColor:"#ffffff",textColor:"#333333"},readingMode:"pagination",zoomLevel:1,...t}}async loadBook(t){var l,a,f,r,c,s;try{const i=await this.readFileContent(t);this.originalText=i.content,this.encodingResult={encoding:i.encoding,confidence:1,text:i.content},console.log(`TxtReader.loadBook: 文件内容长度 ${((l=this.originalText)==null?void 0:l.length)||0}`),console.log(`TxtReader.loadBook: 编码 ${this.encodingResult.encoding}`),console.log(`TxtReader.loadBook: 内容预览: ${((a=this.originalText)==null?void 0:a.substring(0,200))||"空内容"}`);const w=this.detectChapters(this.originalText);return this.paginationResult=Xe(this.originalText,this.currentSettings),console.log(`TxtReader.loadBook: 分页完成，总页数 ${this.paginationResult.totalPages}`),this.bookContent={id:this.generateBookId(t),title:this.extractTitle(t),author:"未知作者",filePath:t,format:"txt",totalPages:this.paginationResult.totalPages,currentPage:1,progress:0,content:this.originalText,chapters:w,metadata:{fileSize:i.size,createdAt:new Date,language:this.detectLanguage(this.originalText)}},(r=(f=this.events).onLoadComplete)==null||r.call(f,this.bookContent),this.bookContent}catch(i){const w=i instanceof Error?i:new Error(String(i));throw(s=(c=this.events).onError)==null||s.call(c,w),w}}async getPageContent(t){var f,r,c,s,i;if(console.log(`TxtReader.getPageContent: 请求第 ${t} 页`),!this.paginationResult||!this.bookContent)throw console.error("TxtReader.getPageContent: 书籍未加载"),new Error("书籍未加载");console.log(`TxtReader.getPageContent: 总页数 ${this.paginationResult.totalPages}`);const l=et(t,this.paginationResult.pages);if(!l)throw console.error(`TxtReader.getPageContent: 页面 ${t} 不存在`),new Error(`页面 ${t} 不存在`);console.log("TxtReader.getPageContent: 页面信息",{pageNumber:l.pageNumber,startPosition:l.startPosition,endPosition:l.endPosition,contentLength:((f=l.content)==null?void 0:f.length)||0}),this.bookContent.currentPage=t,this.bookContent.progress=jt(l.startPosition,this.originalText.length),(c=(r=this.events).onPageChange)==null||c.call(r,t),(i=(s=this.events).onProgressChange)==null||i.call(s,this.bookContent.progress);const a=this.formatPageContent(l.content);return console.log(`TxtReader.getPageContent: 格式化后内容长度 ${(a==null?void 0:a.length)||0}`),a}async goToPage(t){await this.getPageContent(t)}async nextPage(){if(!this.bookContent||!this.paginationResult)return!1;const t=this.bookContent.currentPage+1;return t<=this.paginationResult.totalPages?(await this.goToPage(t),!0):!1}async previousPage(){if(!this.bookContent)return!1;const t=this.bookContent.currentPage-1;return t>=1?(await this.goToPage(t),!0):!1}async search(t){if(!this.originalText||!this.paginationResult)return[];const l=[],a=new RegExp(t,"gi");let f;for(;(f=a.exec(this.originalText))!==null;){const r=f.index,c=Ge(r,this.paginationResult.pages),s=Math.max(0,r-50),i=Math.min(this.originalText.length,r+t.length+50),w=this.originalText.substring(s,i);l.push({text:f[0],page:c,position:r,context:w})}return l}getCurrentPosition(){if(!this.bookContent||!this.paginationResult)return{page:1,characterPosition:0,scrollPosition:0};const t=et(this.bookContent.currentPage,this.paginationResult.pages);return{page:this.bookContent.currentPage,characterPosition:(t==null?void 0:t.startPosition)||0,scrollPosition:0}}async setPosition(t){if(this.paginationResult){if(t.page)await this.goToPage(t.page);else if(t.characterPosition!==void 0){const l=Ge(t.characterPosition,this.paginationResult.pages);await this.goToPage(l)}}}applySettings(t){if(this.currentSettings={...this.currentSettings,...t},this.originalText&&(t.font||t.page)){const l=this.getCurrentPosition();this.paginationResult=Xe(this.originalText,this.currentSettings),this.bookContent&&(this.bookContent.totalPages=this.paginationResult.totalPages,this.setPosition(l))}}getChapters(){var t;return((t=this.bookContent)==null?void 0:t.chapters)||[]}async goToChapter(t){const a=this.getChapters().find(f=>f.id===t);if(a&&this.paginationResult){const f=Ge(a.startPosition,this.paginationResult.pages);await this.goToPage(f)}}setEventListeners(t){this.events={...this.events,...t}}async getFullContent(){if(!this.originalText)throw new Error("书籍未加载");return this.originalText.replace(/\r\n/g,`
`).replace(/\r/g,`
`).trim()}dispose(){this.bookContent=null,this.originalText="",this.paginationResult=null,this.events={},this.encodingResult=null}async readFileContent(t){try{if(typeof window<"u"&&window.electronAPI){const r=window.electronAPI;if(r.txtReader&&r.txtReader.readFile){const c=await r.txtReader.readFile(t);return{content:c.content,encoding:c.encoding||"utf-8",size:c.size||c.content.length}}if(r.invoke){const c=await r.invoke("txt-reader:read-file",t);return{content:c.content,encoding:c.encoding||"utf-8",size:c.size||c.content.length}}if(r.txtReader&&r.txtReader.detectEncoding){const c=await r.txtReader.detectEncoding(t),s=await r.txtReader.readFile(t,c.encoding);return{content:s.content,encoding:s.encoding,size:s.size}}if(r.file&&r.file.read){const c=await r.file.read(t),s=c.buffer||c;return{content:new TextDecoder("utf-8",{fatal:!1}).decode(s),encoding:"utf-8",size:s.byteLength}}}const l=await fetch(t);if(!l.ok)throw new Error(`Failed to read file: ${l.statusText}`);const a=await l.arrayBuffer(),f=await detectEncoding(a);return{content:f.text,encoding:f.encoding,size:a.byteLength}}catch(l){throw new Error(`无法读取文件 ${t}: ${l}`)}}async readFile(t){const l=await this.readFileContent(t);return new TextEncoder().encode(l.content).buffer}generateBookId(t){return`txt_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}extractTitle(t){return(t.split(/[/\\]/).pop()||"").replace(/\.[^/.]+$/,"")||"未命名文档"}detectChapters(t){const l=[],a=t.split(`
`),f=[/^第[一二三四五六七八九十\d]+章\s*.*/,/^第[一二三四五六七八九十\d]+节\s*.*/,/^Chapter\s+\d+.*/i,/^\d+\.\s*.{1,50}$/,/^[一二三四五六七八九十]+、.*/];let r=0,c=1;for(let s=0;s<a.length;s++){const i=a[s].trim();i.length>0&&f.some(S=>S.test(i))&&(l.push({id:`chapter_${c}`,title:i,startPosition:r,endPosition:r+i.length,level:1}),c++),r+=a[s].length+1}return l.length===0&&l.push({id:"chapter_1",title:"正文",startPosition:0,endPosition:t.length,level:1}),l}detectLanguage(t){const l=/[\u4e00-\u9fff]/,a=/[a-zA-Z]/,f=t.match(l),r=t.match(a),c=f?f.length:0,s=r?r.length:0;return c>s?"zh-CN":s>0?"en":"unknown"}formatPageContent(t){return`<p>${t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/\n\n+/g,"</p><p>").replace(/\n/g,"<br>")}</p>`}}const ze=new Map,Kt=Tt("reader",()=>{const _=v(null),t=v(!1),l=v(null),a=v(null),f=v([]),r=v(!1),c=Et({font:{family:"Microsoft YaHei, SimSun, serif",size:16,weight:"normal",lineHeight:1.6},page:{width:800,height:600,marginTop:40,marginBottom:40,marginLeft:60,marginRight:60},theme:{mode:"light",backgroundColor:"#ffffff",textColor:"#333333"},readingMode:"pagination",zoomLevel:1}),s=v([]),i=v(-1),w=v({}),S=ne(()=>_.value!==null),D=ne(()=>{var g;return((g=_.value)==null?void 0:g.currentPage)||1}),O=ne(()=>{var g;return((g=_.value)==null?void 0:g.totalPages)||0}),G=ne(()=>{var g;return((g=_.value)==null?void 0:g.progress)||0}),C=ne(()=>{var g;return((g=_.value)==null?void 0:g.chapters)||[]}),V=ne(()=>i.value>0),k=ne(()=>i.value<s.value.length-1);function y(g){if(!ze.has(g))switch(g){case"txt":ze.set(g,new Yt(c));break;case"pdf":ze.set(g,{loadBook:()=>Promise.resolve(null),setEventListeners:()=>{},applySettings:()=>{},dispose:()=>{}});break;default:throw new Error(`不支持的阅读器类型: ${g}`)}return ze.get(g)}async function B(g,T){t.value=!0,l.value=null;try{const J=y(T);a.value=J,J.setEventListeners({onPageChange:le=>{_.value&&(_.value.currentPage=le)},onProgressChange:le=>{_.value&&(_.value.progress=le)},onLoadComplete:le=>{_.value=le,M()},onError:le=>{l.value=le.message}}),J.applySettings(c);const ue=await J.loadBook(g);_.value=ue,f.value=[],s.value=[],i.value=-1,M()}catch(J){throw l.value=J instanceof Error?J.message:"加载书籍失败",J}finally{t.value=!1}}async function Y(g){if(a.value)try{await a.value.goToPage(g),M()}catch(T){l.value=T instanceof Error?T.message:"跳转页面失败"}}async function $(){if(!a.value)return!1;try{const g=await a.value.nextPage();return g&&M(),g}catch(g){return l.value=g instanceof Error?g.message:"翻页失败",!1}}async function H(){if(!a.value)return!1;try{const g=await a.value.previousPage();return g&&M(),g}catch(g){return l.value=g instanceof Error?g.message:"翻页失败",!1}}async function X(g){if(!a.value||!g.trim()){f.value=[];return}r.value=!0;try{const T=await a.value.search(g.trim());f.value=T}catch(T){l.value=T instanceof Error?T.message:"搜索失败",f.value=[]}finally{r.value=!1}}async function I(g){if(a.value)try{await a.value.setPosition({page:g.page,characterPosition:g.position,scrollPosition:0}),M()}catch(T){l.value=T instanceof Error?T.message:"跳转失败"}}async function ee(g){if(a.value)try{await a.value.goToChapter(g),M()}catch(T){l.value=T instanceof Error?T.message:"跳转章节失败"}}function L(g){Object.assign(c,g),a.value&&a.value.applySettings(c)}function M(){if(!a.value)return;const g=a.value.getCurrentPosition();i.value<s.value.length-1&&(s.value=s.value.slice(0,i.value+1)),s.value.push(g),i.value=s.value.length-1,s.value.length>50&&(s.value=s.value.slice(-50),i.value=s.value.length-1)}async function de(){if(!V.value||!a.value)return;i.value--;const g=s.value[i.value];try{await a.value.setPosition(g)}catch(T){l.value=T instanceof Error?T.message:"后退失败",i.value++}}async function re(){if(!k.value||!a.value)return;i.value++;const g=s.value[i.value];try{await a.value.setPosition(g)}catch(T){l.value=T instanceof Error?T.message:"前进失败",i.value--}}function ce(g,T){w.value[g]=T}function R(g){return w.value[g]||null}function m(){l.value=null}function K(){a.value&&a.value.dispose(),_.value=null,a.value=null,f.value=[],s.value=[],i.value=-1,l.value=null}return{currentBook:_,isLoading:t,error:l,searchResults:f,isSearching:r,settings:c,isBookLoaded:S,currentPage:D,totalPages:O,progress:G,chapters:C,canGoBack:V,canGoForward:k,loadBook:B,goToPage:Y,nextPage:$,previousPage:H,searchText:X,goToSearchResult:I,goToChapter:ee,updateSettings:L,goBack:de,goForward:re,saveProgress:ce,getProgress:R,clearError:m,closeBook:K,currentReader:a}}),Qt={class:"embedded-txt-reader"},Zt={class:"reader-toolbar"},qt={class:"toolbar-left"},Jt={key:0,class:"book-info"},Xt={class:"book-title"},eo={class:"reading-progress"},to={class:"toolbar-center"},oo={class:"ai-buttons-group"},no={class:"toolbar-right"},lo={class:"reader-body"},ao={key:0,class:"search-panel full-height"},so={class:"panel-header"},io={class:"search-content"},ro={class:"search-input-wrapper"},co={key:0,class:"search-results-header"},uo={class:"search-navigation"},vo={class:"search-index"},go={key:1,class:"search-results-list"},fo=["onClick"],po={class:"result-index"},ho={class:"result-text"},mo={key:0,class:"more-results"},_o={key:2,class:"no-results"},wo={key:3,class:"search-tips"},Po={key:1,class:"toc-panel full-height"},ko={class:"panel-header"},bo={class:"toc-content"},yo={key:0,class:"toc-loading"},xo={key:1,class:"toc-empty"},Co={key:2,class:"toc-tree"},$o=["onClick"],Io={class:"toc-title"},Ro={key:0,class:"toc-progress"},To={key:2,class:"settings-panel full-height"},Eo={class:"panel-header"},Ao={class:"settings-content"},So={class:"setting-group"},zo={class:"setting-group"},Fo={class:"setting-group"},Uo={class:"setting-group"},Bo={class:"setting-note"},Do={key:3,class:"ai-guide-panel full-height"},Vo={class:"panel-header"},Lo={class:"ai-panel-content"},Mo={class:"ai-panel-description"},Ho={key:4,class:"editor-panel full-height"},No={class:"panel-header"},Oo={class:"ai-panel-content"},Go={class:"ai-panel-description"},Wo={key:5,class:"ai-chat-panel full-height"},jo={class:"panel-header"},Yo={class:"ai-panel-content"},Ko={class:"ai-panel-description"},Qo={key:6,class:"ai-explore-panel full-height"},Zo={class:"panel-header"},qo={class:"ai-panel-content"},Jo={class:"ai-panel-description"},Xo={key:7,class:"ai-evaluation-panel full-height"},en={class:"panel-header"},tn={class:"ai-panel-content"},on={class:"ai-panel-description"},nn=["innerHTML"],ln={key:0,class:"loading-overlay"},an={key:1,class:"error-overlay"},sn={class:"error-text"},rn=Le({__name:"EmbeddedTxtReader",props:{bookId:{default:""}},setup(_){const t=_,l=je(),a=Kt(),f=Mt(),r=v(),c=v(),s=v(!1),i=v(!1),w=v(!1),S=v(!1),D=v(!1),O=v(!1),G=v(!1),C=v(!1),V=v(""),k=v([]),y=v(-1),B=v(""),Y=v(0),$=v(0),H=v([]),X=v(!1),I=v(""),ee=v(""),L=v({font:{family:"Microsoft YaHei",size:16,lineHeight:1.6}}),M=At(),{currentBook:de,isLoading:re,error:ce}=a,R=ne(()=>{const u=M.currentTheme;return u?u.name:"未知"}),m=ne(()=>s.value||w.value||i.value||S.value||D.value||O.value||G.value||C.value),K=ne(()=>({fontFamily:L.value.font.family,fontSize:`${L.value.font.size}px`,lineHeight:L.value.font.lineHeight,height:"100%",overflowY:"auto",padding:"20px",whiteSpace:"pre-wrap"})),g=ne(()=>{if($.value===0)return"0%";const u=Math.round(Y.value/$.value*100);return`${Math.min(u,100)}%`}),T=ne(()=>{if(!B.value)return"";let u=B.value;if(k.value.length>0&&V.value){const o=new RegExp(`(${z(V.value)})`,"gi");u=u.replace(o,'<mark class="search-highlight">$1</mark>')}return u}),J=()=>{_e(),l.push("/bookshelf/library")},ue=()=>{if(!V.value.trim()){k.value=[],y.value=-1;return}he(V.value)},le=()=>{V.value.trim()||(k.value=[],y.value=-1)},he=u=>{const o=B.value,p=new RegExp(z(u),"gi"),A=[];let F;for(;(F=p.exec(o))!==null;)A.push({index:A.length,text:F[0],position:F.index});k.value=A,y.value=A.length>0?0:-1,A.length>0&&ae(0)},xe=()=>{y.value>0&&(y.value--,ae(y.value))},E=()=>{y.value<k.value.length-1&&(y.value++,ae(y.value))},d=()=>{V.value="",k.value=[],y.value=-1},se=u=>{y.value=u,ae(u)},Q=u=>{const o=B.value,p=Math.max(0,u.position-20),A=Math.min(o.length,u.position+u.text.length+20),F=o.substring(p,A),Z=new RegExp(`(${z(V.value)})`,"gi");return F.replace(Z,"【$1】")},ae=u=>{if(!r.value||u<0||u>=k.value.length)return;const o=k.value[u],p=B.value,A=o.position,F=p.length,Z=A/F,ie=r.value,N=(ie.scrollHeight-ie.clientHeight)*Z;ie.scrollTo({top:N,behavior:"smooth"}),Y.value=N,console.log(`跳转到第 ${u+1} 个搜索结果，位置: ${A}/${F} (${(Z*100).toFixed(1)}%)`)},te=u=>{const o=u.target;Y.value=o.scrollTop,$.value=o.scrollHeight-o.clientHeight,Ct(),Ne()},Pe=()=>{const u=window.getSelection();u&&u.toString().trim()&&console.log("Selected text:",u.toString())},me=()=>{be(()=>{r.value&&($.value=r.value.scrollHeight-r.value.clientHeight)})},_e=()=>{if(t.bookId&&$.value>0){const u=Y.value/$.value;a.saveProgress(t.bookId,{scrollPosition:Y.value,progress:Math.min(u,1),timestamp:Date.now()})}};let ge=null;const Ne=()=>{ge&&clearTimeout(ge),ge=setTimeout(_e,1e3)},Ce=async()=>{if(t.bookId)try{const u=await f.getBook(t.bookId);if(!u)throw new Error("图书不存在");if(await a.loadBook(u.filePath,"txt"),a.currentReader){B.value=await a.currentReader.getFullContent(),await Ee(u.filePath,u.title);const o=await a.getProgress(t.bookId);o&&r.value&&be(()=>{r.value&&o.scrollPosition&&(r.value.scrollTop=o.scrollPosition)})}}catch(u){console.error("Failed to load book:",u),oe.error("加载图书失败")}},Ee=async(u,o)=>{try{let p=await kt(u);p.length===0&&B.value&&(p=await bt(B.value,u,o)),H.value=p,p.length>0&&console.log(`Loaded ${p.length} TOC items for ${o}`)}catch(p){console.error("Failed to load/generate TOC:",p)}},Ae=()=>{a.clearError(),Ce()},z=u=>u.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),ke=()=>{i.value?i.value=!1:(i.value=!0,w.value=!1,s.value=!1,be(()=>{c.value&&c.value.focus()}))},Oe=()=>{w.value?w.value=!1:(w.value=!0,s.value=!1,i.value=!1)},st=()=>{s.value?s.value=!1:(s.value=!0,w.value=!1,i.value=!1)},it=()=>{i.value=!1},rt=()=>{w.value=!1},ct=()=>{s.value=!1},ut=()=>{S.value?S.value=!1:(S.value=!0,D.value=!1,O.value=!1,G.value=!1,C.value=!1,s.value=!1,w.value=!1,i.value=!1)},dt=()=>{D.value?D.value=!1:(D.value=!0,S.value=!1,O.value=!1,G.value=!1,C.value=!1,s.value=!1,w.value=!1,i.value=!1)},vt=()=>{O.value?O.value=!1:(O.value=!0,S.value=!1,D.value=!1,G.value=!1,C.value=!1,s.value=!1,w.value=!1,i.value=!1)},gt=()=>{G.value?G.value=!1:(G.value=!0,S.value=!1,D.value=!1,O.value=!1,C.value=!1,s.value=!1,w.value=!1,i.value=!1)},ft=()=>{C.value?C.value=!1:(C.value=!0,S.value=!1,D.value=!1,O.value=!1,G.value=!1,s.value=!1,w.value=!1,i.value=!1)},pt=()=>{S.value=!1},ht=()=>{D.value=!1},mt=()=>{O.value=!1},_t=()=>{G.value=!1},wt=()=>{C.value=!1},Pt=u=>{const o=u.split(`
`),p=[];let A=0;const F=[{regex:/^第[一二三四五六七八九十\d]+章\s*(.*)$/i,level:1},{regex:/^第[一二三四五六七八九十\d]+节\s*(.*)$/i,level:2},{regex:/^第[一二三四五六七八九十\d]+部分\s*(.*)$/i,level:1},{regex:/^第[一二三四五六七八九十\d]+篇\s*(.*)$/i,level:1},{regex:/^Chapter\s+(\d+)\s*[:\-\s]*(.*)$/i,level:1},{regex:/^Section\s+(\d+)\s*[:\-\s]*(.*)$/i,level:2},{regex:/^Part\s+(\d+)\s*[:\-\s]*(.*)$/i,level:1},{regex:/^(\d+)\.\s*(.+)$/i,level:1},{regex:/^(\d+)\.(\d+)\s*(.+)$/i,level:2},{regex:/^(\d+)\.(\d+)\.(\d+)\s*(.+)$/i,level:3},{regex:/^[【\[]第[一二三四五六七八九十\d]+章[】\]]\s*(.*)$/i,level:1},{regex:/^[【\[]第[一二三四五六七八九十\d]+节[】\]]\s*(.*)$/i,level:2},{regex:/^[★☆]\s*(.+)$/i,level:1},{regex:/^[■□]\s*(.+)$/i,level:2}];return o.forEach((Z,ie)=>{var N;const ve=Z.trim();if(!ve){A+=Z.length+1;return}for(const fe of F){const Se=ve.match(fe.regex);if(Se){let $e="";fe.regex.source.includes("(.*)")?$e=((N=Se[Se.length-1])==null?void 0:N.trim())||ve:$e=ve,$e||($e=ve);const $t=`chapter-${p.length+1}-${ie}`;p.push({id:$t,title:$e,level:fe.level,position:A,lineNumber:ie+1});break}}A+=Z.length+1}),p},Ye=u=>{const o=Math.max(u.lastIndexOf("/"),u.lastIndexOf("\\")),p=o>=0?u.substring(0,o):"",A=o>=0?u.substring(o+1):u,F=A.lastIndexOf("."),Z=F>=0?A.substring(0,F):A;return p?`${p}/${Z}_toc.json`:`${Z}_toc.json`},kt=async u=>{try{const o=Ye(u);if(ee.value=o,await window.electronAPI.file.exists(o)){const A=await window.electronAPI.file.read(o),F=new TextDecoder("utf-8").decode(A);return JSON.parse(F).items||[]}return[]}catch(o){return console.error("Failed to load table of contents:",o),[]}},bt=async(u,o,p)=>{try{X.value=!0;const A=Pt(u);if(A.length>0){const F={bookId:t.bookId||"",bookTitle:p,generatedAt:Date.now(),items:A},Z=Ye(o);console.log(`Generated TOC for ${p}, would save to: ${Z}`),console.log(`Generated TOC with ${A.length} items for ${p}`)}return A}catch(A){return console.error("Failed to generate table of contents:",A),[]}finally{X.value=!1}},yt=u=>{if(r.value)try{const A=B.value.substring(0,u.position).split(`
`),F=parseFloat(getComputedStyle(r.value).lineHeight)||24,Z=(A.length-1)*F;r.value.scrollTop=Z,I.value=u.id,xt(u.title)}catch(o){console.error("Failed to jump to chapter:",o),oe.error("跳转失败")}},xt=u=>{const o=new RegExp(`(${z(u)})`,"gi");B.value.replace(o,'<mark class="chapter-highlight">$1</mark>'),be(()=>{setTimeout(()=>{},3e3)})},Ct=()=>{if(H.value.length===0||$.value===0)return;const u=Y.value/$.value;H.value.forEach((p,A)=>{const F=H.value[A+1],Z=F?p.position/B.value.length:1;if(u>=Z)p.progress=100;else if(u>=p.position/B.value.length){const ie=F?F.position-p.position:B.value.length-p.position,ve=(Y.value*B.value.length/$.value-p.position)/ie;p.progress=Math.max(0,Math.min(100,ve*100))}else p.progress=0});const o=H.value.find(p=>{const A=p.position/B.value.length*$.value;return Y.value>=A-100});o&&(I.value=o.id)};return Me(()=>{Ce(),be(()=>{var u;(u=r.value)==null||u.focus()})}),We(()=>{_e(),ge&&clearTimeout(ge)}),Fe(()=>ce==null?void 0:ce.value,u=>{u&&oe.error(u)}),(u,o)=>{const p=j("el-button"),A=j("el-input"),F=j("el-icon"),Z=j("el-slider"),ie=j("el-option"),ve=j("el-select");return P(),x("div",Qt,[e("div",Zt,[e("div",qt,[n(p,{type:"primary",icon:h(Te),onClick:J,size:"default"},{default:b(()=>o[4]||(o[4]=[W(" 返回图书列表 ")])),_:1,__:[4]},8,["icon"]),h(de)?(P(),x("div",Jt,[e("span",Xt,U(h(de).title),1),e("span",eo,U(g.value),1)])):q("",!0)]),e("div",to,[e("div",oo,[n(p,{icon:h(Ke),onClick:ut,size:"small",title:"AI学习导引 - 让专家在您身旁导引您，让您学习更加轻松",type:S.value?"primary":"default"},{default:b(()=>o[5]||(o[5]=[W(" AI学习导引 ")])),_:1,__:[5]},8,["icon","type"]),n(p,{icon:h(Qe),onClick:vt,size:"small",title:"AI学习对话 - 让每一次问答都成为思维的跳板，跃向更深邃的理解",type:O.value?"primary":"default"},{default:b(()=>o[6]||(o[6]=[W(" AI学习对话 ")])),_:1,__:[6]},8,["icon","type"]),n(p,{icon:h(Ze),onClick:gt,size:"small",title:"AI学习探究 - 让每一次追问都点亮新的知识星空",type:G.value?"primary":"default"},{default:b(()=>o[7]||(o[7]=[W(" AI学习探究 ")])),_:1,__:[7]},8,["icon","type"]),n(p,{icon:h(qe),onClick:ft,size:"small",title:"AI评测 - 智能评估学习效果，提供个性化学习建议",type:C.value?"primary":"default"},{default:b(()=>o[8]||(o[8]=[W(" AI评测 ")])),_:1,__:[8]},8,["icon","type"]),n(p,{icon:h(Je),onClick:dt,size:"small",title:"编辑器 - 提供文本编辑功能",type:D.value?"primary":"default"},{default:b(()=>o[9]||(o[9]=[W(" 笔记编辑器 ")])),_:1,__:[9]},8,["icon","type"])])]),e("div",no,[n(p,{icon:h(ye),onClick:ke,size:"small",title:"搜索文本",type:i.value?"primary":"default"},null,8,["icon","type"]),n(p,{icon:h(St),onClick:Oe,size:"small",title:"目录导航",type:w.value?"primary":"default"},null,8,["icon","type"]),n(p,{icon:h(tt),onClick:st,size:"small",title:"阅读设置",type:s.value?"primary":"default"},null,8,["icon","type"])])]),e("div",lo,[e("div",{class:Ie(["left-panel",{"panel-visible":m.value}])},[i.value?(P(),x("div",ao,[e("div",so,[o[10]||(o[10]=e("h3",null,"搜索文本",-1)),n(p,{link:"",onClick:it,icon:h(pe),size:"small"},null,8,["icon"])]),e("div",io,[e("div",ro,[n(A,{ref_key:"searchInputRef",ref:c,modelValue:V.value,"onUpdate:modelValue":o[0]||(o[0]=N=>V.value=N),placeholder:"搜索文本...",onKeyup:ot(ue,["enter"]),onInput:le,clearable:"",size:"small"},{append:b(()=>[n(p,{onClick:ue,icon:h(ye)},null,8,["icon"])]),_:1},8,["modelValue"])]),k.value.length>0?(P(),x("div",co,[e("span",null,"找到 "+U(k.value.length)+" 个结果",1),e("div",uo,[n(p,{size:"small",disabled:y.value<=0,onClick:xe,icon:h(zt)},null,8,["disabled","icon"]),e("span",vo,U(y.value+1)+" / "+U(k.value.length),1),n(p,{size:"small",disabled:y.value>=k.value.length-1,onClick:E,icon:h(Ft)},null,8,["disabled","icon"]),n(p,{onClick:d,size:"small",icon:h(pe),title:"清除搜索"},null,8,["icon"])])])):q("",!0),k.value.length>0?(P(),x("div",go,[(P(!0),x(Ue,null,Be(k.value.slice(0,20),(N,fe)=>(P(),x("div",{key:fe,class:Ie(["search-result-item",{active:fe===y.value}]),onClick:Se=>se(fe)},[e("span",po,U(fe+1),1),e("span",ho,U(Q(N)),1)],10,fo))),128)),k.value.length>20?(P(),x("div",mo," 还有 "+U(k.value.length-20)+" 个结果... ",1)):q("",!0)])):V.value&&k.value.length===0?(P(),x("div",_o,[n(F,null,{default:b(()=>[n(h(ye))]),_:1}),o[11]||(o[11]=e("span",null,"未找到匹配的内容",-1))])):(P(),x("div",wo,[n(F,null,{default:b(()=>[n(h(ye))]),_:1}),o[12]||(o[12]=e("span",null,"输入关键词开始搜索",-1))]))])])):q("",!0),w.value?(P(),x("div",Po,[e("div",ko,[o[13]||(o[13]=e("h3",null,"目录",-1)),n(p,{link:"",onClick:rt,icon:h(pe),size:"small"},null,8,["icon"])]),e("div",bo,[X.value?(P(),x("div",yo,[n(F,{class:"loading-icon"},{default:b(()=>[n(h(De))]),_:1}),o[14]||(o[14]=e("span",null,"正在生成目录...",-1))])):H.value.length===0?(P(),x("div",xo,[n(F,null,{default:b(()=>[n(h(nt))]),_:1}),o[15]||(o[15]=e("span",null,"未找到目录结构",-1))])):(P(),x("div",Co,[(P(!0),x(Ue,null,Be(H.value,N=>(P(),x("div",{key:N.id,class:Ie(["toc-item",{"toc-level-1":N.level===1,"toc-level-2":N.level===2,"toc-level-3":N.level===3,active:I.value===N.id}]),onClick:fe=>yt(N)},[e("span",Io,U(N.title),1),N.progress!==void 0?(P(),x("span",Ro,U(Math.round(N.progress))+"%",1)):q("",!0)],10,$o))),128))]))])])):q("",!0),s.value?(P(),x("div",To,[e("div",Eo,[o[16]||(o[16]=e("h3",null,"阅读设置",-1)),n(p,{link:"",onClick:ct,icon:h(pe),size:"small"},null,8,["icon"])]),e("div",Ao,[e("div",So,[o[17]||(o[17]=e("label",null,"字体大小",-1)),n(Z,{modelValue:L.value.font.size,"onUpdate:modelValue":o[1]||(o[1]=N=>L.value.font.size=N),min:12,max:32,step:1,"show-input":"",onChange:me},null,8,["modelValue"])]),e("div",zo,[o[18]||(o[18]=e("label",null,"行间距",-1)),n(Z,{modelValue:L.value.font.lineHeight,"onUpdate:modelValue":o[2]||(o[2]=N=>L.value.font.lineHeight=N),min:1,max:3,step:.1,"show-input":"",onChange:me},null,8,["modelValue"])]),e("div",Fo,[o[19]||(o[19]=e("label",null,"字体族",-1)),n(ve,{modelValue:L.value.font.family,"onUpdate:modelValue":o[3]||(o[3]=N=>L.value.font.family=N),onChange:me,style:{width:"100%"}},{default:b(()=>[n(ie,{label:"微软雅黑",value:"Microsoft YaHei"}),n(ie,{label:"宋体",value:"SimSun"}),n(ie,{label:"黑体",value:"SimHei"}),n(ie,{label:"楷体",value:"KaiTi"})]),_:1},8,["modelValue"])]),e("div",Uo,[o[20]||(o[20]=e("label",null,"主题模式",-1)),o[21]||(o[21]=e("p",{class:"setting-note"},"主题设置已移至全局设置，请使用侧边栏或菜单栏的主题切换功能",-1)),e("p",Bo,"当前主题: "+U(R.value),1)])])])):q("",!0),S.value?(P(),x("div",Do,[e("div",Vo,[o[22]||(o[22]=e("h3",null,"AI学习导引",-1)),n(p,{link:"",onClick:pt,icon:h(pe),size:"small"},null,8,["icon"])]),e("div",Lo,[e("div",Mo,[n(F,null,{default:b(()=>[n(h(Ke))]),_:1}),o[23]||(o[23]=e("p",null,"让专家在您身旁导引您，让您学习更加轻松",-1))]),o[24]||(o[24]=e("div",{class:"ai-panel-placeholder"},[e("div",{class:"placeholder-content"},[e("h4",null,"AI学习导引功能"),e("p",null,"这里将显示AI学习导引的内容，包括："),e("ul",null,[e("li",null,"智能学习路径推荐"),e("li",null,"个性化学习建议"),e("li",null,"学习进度跟踪"),e("li",null,"专家级指导意见")])])],-1))])])):q("",!0),D.value?(P(),x("div",Ho,[e("div",No,[o[25]||(o[25]=e("h3",null,"编辑器",-1)),n(p,{link:"",onClick:ht,icon:h(pe),size:"small"},null,8,["icon"])]),e("div",Oo,[e("div",Go,[n(F,null,{default:b(()=>[n(h(Je))]),_:1}),o[26]||(o[26]=e("p",null,"提供文本编辑功能",-1))]),o[27]||(o[27]=e("div",{class:"ai-panel-placeholder"},[e("div",{class:"placeholder-content"},[e("h4",null,"编辑器功能"),e("p",null,"这里将显示编辑器的内容，包括："),e("ul",null,[e("li",null,"文本编辑和格式化"),e("li",null,"笔记记录和整理"),e("li",null,"内容标注和高亮"),e("li",null,"文档导出和分享")])])],-1))])])):q("",!0),O.value?(P(),x("div",Wo,[e("div",jo,[o[28]||(o[28]=e("h3",null,"AI学习对话",-1)),n(p,{link:"",onClick:mt,icon:h(pe),size:"small"},null,8,["icon"])]),e("div",Yo,[e("div",Ko,[n(F,null,{default:b(()=>[n(h(Qe))]),_:1}),o[29]||(o[29]=e("p",null,"让每一次问答都成为思维的跳板，跃向更深邃的理解",-1))]),o[30]||(o[30]=e("div",{class:"ai-panel-placeholder"},[e("div",{class:"placeholder-content"},[e("h4",null,"AI学习对话功能"),e("p",null,"这里将显示AI对话的内容，包括："),e("ul",null,[e("li",null,"智能问答交互"),e("li",null,"学习疑问解答"),e("li",null,"知识点深度探讨"),e("li",null,"个性化学习建议")])])],-1))])])):q("",!0),G.value?(P(),x("div",Qo,[e("div",Zo,[o[31]||(o[31]=e("h3",null,"AI学习探究",-1)),n(p,{link:"",onClick:_t,icon:h(pe),size:"small"},null,8,["icon"])]),e("div",qo,[e("div",Jo,[n(F,null,{default:b(()=>[n(h(Ze))]),_:1}),o[32]||(o[32]=e("p",null,"让每一次追问都点亮新的知识星空",-1))]),o[33]||(o[33]=e("div",{class:"ai-panel-placeholder"},[e("div",{class:"placeholder-content"},[e("h4",null,"AI学习探究功能"),e("p",null,"这里将显示AI探究的内容，包括："),e("ul",null,[e("li",null,"深度知识挖掘"),e("li",null,"关联概念探索"),e("li",null,"学习路径拓展"),e("li",null,"创新思维启发")])])],-1))])])):q("",!0),C.value?(P(),x("div",Xo,[e("div",en,[o[34]||(o[34]=e("h3",null,"AI评测",-1)),n(p,{link:"",onClick:wt,icon:h(pe),size:"small"},null,8,["icon"])]),e("div",tn,[e("div",on,[n(F,null,{default:b(()=>[n(h(qe))]),_:1}),o[35]||(o[35]=e("p",null,"智能评估学习效果，提供个性化学习建议",-1))]),o[36]||(o[36]=Ut('<div class="ai-panel-placeholder" data-v-02a52042><div class="placeholder-content" data-v-02a52042><h4 data-v-02a52042>AI评测功能</h4><p data-v-02a52042>这里将显示AI评测的内容，包括：</p><ul data-v-02a52042><li data-v-02a52042>学习效果智能评估</li><li data-v-02a52042>知识掌握程度分析</li><li data-v-02a52042>个性化学习建议</li><li data-v-02a52042>学习进度跟踪报告</li><li data-v-02a52042>薄弱环节识别</li><li data-v-02a52042>学习路径优化建议</li></ul><div class="evaluation-preview" data-v-02a52042><h5 data-v-02a52042>评测维度</h5><div class="evaluation-metrics" data-v-02a52042><div class="metric-item" data-v-02a52042><span class="metric-label" data-v-02a52042>理解深度</span><div class="metric-bar" data-v-02a52042><div class="metric-progress" style="width:75%;" data-v-02a52042></div></div><span class="metric-value" data-v-02a52042>75%</span></div><div class="metric-item" data-v-02a52042><span class="metric-label" data-v-02a52042>知识关联</span><div class="metric-bar" data-v-02a52042><div class="metric-progress" style="width:60%;" data-v-02a52042></div></div><span class="metric-value" data-v-02a52042>60%</span></div><div class="metric-item" data-v-02a52042><span class="metric-label" data-v-02a52042>应用能力</span><div class="metric-bar" data-v-02a52042><div class="metric-progress" style="width:80%;" data-v-02a52042></div></div><span class="metric-value" data-v-02a52042>80%</span></div></div></div></div></div>',1))])])):q("",!0)],2),e("div",{class:Ie(["reader-main",{"with-left-panel":m.value}])},[e("div",{class:"reader-content",style:lt(K.value),ref_key:"contentRef",ref:r,onScroll:te,tabindex:"0"},[e("div",{class:"text-content",innerHTML:T.value,onMouseup:Pe},null,40,nn)],36)],2)]),h(re)?(P(),x("div",ln,[n(F,{class:"loading-icon",size:40},{default:b(()=>[n(h(De))]),_:1}),o[37]||(o[37]=e("div",{class:"loading-text"},"正在加载文档...",-1))])):q("",!0),h(ce)?(P(),x("div",an,[n(F,{class:"error-icon"},{default:b(()=>[n(h(Ve))]),_:1}),e("div",sn,U(h(ce)),1),n(p,{type:"primary",onClick:Ae},{default:b(()=>o[38]||(o[38]=[W("重试")])),_:1,__:[38]})])):q("",!0)])}}}),cn=He(rn,[["__scopeId","data-v-02a52042"]]),un={class:"pdf-reader-view"},dn={class:"pdf-toolbar"},vn={class:"toolbar-left"},gn={class:"book-title"},fn={class:"toolbar-center"},pn={class:"page-info"},hn={class:"toolbar-right"},mn={key:0,class:"pdf-container"},_n=["src"],wn={key:1,class:"loading-container"},Pn={key:0,class:"loading-progress"},kn={class:"progress-text"},bn={key:2,class:"error-container"},yn={class:"error-actions"},xn={key:0,class:"error-details"},Cn=Le({__name:"PdfReaderViewNew",props:{bookId:{},bookInfo:{}},emits:["go-back"],setup(_,{emit:t}){const l=_,a=t,f=je(),r=v(!0),c=v(""),s=v(""),i=v(""),w=v(0),S=v(1),D=v(0),O=v(1),G=v(1),C=v(l.bookInfo||null),V=v();ne(()=>D.value===0?"加载中...":`${Math.round(S.value/D.value*100)}% (${S.value}/${D.value})`);const k=()=>{a("go-back"),f.push("/bookshelf/library")},y=()=>{oe.info("请使用PDF查看器内置的导航功能")},B=()=>{oe.info("请使用PDF查看器内置的导航功能")},Y=R=>{oe.info("请使用PDF查看器内置的导航功能")},$=()=>{oe.info("请使用PDF查看器内置的缩放功能")},H=()=>{oe.info("请使用PDF查看器内置的缩放功能")},X=()=>{oe.info("请使用PDF查看器内置的缩放功能")},I=()=>{var R;document.fullscreenElement?document.exitFullscreen():(R=V.value)==null||R.requestFullscreen()},ee=async()=>{var R,m,K;if(!((R=C.value)!=null&&R.filePath)){console.error("文件路径不存在");return}try{console.log("尝试使用外部查看器打开PDF:",C.value.filePath),(K=(m=window.electronAPI)==null?void 0:m.shell)!=null&&K.openPath?(await window.electronAPI.shell.openPath(C.value.filePath),console.log("已使用外部查看器打开PDF文件"),oe.success("已使用系统默认PDF查看器打开文件")):(console.error("Electron shell API不可用"),oe.error("无法打开外部查看器"))}catch(g){console.error("打开外部查看器失败:",g),oe.error("打开外部查看器失败")}},L=async R=>{console.log("开始加载PDF文件:",R);try{const m=await window.electronAPI.file.readPdf(R);if(!m.success)throw new Error(m.error||"获取文件数据失败");return console.log("成功获取PDF数据，大小:",m.size,"字节"),m.data}catch(m){throw console.error("加载PDF文件失败:",m),m}},M=R=>{console.log("PDF iframe加载完成",R),r.value=!1,w.value=100,console.log("PDF加载完成，准备显示")},de=R=>{console.error("PDF iframe加载失败",R),c.value="PDF文档加载失败",s.value=`iframe加载错误: ${R.type}`,r.value=!1,w.value=0},re=async()=>{var R;if(!((R=C.value)!=null&&R.filePath)){c.value="书籍文件路径不存在",r.value=!1;return}try{r.value=!0,c.value="",s.value="",w.value=10,console.log("开始加载PDF文档，书籍ID:",l.bookId);const m=await L(C.value.filePath);console.log("PDF数据类型:",typeof m,"是否为ArrayBuffer:",m instanceof ArrayBuffer),w.value=30;const K=5*1024*1024,g=m.byteLength/1024/1024,T=m.byteLength>K;if(console.log(`PDF文件大小: ${g.toFixed(2)}MB (${m.byteLength} 字节), 是否为大文件: ${T}`),T)throw new Error(`PDF文件过大 (${g.toFixed(1)}MB)。

建议：
1. 使用系统默认PDF查看器打开
2. 或者使用较小的PDF文件（建议小于5MB）`);console.log("使用blob URL方案显示小PDF文件");try{const J=new Blob([m],{type:"application/pdf"}),ue=URL.createObjectURL(J);i.value=ue,console.log("PDF Blob URL创建成功:",ue),w.value=95}catch(J){throw console.error("创建PDF Blob URL失败:",J),new Error(`PDF显示失败: ${J.message}`)}D.value=1,S.value=1}catch(m){console.error("加载PDF文档失败:",m),c.value=m instanceof Error?m.message:"未知错误",s.value=m instanceof Error?m.stack||m.toString():String(m),r.value=!1,w.value=0}},ce=async()=>{try{if(console.log("初始化PDF阅读器，书籍ID:",l.bookId),l.bookInfo)C.value=l.bookInfo,console.log("使用传入的书籍信息:",C.value);else{console.log("书籍信息未传入，等待父组件提供");return}await re()}catch(R){console.error("初始化PDF阅读器失败:",R),c.value=R instanceof Error?R.message:"初始化失败",r.value=!1}};return Fe(S,R=>{G.value=R}),Fe(()=>l.bookInfo,async R=>{R&&(C.value=R,console.log("接收到新的书籍信息:",R),await re())},{immediate:!0}),Me(async()=>{console.log("PDF阅读器组件已挂载"),await ce()}),We(()=>{console.log("PDF阅读器组件卸载"),i.value&&i.value.startsWith("blob:")&&(URL.revokeObjectURL(i.value),console.log("已清理PDF Blob URL"))}),(R,m)=>{var le,he;const K=j("el-button"),g=j("el-button-group"),T=j("el-input-number"),J=j("el-icon"),ue=j("el-progress");return P(),x("div",un,[e("div",dn,[e("div",vn,[n(K,{onClick:k,icon:h(Te),circle:""},null,8,["icon"]),e("span",gn,U(((le=C.value)==null?void 0:le.title)||"加载中..."),1)]),e("div",fn,[n(g,null,{default:b(()=>[n(K,{onClick:y,disabled:S.value<=1,icon:h(Te)},{default:b(()=>m[1]||(m[1]=[W(" 上一页 ")])),_:1,__:[1]},8,["disabled","icon"]),n(K,{onClick:B,disabled:S.value>=D.value,icon:h(at)},{default:b(()=>m[2]||(m[2]=[W(" 下一页 ")])),_:1,__:[2]},8,["disabled","icon"])]),_:1}),e("div",pn,[e("span",null,"第 "+U(S.value)+" 页 / 共 "+U(D.value)+" 页",1)]),n(T,{modelValue:G.value,"onUpdate:modelValue":m[0]||(m[0]=xe=>G.value=xe),min:1,max:Math.max(1,D.value),size:"small",style:{width:"80px",margin:"0 10px"},onChange:Y},null,8,["modelValue","max"])]),e("div",hn,[n(g,null,{default:b(()=>[n(K,{onClick:H,icon:h(Bt)},{default:b(()=>m[3]||(m[3]=[W("缩小")])),_:1,__:[3]},8,["icon"]),n(K,{onClick:X},{default:b(()=>[W(U(Math.round(O.value*100))+"%",1)]),_:1}),n(K,{onClick:$,icon:h(Dt)},{default:b(()=>m[4]||(m[4]=[W("放大")])),_:1,__:[4]},8,["icon"])]),_:1}),n(K,{onClick:I,icon:h(Vt)},{default:b(()=>m[5]||(m[5]=[W("全屏")])),_:1,__:[5]},8,["icon"])])]),e("div",{class:"pdf-content",ref_key:"pdfContentRef",ref:V},[i.value?(P(),x("div",mn,[e("iframe",{src:i.value,class:"pdf-iframe",frameborder:"0",onLoad:M,onError:de,sandbox:"allow-same-origin allow-scripts"},null,40,_n)])):r.value?(P(),x("div",wn,[n(J,{class:"is-loading"},{default:b(()=>[n(h(De))]),_:1}),m[6]||(m[6]=e("span",null,"正在加载PDF文档...",-1)),w.value>0?(P(),x("div",Pn,[n(ue,{percentage:w.value,"show-text":!1},null,8,["percentage"]),e("span",kn,U(w.value)+"%",1)])):q("",!0)])):(P(),x("div",bn,[n(J,null,{default:b(()=>[n(h(Ve))]),_:1}),e("span",null,"PDF文档加载失败: "+U(c.value),1),e("div",yn,[n(K,{onClick:re,type:"primary",size:"small"},{default:b(()=>m[7]||(m[7]=[W("重新加载")])),_:1,__:[7]}),(he=C.value)!=null&&he.filePath?(P(),Re(K,{key:0,onClick:ee,type:"success",size:"small"},{default:b(()=>m[8]||(m[8]=[W(" 使用外部查看器打开 ")])),_:1,__:[8]})):q("",!0)]),s.value?(P(),x("div",xn,[e("details",null,[m[9]||(m[9]=e("summary",null,"错误详情",-1)),e("pre",null,U(s.value),1)])])):q("",!0)]))],512)])}}}),$n=He(Cn,[["__scopeId","data-v-bf452a55"]]),In={class:"epub-reader-container"},Rn={key:0,class:"loading-container"},Tn={class:"loading-content"},En={class:"loading-text"},An={key:0,class:"error-text"},Sn={class:"loading-actions"},zn={key:1,class:"reader-main"},Fn={class:"reader-toolbar"},Un={class:"toolbar-left"},Bn={class:"book-title"},Dn={class:"toolbar-center"},Vn={class:"chapter-progress-info"},Ln={class:"chapter-info"},Mn={class:"reading-progress"},Hn={class:"toolbar-right"},Nn={class:"reader-content",ref:"contentContainer"},On={class:"chapter-container"},Gn={class:"chapter-title"},Wn=["innerHTML"],jn={class:"toc-container"},Yn=["onClick"],Kn={class:"toc-title"},Qn={class:"toc-level"},Zn={class:"search-container"},qn={key:0,class:"search-results"},Jn={class:"results-count"},Xn=["onClick"],el={class:"result-context"},tl={class:"result-info"},ol={key:1,class:"no-results"},nl={class:"settings-container"},ll={class:"setting-group"},al={class:"setting-item"},sl={class:"setting-item"},il={class:"setting-group"},rl={class:"setting-group"},cl={class:"setting-item"},ul=Le({__name:"EpubReaderView",props:{bookId:{}},emits:["go-back"],setup(_,{emit:t}){const l=_,a=t,f=v(!0),r=v(!1),c=v(""),s=v(0),i=v("正在加载..."),w=v(""),S=v(null),D=v(null),O=v([]),G=v(null),C=v(0),V=v(0),k=v(0),y=v(!1),B=v(!1),Y=v(!1),$=v(""),H=v([]),X=v(!1),I=v({fontSize:16,lineHeight:1.6,theme:"light",maxWidth:800,fontFamily:"system-ui, -apple-system, sans-serif"}),ee=ne(()=>({fontSize:`${I.value.fontSize}px`,lineHeight:I.value.lineHeight,maxWidth:`${I.value.maxWidth}px`,fontFamily:I.value.fontFamily,margin:"0 auto",padding:"20px",backgroundColor:L().background,color:L().text}));function L(){return{light:{background:"#ffffff",text:"#333333"},dark:{background:"#1a1a1a",text:"#e0e0e0"},sepia:{background:"#f7f3e9",text:"#5c4b37"}}[I.value.theme]}Me(async()=>{await M()}),We(()=>{xe()});async function M(){try{if(f.value=!0,c.value="",s.value=0,i.value="正在初始化...",console.log(`EpubReaderView: 开始加载书籍 ${l.bookId}`),await de(),s.value=10,i.value="获取书籍信息...",S.value=await window.electronAPI.reader.getBook(l.bookId),!S.value)throw new Error("未找到指定的书籍");s.value=30,i.value="准备EPUB阅读器...";const E=S.value.file_path||S.value.filePath;if(!E)throw new Error(`书籍文件路径无效: ${E}`);const d=`epub-reader-${l.bookId}-${Date.now()}`;w.value=d,s.value=50,i.value="创建阅读器实例...";const se={enableImageLoading:!0,enableStyleProcessing:!0,enableToc:!0,enableSearch:!0},Q=await window.electronAPI.epubReader.createReader(d,E,se);if(!(Q!=null&&Q.success))throw new Error((Q==null?void 0:Q.error)||"创建阅读器失败");s.value=70,i.value="加载目录信息...";const ae=await window.electronAPI.epubReader.getToc(d);if(ae.success){const te=ae.toc||ae.chapters||[];te.length>0&&te[0].children?O.value=te[0].children:O.value=te,D.value=ae.bookInfo,V.value=O.value.length,console.log(`EpubReaderView: 加载目录完成，章节数: ${V.value}`),console.log("EpubReaderView: 目录项:",O.value)}s.value=90,i.value="加载第一章...",V.value>0&&await re(0),s.value=100,i.value="加载完成",await new Promise(te=>setTimeout(te,500)),f.value=!1,r.value=!0,console.log("EpubReaderView: EPUB书籍加载完成")}catch(E){console.error("EpubReaderView: 加载失败:",E),c.value=E instanceof Error?E.message:"加载失败",i.value="加载失败"}}async function de(E=10,d=100){var se,Q,ae,te;for(let Pe=0;Pe<E;Pe++){if((Q=(se=window.electronAPI)==null?void 0:se.reader)!=null&&Q.getBook&&((te=(ae=window.electronAPI)==null?void 0:ae.epubReader)!=null&&te.createReader))return;await new Promise(me=>setTimeout(me,d))}throw new Error("EPUB阅读器API初始化超时，请稍后重试")}async function re(E){try{if(!w.value)throw new Error("阅读器未初始化");const d=await window.electronAPI.epubReader.getChapter(w.value,E);if(d!=null&&d.success&&d.chapter){G.value=d.chapter,C.value=E,k.value=V.value>0?(E+1)/V.value*100:0,await be();const se=document.querySelector(".reader-content");se&&(se.scrollTop=0),await ue()}else throw new Error((d==null?void 0:d.error)||"获取章节内容失败")}catch(d){console.error("加载章节失败:",d),oe.error("加载章节失败")}}async function ce(){C.value>0&&await re(C.value-1)}async function R(){C.value<V.value-1&&await re(C.value+1)}async function m(E){E>=0&&E<V.value&&(await re(E),y.value=!1)}async function K(){if(!$.value.trim()||!w.value){H.value=[];return}try{X.value=!0,console.log("EpubReaderView: 执行搜索:",$.value.trim());const E=await window.electronAPI.epubReader.search(w.value,$.value.trim());H.value=E||[],H.value.length===0&&oe.info("未找到匹配的内容")}catch(E){console.error("搜索失败:",E),oe.error("搜索失败")}finally{X.value=!1}}async function g(E){await m(E.chapterIndex),B.value=!1}function T(){}function J(E){E.target.tagName==="A"&&E.preventDefault()}async function ue(){var E,d;if((d=(E=window.electronAPI)==null?void 0:E.reader)!=null&&d.updateProgress&&S.value)try{await window.electronAPI.reader.updateProgress(l.bookId,k.value,C.value+1)}catch(se){console.warn("保存阅读进度失败:",se)}}async function le(){c.value="",await M()}function he(){a("go-back")}function xe(){w.value&&window.electronAPI.epubReader.destroyReader(w.value).catch(E=>console.warn("清理阅读器失败:",E))}return(E,d)=>{var Ce,Ee,Ae;const se=j("el-progress"),Q=j("el-button"),ae=j("el-card"),te=j("el-drawer"),Pe=j("el-input"),me=j("el-empty"),_e=j("el-slider"),ge=j("el-radio"),Ne=j("el-radio-group");return P(),x("div",In,[f.value?(P(),x("div",Rn,[n(ae,{class:"loading-card"},{default:b(()=>[e("div",Tn,[n(se,{percentage:s.value,status:c.value?"exception":"success","stroke-width":8},null,8,["percentage","status"]),e("p",En,U(i.value),1),c.value?(P(),x("p",An,U(c.value),1)):q("",!0),e("div",Sn,[n(Q,{onClick:he},{default:b(()=>d[11]||(d[11]=[W("返回书库")])),_:1,__:[11]}),c.value?(P(),Re(Q,{key:0,onClick:le,type:"primary"},{default:b(()=>d[12]||(d[12]=[W("重试")])),_:1,__:[12]})):q("",!0)])])]),_:1})])):r.value?(P(),x("div",zn,[e("div",Fn,[e("div",Un,[n(Q,{onClick:he,icon:h(Te),circle:""},null,8,["icon"]),e("span",Bn,U(((Ce=D.value)==null?void 0:Ce.title)||"未知书籍"),1)]),e("div",Dn,[n(Q,{onClick:ce,disabled:C.value<=0,icon:h(Te),circle:"",size:"small",title:"上一章"},null,8,["disabled","icon"]),e("div",Vn,[e("span",Ln," 第 "+U(C.value+1)+" 章 / 共 "+U(V.value)+" 章 ",1),e("span",Mn,U(Math.round(k.value))+"%",1)]),n(Q,{onClick:R,disabled:C.value>=V.value-1,icon:h(at),circle:"",size:"small",title:"下一章"},null,8,["disabled","icon"])]),e("div",Hn,[n(Q,{onClick:d[0]||(d[0]=z=>y.value=!0),icon:h(Lt),circle:"",title:"目录"},null,8,["icon"]),n(Q,{onClick:d[1]||(d[1]=z=>B.value=!0),icon:h(ye),circle:"",title:"搜索"},null,8,["icon"]),n(Q,{onClick:d[2]||(d[2]=z=>Y.value=!0),icon:h(tt),circle:"",title:"设置"},null,8,["icon"])])]),e("div",Nn,[e("div",On,[e("h2",Gn,U((Ee=G.value)==null?void 0:Ee.title),1),e("div",{class:"chapter-content",style:lt(ee.value),innerHTML:(Ae=G.value)==null?void 0:Ae.content,onClick:J},null,12,Wn)])],512)])):q("",!0),n(te,{modelValue:y.value,"onUpdate:modelValue":d[3]||(d[3]=z=>y.value=z),title:"目录",direction:"ltr",size:"400px"},{default:b(()=>[e("div",jn,[(P(!0),x(Ue,null,Be(O.value,(z,ke)=>(P(),x("div",{key:z.id,class:Ie(["toc-item",{active:(z.chapterIndex??ke)===C.value}]),onClick:Oe=>m(z.chapterIndex??ke)},[e("span",Kn,U(z.title),1),e("span",Qn,U(z.level),1)],10,Yn))),128))])]),_:1},8,["modelValue"]),n(te,{modelValue:B.value,"onUpdate:modelValue":d[5]||(d[5]=z=>B.value=z),title:"搜索",direction:"rtl",size:"400px"},{default:b(()=>[e("div",Zn,[n(Pe,{modelValue:$.value,"onUpdate:modelValue":d[4]||(d[4]=z=>$.value=z),placeholder:"输入搜索关键词",onKeyup:ot(K,["enter"]),clearable:""},{append:b(()=>[n(Q,{onClick:K,icon:h(ye)},null,8,["icon"])]),_:1},8,["modelValue"]),H.value.length>0?(P(),x("div",qn,[e("p",Jn,"找到 "+U(H.value.length)+" 个结果",1),(P(!0),x(Ue,null,Be(H.value,(z,ke)=>(P(),x("div",{key:ke,class:"search-result-item",onClick:Oe=>g(z)},[e("div",el,U(z.context),1),e("div",tl," 第"+U(z.chapterIndex+1)+"章 - "+U(z.chapterTitle),1)],8,Xn))),128))])):$.value&&!X.value?(P(),x("div",ol,[n(me,{description:"未找到匹配的内容"})])):q("",!0)])]),_:1},8,["modelValue"]),n(te,{modelValue:Y.value,"onUpdate:modelValue":d[10]||(d[10]=z=>Y.value=z),title:"阅读设置",direction:"rtl",size:"350px"},{default:b(()=>[e("div",nl,[e("div",ll,[d[15]||(d[15]=e("h4",null,"字体设置",-1)),e("div",al,[d[13]||(d[13]=e("label",null,"字体大小",-1)),n(_e,{modelValue:I.value.fontSize,"onUpdate:modelValue":d[6]||(d[6]=z=>I.value.fontSize=z),min:12,max:24,step:1,onChange:T},null,8,["modelValue"])]),e("div",sl,[d[14]||(d[14]=e("label",null,"行高",-1)),n(_e,{modelValue:I.value.lineHeight,"onUpdate:modelValue":d[7]||(d[7]=z=>I.value.lineHeight=z),min:1.2,max:2,step:.1,onChange:T},null,8,["modelValue"])])]),e("div",il,[d[19]||(d[19]=e("h4",null,"主题设置",-1)),n(Ne,{modelValue:I.value.theme,"onUpdate:modelValue":d[8]||(d[8]=z=>I.value.theme=z),onChange:T},{default:b(()=>[n(ge,{value:"light"},{default:b(()=>d[16]||(d[16]=[W("浅色")])),_:1,__:[16]}),n(ge,{value:"dark"},{default:b(()=>d[17]||(d[17]=[W("深色")])),_:1,__:[17]}),n(ge,{value:"sepia"},{default:b(()=>d[18]||(d[18]=[W("护眼")])),_:1,__:[18]})]),_:1},8,["modelValue"])]),e("div",rl,[d[21]||(d[21]=e("h4",null,"布局设置",-1)),e("div",cl,[d[20]||(d[20]=e("label",null,"最大宽度",-1)),n(_e,{modelValue:I.value.maxWidth,"onUpdate:modelValue":d[9]||(d[9]=z=>I.value.maxWidth=z),min:600,max:1200,step:50,onChange:T},null,8,["modelValue"])])])])]),_:1},8,["modelValue"])])}}}),dl=He(ul,[["__scopeId","data-v-34a525ba"]]),vl={class:"unified-reader"},gl={key:0,class:"loading-container"},fl={key:1,class:"error-container"},pl={key:5,class:"format-placeholder"},hl={key:6,class:"unsupported-format"},ml=Le({__name:"UnifiedReader",props:{bookId:{}},setup(_){const t=_,l=je(),a=v(!0),f=v(""),r=v(null),c=v(""),s=v(""),i=v(1),w=ne(()=>["txt","pdf","epub","mobi"]),S=async()=>{try{a.value=!0,f.value="",console.log(`UnifiedReader: 加载书籍信息 ${t.bookId}`);const k=async(I=100,ee=100)=>{console.log("UnifiedReader: 开始检查Electron环境...");for(let L=0;L<I;L++){if(typeof window>"u"){console.log(`UnifiedReader: 等待window对象... (${L+1}/${I})`),await new Promise(M=>setTimeout(M,ee));continue}if(!window.electronAPI){console.log(`UnifiedReader: 等待electronAPI对象... (${L+1}/${I})`),await new Promise(M=>setTimeout(M,ee));continue}return console.log("UnifiedReader: Electron环境检查通过"),!0}return console.error("UnifiedReader: Electron环境检查超时"),!1},y=async(I=50,ee=200)=>{console.log("UnifiedReader: 开始检查API可用性...");for(let L=0;L<I;L++)try{if(!window.electronAPI){console.log(`UnifiedReader: electronAPI对象不存在 (${L+1}/${I})`),await new Promise(M=>setTimeout(M,ee));continue}if(!window.electronAPI.reader){console.log(`UnifiedReader: reader API不存在 (${L+1}/${I})`),await new Promise(M=>setTimeout(M,ee));continue}if(typeof window.electronAPI.reader.getBook!="function"){console.log(`UnifiedReader: getBook方法不是函数 (${L+1}/${I})`),await new Promise(M=>setTimeout(M,ee));continue}return console.log("UnifiedReader: API可用性检查通过"),!0}catch(M){console.warn(`UnifiedReader: API检查异常 (${L+1}/${I}):`,M),await new Promise(de=>setTimeout(de,ee))}return console.error("UnifiedReader: API可用性检查超时"),!1};if(!await k())throw new Error("Electron环境未就绪，请确保应用在Electron环境中运行");if(!await y())throw new Error("阅读器API初始化超时，请稍后重试或重启应用");console.log("UnifiedReader: 开始调用阅读器API获取书籍信息");let $=null,H=0;const X=3;for(;H<X&&!$;)try{if(console.log(`UnifiedReader: API调用尝试 ${H+1}/${X}`),$=await window.electronAPI.reader.getBook(t.bookId),$){console.log("UnifiedReader: 成功获取书籍信息");break}else console.warn(`UnifiedReader: API返回空结果 (尝试 ${H+1}/${X})`)}catch(I){if(H++,console.error(`UnifiedReader: API调用失败 (尝试 ${H}/${X}):`,I),H<X)console.log("UnifiedReader: 将在1秒后重试..."),await new Promise(ee=>setTimeout(ee,1e3));else throw new Error(`API调用失败: ${I.message||I}`)}if(!$)throw new Error("书籍不存在或无法获取书籍信息");if(!$.id||!$.title)throw new Error("书籍数据不完整，缺少必要字段");r.value=$,c.value=$.file_format||$.format||"txt",s.value=$.file_path||$.filePath||"",$.current_page&&(i.value=$.current_page),console.log("UnifiedReader: 书籍信息加载成功",{id:$.id,title:$.title,format:c.value,filePath:s.value,initialPage:i.value}),w.value.includes(c.value)||console.warn(`UnifiedReader: 不支持的格式 ${c.value}`)}catch(k){console.error("UnifiedReader: 加载书籍信息失败:",k);let y="加载失败";k.message?y=k.message:typeof k=="string"?y=k:y="未知错误，请查看控制台获取详细信息",f.value=y}finally{a.value=!1}},D=()=>{const k={timestamp:new Date().toISOString(),environment:{isElectron:typeof window<"u"&&!!window.electronAPI,hasWindow:typeof window<"u",userAgent:typeof navigator<"u"?navigator.userAgent:"unknown"},api:{electronAPI:!!window.electronAPI,readerAPI:!!(window.electronAPI&&window.electronAPI.reader),getBookMethod:!!(window.electronAPI&&window.electronAPI.reader&&typeof window.electronAPI.reader.getBook=="function")},bookId:t.bookId,currentState:{isLoading:a.value,hasError:!!f.value,errorMessage:f.value,hasBookInfo:!!r.value}};return console.log("UnifiedReader: 环境诊断报告",k),k},O=async()=>{console.log("UnifiedReader: 开始智能重试...");const k=D();if(!k.environment.isElectron){f.value="应用未在Electron环境中运行，请重启应用";return}if(!k.api.electronAPI){f.value="Electron API未初始化，请等待应用完全加载后重试";return}if(!k.api.readerAPI){f.value="阅读器API不可用，请检查应用状态或重启应用";return}f.value="",console.log("UnifiedReader: 延迟2秒后重试..."),await new Promise(y=>setTimeout(y,2e3)),await S()},G=()=>{O()},C=()=>{console.log("UnifiedReader: 返回图书列表"),l.push("/bookshelf/library")},V=async()=>{console.log("UnifiedReader: 执行环境预检查...");try{if(typeof window>"u")throw new Error("Window对象不可用");return await new Promise(k=>setTimeout(k,500)),window.electronAPI?(console.log("UnifiedReader: ElectronAPI已可用"),window.electronAPI.reader?console.log("UnifiedReader: 阅读器API已可用"):console.warn("UnifiedReader: 阅读器API暂未可用")):console.warn("UnifiedReader: ElectronAPI暂未可用，将在加载时等待"),console.log("UnifiedReader: 环境预检查完成"),!0}catch(k){return console.error("UnifiedReader: 环境预检查失败:",k),!1}};return Me(async()=>{console.log("UnifiedReader: 组件已挂载，开始初始化..."),await V()||console.warn("UnifiedReader: 环境预检查未通过，但仍尝试加载书籍信息"),await S()}),Fe(()=>t.bookId,()=>{t.bookId&&S()}),(k,y)=>{var $;const B=j("el-icon"),Y=j("el-button");return P(),x("div",vl,[a.value?(P(),x("div",gl,[n(B,{class:"is-loading"},{default:b(()=>[n(h(De))]),_:1}),y[0]||(y[0]=e("span",null,"正在加载阅读器...",-1))])):f.value?(P(),x("div",fl,[n(B,null,{default:b(()=>[n(h(Ve))]),_:1}),e("span",null,U(f.value),1),n(Y,{onClick:G,type:"primary",size:"small"},{default:b(()=>y[1]||(y[1]=[W("重试")])),_:1,__:[1]})])):c.value==="txt"?(P(),Re(cn,{key:2,"book-id":k.bookId,onGoBack:C},null,8,["book-id"])):c.value==="pdf"?(P(),Re($n,{key:3,"book-id":k.bookId,"book-info":r.value,onGoBack:C},null,8,["book-id","book-info"])):c.value==="epub"?(P(),Re(dl,{key:4,"book-id":k.bookId,onGoBack:C},null,8,["book-id"])):c.value==="mobi"?(P(),x("div",pl,[n(B,null,{default:b(()=>[n(h(nt))]),_:1}),y[3]||(y[3]=e("h3",null,"MOBI阅读器",-1)),y[4]||(y[4]=e("p",null,"MOBI格式支持正在开发中...",-1)),n(Y,{onClick:C,type:"primary"},{default:b(()=>y[2]||(y[2]=[W("返回图书列表")])),_:1,__:[2]})])):(P(),x("div",hl,[n(B,null,{default:b(()=>[n(h(Ve))]),_:1}),y[6]||(y[6]=e("h3",null,"不支持的文件格式",-1)),e("p",null,"当前不支持 "+U(($=c.value)==null?void 0:$.toUpperCase())+" 格式的文件",1),n(Y,{onClick:C,type:"primary"},{default:b(()=>y[5]||(y[5]=[W("返回图书列表")])),_:1,__:[5]})]))])}}}),kl=He(ml,[["__scopeId","data-v-ff4a6c4c"]]);export{kl as default};
