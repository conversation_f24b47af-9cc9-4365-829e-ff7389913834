<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src\backend">
      <UniqueIdentifier>{0601BD18-2FE3-2D4A-0C05-611A0F36D709}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src\backend">
      <UniqueIdentifier>{0601BD18-2FE3-2D4A-0C05-611A0F36D709}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src\backend">
      <UniqueIdentifier>{0601BD18-2FE3-2D4A-0C05-611A0F36D709}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src\backend">
      <UniqueIdentifier>{0601BD18-2FE3-2D4A-0C05-611A0F36D709}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src\bmp">
      <UniqueIdentifier>{C08C95BF-9646-DB44-5C81-9CB5B5F652A5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:">
      <UniqueIdentifier>{7B735499-E5DD-1C2B-6C26-70023832A1CF}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\hostedtoolcache">
      <UniqueIdentifier>{296B63E6-8BC4-B79B-77CC-9C615B0D2B0F}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\hostedtoolcache\windows">
      <UniqueIdentifier>{C1450D01-C033-76F3-3763-6DE88AF48A77}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\hostedtoolcache\windows\node">
      <UniqueIdentifier>{A49AD564-6B22-6A46-08E5-B5A7F4427839}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\hostedtoolcache\windows\node\21.7.3">
      <UniqueIdentifier>{1C63F1C8-0353-A369-E968-394FCDA23886}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\hostedtoolcache\windows\node\21.7.3\x64">
      <UniqueIdentifier>{E075064C-529C-A4E7-0810-FB88D599C3BE}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\hostedtoolcache\windows\node\21.7.3\x64\node_modules">
      <UniqueIdentifier>{56DF7A98-063D-FB9D-485C-089023B4C16A}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\hostedtoolcache\windows\node\21.7.3\x64\node_modules\npm">
      <UniqueIdentifier>{741E0E76-39B2-B1AB-9FA1-F1A20B16F295}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\hostedtoolcache\windows\node\21.7.3\x64\node_modules\npm\node_modules">
      <UniqueIdentifier>{56DF7A98-063D-FB9D-485C-089023B4C16A}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\hostedtoolcache\windows\node\21.7.3\x64\node_modules\npm\node_modules\node-gyp">
      <UniqueIdentifier>{77348C0E-2034-7791-74D5-63C077DF5A3B}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\hostedtoolcache\windows\node\21.7.3\x64\node_modules\npm\node_modules\node-gyp\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\backend\Backend.cc">
      <Filter>..\src\backend</Filter>
    </ClCompile>
    <ClCompile Include="..\src\backend\ImageBackend.cc">
      <Filter>..\src\backend</Filter>
    </ClCompile>
    <ClCompile Include="..\src\backend\PdfBackend.cc">
      <Filter>..\src\backend</Filter>
    </ClCompile>
    <ClCompile Include="..\src\backend\SvgBackend.cc">
      <Filter>..\src\backend</Filter>
    </ClCompile>
    <ClCompile Include="..\src\bmp\BMPParser.cc">
      <Filter>..\src\bmp</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Backends.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Canvas.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\CanvasGradient.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\CanvasPattern.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\CanvasRenderingContext2d.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\closure.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\color.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Image.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\ImageData.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\init.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\register_font.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\FontParser.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="C:\hostedtoolcache\windows\node\21.7.3\x64\node_modules\npm\node_modules\node-gyp\src\win_delay_load_hook.cc">
      <Filter>C:\hostedtoolcache\windows\node\21.7.3\x64\node_modules\npm\node_modules\node-gyp\src</Filter>
    </ClCompile>
    <None Include="..\binding.gyp">
      <Filter>..</Filter>
    </None>
  </ItemGroup>
</Project>
