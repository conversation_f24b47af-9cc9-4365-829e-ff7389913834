@echo off
chcp 65001 >nul
title Yu Reader 桌面应用启动器

echo.
echo ========================================
echo    🚀 Yu Reader 桌面应用启动器
echo ========================================
echo.

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 Node.js，请先安装 Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js 已安装

REM 检查依赖
if not exist "node_modules" (
    echo ❌ 依赖未安装，正在安装...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖已安装

REM 启动开发服务器（如果未运行）
echo 🔄 检查开发服务器状态...
netstat -an | find "5173" >nul
if errorlevel 1 (
    echo 🚀 启动开发服务器...
    start "Yu Reader Dev Server" cmd /c "npm run dev"
    echo ⏳ 等待开发服务器启动...
    timeout /t 10 /nobreak >nul
) else (
    echo ✅ 开发服务器已运行
)

REM 在浏览器中打开应用
echo 🌐 在浏览器中打开应用...
start http://127.0.0.1:5173/

echo.
echo ========================================
echo    ✅ 应用已在浏览器中启动
echo    📱 URL: http://127.0.0.1:5173/
echo ========================================
echo.
echo 💡 提示: 
echo    - 浏览器版本包含所有功能
echo    - 支持PDF阅读器的所有增强功能
echo    - 可以正常使用文件上传和处理
echo.

pause
