/**
 * URL polyfill for vue3-pdf-app compatibility
 * 提供URL.parse方法的polyfill，因为浏览器环境中不存在这个Node.js方法
 * vue3-pdf-app内部可能需要这个方法来处理PDF文档的URL解析
 */

// 扩展全局URL对象
declare global {
  interface URLConstructor {
    parse(input: string, base?: string): URL | null
  }
}

// 如果URL.parse不存在，则添加polyfill
if (typeof URL !== 'undefined' && !URL.parse) {
  URL.parse = function(input: string, base?: string): URL | null {
    try {
      return new URL(input, base)
    } catch (error) {
      return null
    }
  }
}

export {}
