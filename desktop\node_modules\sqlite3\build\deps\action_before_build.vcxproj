<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{FDC71119-B3D1-815D-7E30-B6E174130CDE}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>action_before_build</RootNamespace>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props"/>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Label="Locals">
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props"/>
  <ImportGroup Label="ExtensionSettings"/>
  <ImportGroup Label="PropertySheets">
    <Import Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props"/>
  </ImportGroup>
  <PropertyGroup Label="UserMacros"/>
  <PropertyGroup>
    <ExecutablePath>$(ExecutablePath);$(MSBuildProjectDirectory)\..\..\deps\bin\;$(MSBuildProjectDirectory)\..\..\deps\bin\</ExecutablePath>
    <IntDir>$(Configuration)\obj\$(ProjectName)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</LinkIncremental>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <TargetName>$(ProjectName)</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.electron-gyp\28.3.3\include\node;C:\Users\<USER>\.electron-gyp\28.3.3\src;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\config;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\openssl\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\uv\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\zlib;C:\Users\<USER>\.electron-gyp\28.3.3\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++17 %(AdditionalOptions)</AdditionalOptions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <MinimalRebuild>false</MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>false</OmitFramePointers>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=action_before_build;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;V8_DEPRECATION_WARNINGS;V8_IMMINENT_DEPRECATION_WARNINGS;_GLIBCXX_USE_CXX11_ABI=1;ELECTRON_ENSURE_CONFIG_GYPI;WIN32;USING_ELECTRON_CONFIG_GYPI;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;OPENSSL_NO_ASM;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;V8_ENABLE_CHECKS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\.electron-gyp\\28.3.3\\ia32\\node.lib&quot;</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.electron-gyp\28.3.3\include\node;C:\Users\<USER>\.electron-gyp\28.3.3\src;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\config;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\openssl\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\uv\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\zlib;C:\Users\<USER>\.electron-gyp\28.3.3\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=action_before_build;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;V8_DEPRECATION_WARNINGS;V8_IMMINENT_DEPRECATION_WARNINGS;_GLIBCXX_USE_CXX11_ABI=1;ELECTRON_ENSURE_CONFIG_GYPI;WIN32;USING_ELECTRON_CONFIG_GYPI;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;OPENSSL_NO_ASM;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;V8_ENABLE_CHECKS;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.electron-gyp\28.3.3\include\node;C:\Users\<USER>\.electron-gyp\28.3.3\src;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\config;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\openssl\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\uv\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\zlib;C:\Users\<USER>\.electron-gyp\28.3.3\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++17 %(AdditionalOptions)</AdditionalOptions>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>true</OmitFramePointers>
      <Optimization>Full</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=action_before_build;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;V8_DEPRECATION_WARNINGS;V8_IMMINENT_DEPRECATION_WARNINGS;_GLIBCXX_USE_CXX11_ABI=1;ELECTRON_ENSURE_CONFIG_GYPI;WIN32;USING_ELECTRON_CONFIG_GYPI;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;OPENSSL_NO_ASM;HOST_BINARY=&quot;node.exe&quot;;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\.electron-gyp\\28.3.3\\ia32\\node.lib&quot;</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.electron-gyp\28.3.3\include\node;C:\Users\<USER>\.electron-gyp\28.3.3\src;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\config;C:\Users\<USER>\.electron-gyp\28.3.3\deps\openssl\openssl\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\uv\include;C:\Users\<USER>\.electron-gyp\28.3.3\deps\zlib;C:\Users\<USER>\.electron-gyp\28.3.3\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=action_before_build;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;V8_DEPRECATION_WARNINGS;V8_IMMINENT_DEPRECATION_WARNINGS;_GLIBCXX_USE_CXX11_ABI=1;ELECTRON_ENSURE_CONFIG_GYPI;WIN32;USING_ELECTRON_CONFIG_GYPI;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;OPENSSL_NO_ASM;HOST_BINARY=&quot;node.exe&quot;;NDEBUG;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\..\deps\sqlite3.gyp"/>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\reader\desktop\node_modules\@electron\node-gyp\src\win_delay_load_hook.cc"/>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\..\deps\sqlite-autoconf-3440200.tar.gz">
      <FileType>Document</FileType>
      <Command>call call &quot;node&quot; &quot;../../deps/extract.js&quot; &quot;../../deps/sqlite-autoconf-3440200.tar.gz&quot; &quot;$(OutDir)obj/global_intermediate&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>unpack_sqlite_dep</Message>
      <Outputs>$(OutDir)\obj\global_intermediate\sqlite-autoconf-3440200\sqlite3.c</Outputs>
    </CustomBuild>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets"/>
  <ImportGroup Label="ExtensionTargets"/>
</Project>
