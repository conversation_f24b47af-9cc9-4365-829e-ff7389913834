var pe=Object.defineProperty;var me=(l,e,t)=>e in l?pe(l,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[e]=t;var X=(l,e,t)=>me(l,typeof e!="symbol"?e+"":e,t);import{d as fe,b as M,q as ge,v as he,e as ve,c as b,f as a,a as i,w as u,r as U,l as z,k as E,n as _e,u as m,h as ye,F as $,i as L,t as x,M as we,N as be,A as xe,O as Ce,P as ke,Q as Fe,m as V,j as De,o as y,L as Se,_ as Ie}from"./index-DS8Rgqx0.js";import{u as Me}from"./bookshelf-CA-71gsC.js";import*as I from"fs";import*as D from"path";import*as Ve from"crypto";let B;const ze=new Uint8Array(16);function Ee(){if(!B&&(B=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!B))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return B(ze)}const v=[];for(let l=0;l<256;++l)v.push((l+256).toString(16).slice(1));function Ne(l,e=0){return v[l[e+0]]+v[l[e+1]]+v[l[e+2]]+v[l[e+3]]+"-"+v[l[e+4]]+v[l[e+5]]+"-"+v[l[e+6]]+v[l[e+7]]+"-"+v[l[e+8]]+v[l[e+9]]+"-"+v[l[e+10]]+v[l[e+11]]+v[l[e+12]]+v[l[e+13]]+v[l[e+14]]+v[l[e+15]]}const Ue=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),q={randomUUID:Ue};function Te(l,e,t){if(q.randomUUID&&!l)return q.randomUUID();l=l||{};const r=l.random||(l.rng||Ee)();return r[6]=r[6]&15|64,r[8]=r[8]&63|128,Ne(r)}class Be{async extractMetadata(e){try{if(!I.existsSync(e))return{success:!1,error:"文件不存在"};const r=I.statSync(e).size,o=await this.calculateFileHash(e),d=this.detectFormat(e);if(!d)return{success:!1,error:"不支持的文件格式"};let g;switch(d){case"epub":g=await this.extractEpubMetadata(e,r,o);break;case"txt":g=await this.extractTxtMetadata(e,r,o);break;case"mobi":g=await this.extractMobiMetadata(e,r,o);break;case"azw3":g=await this.extractAzw3Metadata(e,r,o);break;default:return{success:!1,error:`不支持的格式: ${d}`}}return{success:!0,metadata:g}}catch(t){return console.error("元数据提取失败:",t),{success:!1,error:t instanceof Error?t.message:"未知错误"}}}detectFormat(e){switch(D.extname(e).toLowerCase()){case".epub":return"epub";case".txt":return"txt";case".mobi":return"mobi";case".azw3":return"azw3";default:return null}}async calculateFileHash(e){return new Promise((t,r)=>{const o=Ve.createHash("sha256"),d=I.createReadStream(e);d.on("data",g=>o.update(g)),d.on("end",()=>t(o.digest("hex"))),d.on("error",r)})}async extractEpubMetadata(e,t,r){return{title:D.basename(e,".epub"),author:"未知作者",language:"zh-CN",description:"",wordCount:0,totalPages:0,format:"epub",fileSize:t,fileHash:r}}async extractTxtMetadata(e,t,r){try{const o=I.readFileSync(e,"utf-8"),d=o.split(`
`),g=o.split(/\s+/).filter(w=>w.length>0).length,S=Math.ceil(d.length/50);let f=D.basename(e,".txt"),k="未知作者";if(d.length>0){const w=d[0].trim();w.length>0&&w.length<100&&(f=w)}return{title:f,author:k,language:"zh-CN",description:o.substring(0,200)+(o.length>200?"...":""),wordCount:g,totalPages:S,format:"txt",fileSize:t,fileHash:r}}catch(o){return console.error("TXT元数据提取失败:",o),{title:D.basename(e,".txt"),author:"未知作者",language:"zh-CN",description:"",wordCount:0,totalPages:0,format:"txt",fileSize:t,fileHash:r}}}async extractMobiMetadata(e,t,r){return{title:D.basename(e,".mobi"),author:"未知作者",language:"zh-CN",description:"",wordCount:0,totalPages:0,format:"mobi",fileSize:t,fileHash:r}}async extractAzw3Metadata(e,t,r){return{title:D.basename(e,".azw3"),author:"未知作者",language:"zh-CN",description:"",wordCount:0,totalPages:0,format:"azw3",fileSize:t,fileHash:r}}async extractBatchMetadata(e){const t=[];for(const r of e){const o=await this.extractMetadata(r);t.push(o)}return t}validateMetadata(e){const t=[];return(!e.title||e.title.trim().length===0)&&t.push("标题不能为空"),(!e.author||e.author.trim().length===0)&&t.push("作者不能为空"),e.format||t.push("文件格式不能为空"),e.fileSize<=0&&t.push("文件大小必须大于0"),e.fileHash||t.push("文件哈希值不能为空"),{valid:t.length===0,errors:t}}}const Pe=new Be;class Re{constructor(){X(this,"supportedFormats",[".epub",".txt",".pdf",".mobi",".azw3"])}async importFile(e,t={}){return this.importFiles([e],t)}async importFiles(e,t={}){var d,g,S,C;const r={success:!0,imported:[],errors:[],duplicates:[],totalFiles:e.length,successCount:0,errorCount:0,duplicateCount:0},o={total:e.length,completed:0,current:"",percentage:0,status:"pending",errors:[]};try{o.status="processing",(d=t.onProgress)==null||d.call(t,o);for(let f=0;f<e.length;f++){const k=e[f];o.current=D.basename(k),o.completed=f,o.percentage=Math.round(f/e.length*100),(g=t.onProgress)==null||g.call(t,o);try{const w=await this.processFile(k,t);w?(r.imported.push(w),r.successCount++):(r.duplicates.push(k),r.duplicateCount++)}catch(w){const F={filePath:k,error:w instanceof Error?w.message:"未知错误",timestamp:new Date};r.errors.push(F),o.errors.push(F),r.errorCount++}}o.completed=e.length,o.percentage=100,o.status=r.errors.length===0?"completed":"error",(S=t.onProgress)==null||S.call(t,o),r.success=r.errorCount===0}catch(f){o.status="error",(C=t.onProgress)==null||C.call(t,o),r.success=!1,r.errors.push({filePath:"batch",error:f instanceof Error?f.message:"批量导入失败",timestamp:new Date})}return r}async importFolder(e,t=!0,r={}){try{const o=await this.scanFolder(e,t);return this.importFiles(o,r)}catch(o){return{success:!1,imported:[],errors:[{filePath:e,error:o instanceof Error?o.message:"扫描文件夹失败",timestamp:new Date}],duplicates:[],totalFiles:0,successCount:0,errorCount:1,duplicateCount:0}}}async processFile(e,t){if(t.validateFiles&&!this.validateFile(e))throw new Error("文件验证失败");const r=await Pe.extractMetadata(e);if(!r.success||!r.metadata)throw new Error(r.error||"元数据提取失败");const o=r.metadata;if(!t.allowDuplicates&&await this.checkDuplicate(o.fileHash,e)){if(t.skipExisting)return null;throw new Error("文件已存在")}const d=await this.createBookRecord(o,e);return t.extractCovers&&await this.extractCover(d,e),d}validateFile(e){try{if(!I.existsSync(e))return!1;const t=D.extname(e).toLowerCase();return!this.supportedFormats.includes(t)||I.statSync(e).size===0?!1:(I.accessSync(e,I.constants.R_OK),!0)}catch{return!1}}async checkDuplicate(e,t){return!1}async createBookRecord(e,t){const r=new Date().toISOString();return{id:Te(),title:e.title,author:e.author,filePath:t,fileSize:e.fileSize,format:e.format,coverPath:e.coverPath,readProgress:0,totalPages:e.totalPages,currentPage:0,currentPosition:void 0,readingTime:0,isbn:e.isbn,publisher:e.publisher,publishDate:e.publishDate,description:e.description,language:e.language,wordCount:e.wordCount,category:"默认分类",tags:void 0,isFavorite:!1,fileHash:e.fileHash,importSource:"manual",addedAt:r,lastReadAt:void 0,createdAt:r,updatedAt:r}}async extractCover(e,t){}async scanFolder(e,t){const r=[],o=d=>{const g=I.readdirSync(d);for(const S of g){const C=D.join(d,S),f=I.statSync(C);if(f.isFile()){const k=D.extname(S).toLowerCase();this.supportedFormats.includes(k)&&r.push(C)}else f.isDirectory()&&t&&o(C)}};return o(e),r}getSupportedFormats(){return[...this.supportedFormats]}isFormatSupported(e){const t=D.extname(e).toLowerCase();return this.supportedFormats.includes(t)}}const Ae=new Re,$e={class:"import-view"},Le={class:"import-header"},He={class:"import-options"},Oe={class:"options-content"},je={class:"import-content"},Xe={class:"import-methods"},qe={class:"upload-icon"},Ke={class:"supported-formats"},Qe={class:"folder-import"},We=["accept"],Ze={key:0,class:"import-progress"},Ge={class:"progress-header"},Je={key:0,class:"progress-content"},Ye={class:"overall-progress"},et={class:"progress-info"},tt={class:"current-file"},st={class:"progress-text"},rt={key:0,class:"error-list"},ot={class:"error-items"},at={class:"error-details"},nt={class:"error-file"},lt={class:"error-message"},it={key:1,class:"import-result"},ct={class:"result-summary"},ut={class:"summary-item success"},dt={key:0,class:"summary-item error"},pt={key:1,class:"summary-item warning"},mt={class:"result-actions"},ft={key:2,class:"recent-imports"},gt={class:"recent-list"},ht=["onClick"],vt={class:"book-cover"},_t=["src","alt"],yt={class:"book-info"},wt={class:"book-meta"},bt={class:"format-tag"},xt={class:"import-time"},Ct=fe({__name:"ImportView",setup(l){const e=De(),t=Me(),{isImporting:r,importProgress:o,startImport:d,updateImportProgress:g,finishImport:S}=t,C=M(!1),f=M("files"),k=M(),w=M(),F=M(null),P=M([]),N=M({allowDuplicates:!1,extractCovers:!0,validateFiles:!0,onProgress:n=>{g(n)}}),H=M({recursive:!0}),R=M(["epub","pdf","txt","mobi","azw3"]),K=ge(()=>R.value.map(n=>`.${n}`).join(",")),Q=n=>{n.preventDefault(),r||(C.value=!0)},W=n=>{n.preventDefault(),C.value=!1},Z=n=>{var p;if(n.preventDefault(),C.value=!1,r){V.warning("正在导入中，请等待完成");return}const s=Array.from(((p=n.dataTransfer)==null?void 0:p.files)||[]);A(s)},G=()=>{var n;if(r){V.warning("正在导入中，请等待完成");return}(n=k.value)==null||n.click()},J=()=>{var n;if(r){V.warning("正在导入中，请等待完成");return}(n=w.value)==null||n.click()},Y=n=>{const s=n.target,p=Array.from(s.files||[]);A(p),s.value=""},ee=n=>{const s=n.target,p=Array.from(s.files||[]);A(p),s.value=""},te=n=>{f.value=n},A=async n=>{if(n.length===0)return;const s=n.filter(p=>{var h;const _="."+((h=p.name.split(".").pop())==null?void 0:h.toLowerCase());return R.value.includes(_.substring(1))});if(s.length===0){V.warning("没有找到支持的文件格式");return}s.length!==n.length&&V.warning(`已过滤 ${n.length-s.length} 个不支持的文件`);try{d();const p=s.map(h=>h.name),_=await Ae.importFiles(p,N.value);S(_),F.value=_,_.success?V.success(`成功导入 ${_.successCount} 本图书`):V.error(`导入完成，成功 ${_.successCount} 本，失败 ${_.errorCount} 本`),await O()}catch(p){console.error("导入失败:",p),V.error("导入失败: "+(p instanceof Error?p.message:"未知错误")),S({success:!1,imported:[],errors:[{filePath:"batch",error:p instanceof Error?p.message:"未知错误",timestamp:new Date}],duplicates:[],totalFiles:s.length,successCount:0,errorCount:s.length,duplicateCount:0})}},se=async()=>{try{await Se.confirm("确定要取消当前导入操作吗？","确认取消",{confirmButtonText:"确定",cancelButtonText:"继续导入",type:"warning"}),V.info("导入已取消")}catch{}},re=n=>{switch(n){case"completed":return"success";case"error":return"exception";case"processing":return;default:return}},oe=n=>n.split("/").pop()||n,ae=()=>{e.push("/bookshelf/library")},ne=()=>{F.value=null},le=n=>{e.push(`/reader/${n.id}`)},ie=n=>{const s=n.target;s.src="/placeholder-book.png"},ce=n=>{const s=new Date(n),_=new Date().getTime()-s.getTime(),h=Math.floor(_/(1e3*60*60));return h<1?"刚刚":h<24?`${h}小时前`:h<24*7?`${Math.floor(h/24)}天前`:s.toLocaleDateString()},O=async()=>{try{P.value=[]}catch(n){console.error("加载最近导入失败:",n)}};return he(()=>t.importProgress,n=>{n&&n.status},{immediate:!1}),ve(async()=>{await O()}),(n,s)=>{const p=U("el-checkbox"),_=U("el-card"),h=U("el-icon"),j=U("el-tab-pane"),T=U("el-button"),ue=U("el-tabs"),de=U("el-progress");return y(),b("div",$e,[a("div",Le,[s[9]||(s[9]=a("h1",{class:"page-title"},"导入图书",-1)),s[10]||(s[10]=a("p",{class:"page-description"},"支持 EPUB、PDF、MOBI、TXT、AZW3 等格式",-1)),a("div",He,[i(_,null,{header:u(()=>s[5]||(s[5]=[a("span",null,"导入设置",-1)])),default:u(()=>[a("div",Oe,[i(p,{modelValue:N.value.allowDuplicates,"onUpdate:modelValue":s[0]||(s[0]=c=>N.value.allowDuplicates=c)},{default:u(()=>s[6]||(s[6]=[z(" 允许重复文件 ")])),_:1,__:[6]},8,["modelValue"]),i(p,{modelValue:N.value.extractCovers,"onUpdate:modelValue":s[1]||(s[1]=c=>N.value.extractCovers=c)},{default:u(()=>s[7]||(s[7]=[z(" 提取封面图片 ")])),_:1,__:[7]},8,["modelValue"]),i(p,{modelValue:N.value.validateFiles,"onUpdate:modelValue":s[2]||(s[2]=c=>N.value.validateFiles=c)},{default:u(()=>s[8]||(s[8]=[z(" 验证文件完整性 ")])),_:1,__:[8]},8,["modelValue"])])]),_:1})])]),a("div",je,[a("div",Xe,[i(ue,{modelValue:f.value,"onUpdate:modelValue":s[4]||(s[4]=c=>f.value=c),onTabChange:te},{default:u(()=>[i(j,{label:"文件导入",name:"files"},{default:u(()=>[a("div",{class:_e(["upload-area",{dragover:C.value,disabled:m(r)}]),onDrop:Z,onDragover:Q,onDragleave:W,onClick:G},[a("div",qe,[i(h,null,{default:u(()=>[i(m(ye))]),_:1})]),s[11]||(s[11]=a("div",{class:"upload-text"},[a("h3",null,"拖拽文件到此处或点击选择"),a("p",null,"支持批量导入多个文件")],-1)),a("div",Ke,[(y(!0),b($,null,L(R.value,c=>(y(),b("span",{key:c,class:"format-tag"},x(c.toUpperCase()),1))),128))])],34)]),_:1}),i(j,{label:"文件夹导入",name:"folder"},{default:u(()=>[a("div",Qe,[i(T,{type:"primary",size:"large",loading:m(r),onClick:J},{default:u(()=>[i(h,null,{default:u(()=>[i(m(we))]),_:1}),s[12]||(s[12]=z(" 选择文件夹 "))]),_:1,__:[12]},8,["loading"]),s[14]||(s[14]=a("p",{class:"folder-tip"}," 选择包含电子书文件的文件夹，支持递归扫描子文件夹 ",-1)),i(p,{modelValue:H.value.recursive,"onUpdate:modelValue":s[3]||(s[3]=c=>H.value.recursive=c)},{default:u(()=>s[13]||(s[13]=[z(" 递归扫描子文件夹 ")])),_:1,__:[13]},8,["modelValue"])])]),_:1})]),_:1},8,["modelValue"])]),a("input",{ref_key:"fileInput",ref:k,type:"file",multiple:"",accept:K.value,style:{display:"none"},onChange:Y},null,40,We),a("input",{ref_key:"folderInput",ref:w,type:"file",webkitdirectory:"",style:{display:"none"},onChange:ee},null,544),m(r)||m(o)?(y(),b("div",Ze,[i(_,null,{header:u(()=>[a("div",Ge,[s[16]||(s[16]=a("span",null,"导入进度",-1)),m(r)?(y(),xe(T,{key:0,size:"small",type:"danger",onClick:se},{default:u(()=>s[15]||(s[15]=[z(" 取消导入 ")])),_:1,__:[15]})):E("",!0)])]),default:u(()=>[m(o)?(y(),b("div",Je,[a("div",Ye,[a("div",et,[a("span",tt,x(m(o).current),1),a("span",st,x(m(o).completed)+" / "+x(m(o).total),1)]),i(de,{percentage:m(o).percentage,status:re(m(o).status),"stroke-width":8},null,8,["percentage","status"])]),m(o).errors.length>0?(y(),b("div",rt,[s[17]||(s[17]=a("h4",null,"导入错误",-1)),a("div",ot,[(y(!0),b($,null,L(m(o).errors,c=>(y(),b("div",{key:c.filePath,class:"error-item"},[i(h,{class:"error-icon"},{default:u(()=>[i(m(be))]),_:1}),a("div",at,[a("div",nt,x(oe(c.filePath)),1),a("div",lt,x(c.error),1)])]))),128))])])):E("",!0)])):E("",!0)]),_:1})])):E("",!0),F.value?(y(),b("div",it,[i(_,null,{header:u(()=>s[18]||(s[18]=[a("span",null,"导入结果",-1)])),default:u(()=>[a("div",ct,[a("div",ut,[i(h,null,{default:u(()=>[i(m(Ce))]),_:1}),a("span",null,"成功导入 "+x(F.value.successCount)+" 本",1)]),F.value.errorCount>0?(y(),b("div",dt,[i(h,null,{default:u(()=>[i(m(ke))]),_:1}),a("span",null,"导入失败 "+x(F.value.errorCount)+" 本",1)])):E("",!0),F.value.duplicateCount>0?(y(),b("div",pt,[i(h,null,{default:u(()=>[i(m(Fe))]),_:1}),a("span",null,"重复文件 "+x(F.value.duplicateCount)+" 本",1)])):E("",!0)]),a("div",mt,[i(T,{onClick:ae},{default:u(()=>s[19]||(s[19]=[z(" 查看导入的图书 ")])),_:1,__:[19]}),i(T,{onClick:ne},{default:u(()=>s[20]||(s[20]=[z(" 清除结果 ")])),_:1,__:[20]})])]),_:1})])):E("",!0),P.value.length>0?(y(),b("div",ft,[i(_,null,{header:u(()=>s[21]||(s[21]=[a("span",null,"最近导入",-1)])),default:u(()=>[a("div",gt,[(y(!0),b($,null,L(P.value,c=>(y(),b("div",{key:c.id,class:"recent-item",onClick:kt=>le(c)},[a("div",vt,[a("img",{src:c.coverPath||"/placeholder-book.png",alt:c.title,onError:ie},null,40,_t)]),a("div",yt,[a("h4",null,x(c.title),1),a("p",null,x(c.author),1),a("div",wt,[a("span",bt,x(c.format.toUpperCase()),1),a("span",xt,x(ce(c.addedAt)),1)])])],8,ht))),128))])]),_:1})])):E("",!0)])])}}}),It=Ie(Ct,[["__scopeId","data-v-f480492f"]]);export{It as default};
