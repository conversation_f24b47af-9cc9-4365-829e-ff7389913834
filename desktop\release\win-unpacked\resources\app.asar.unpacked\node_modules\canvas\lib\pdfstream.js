'use strict'

/*!
 * Canvas - PDFStream
 */

const { Readable } = require('stream')
function noop () {}

class PDFStream extends Readable {
  constructor (canvas, options) {
    super()

    this.canvas = canvas
    this.options = options
  }

  _read () {
    // For now we're not controlling the c++ code's data emission, so we only
    // call canvas.streamPDFSync once and let it emit data at will.
    this._read = noop

    this.canvas.streamPDFSync((err, chunk, len) => {
      if (err) {
        this.emit('error', err)
      } else if (len) {
        this.push(chunk)
      } else {
        this.push(null)
      }
    }, this.options)
  }
}

module.exports = PDFStream
