import{d as g,b as l,e as w,c as u,f as t,a as n,w as c,u as _,aj as y,r as C,t as e,a3 as D,ak as B,F as V,i as x,A as S,al as T,j as $,o as d,_ as j}from"./index-DS8Rgqx0.js";const A={class:"bookmark-view"},E={class:"bookmark-content"},F={class:"quick-nav"},L={class:"nav-icon"},M={class:"nav-content"},N={class:"nav-count"},R={class:"nav-icon"},q={class:"nav-content"},I={class:"nav-count"},J={class:"nav-icon"},P={class:"nav-content"},z={class:"nav-count"},G={class:"recent-section"},H={class:"activity-list"},K={class:"activity-icon"},O={class:"activity-content"},Q={class:"activity-title"},U={class:"activity-book"},W={class:"activity-time"},X=g({__name:"BookmarkView",setup(Y){const k=$(),p=l(15),m=l(8),f=l(23),h=l([{id:"1",title:"添加了新书签",book:"JavaScript高级程序设计",time:Date.now()-36e5,icon:"Star"},{id:"2",title:"创建了新笔记",book:"Vue.js设计与实现",time:Date.now()-72e5,icon:"EditPen"},{id:"3",title:"高亮了重要段落",book:"React技术揭秘",time:Date.now()-108e5,icon:"Brush"}]),r=a=>{k.push(a)},b=a=>{const s=new Date(a),o=new Date().getTime()-s.getTime(),v=Math.floor(o/(1e3*60*60));return v<1?"刚刚":v<24?`${v}小时前`:s.toLocaleDateString()};return w(()=>{console.log("书签笔记页面已加载")}),(a,s)=>{const i=C("el-icon");return d(),u("div",A,[s[10]||(s[10]=t("div",{class:"page-header"},[t("h1",{class:"page-title"},"书签笔记"),t("p",{class:"page-description"},"管理您的阅读标记和笔记")],-1)),t("div",E,[t("div",F,[t("div",{class:"nav-card",onClick:s[0]||(s[0]=o=>r("/bookmark/bookmarks"))},[t("div",L,[n(i,null,{default:c(()=>[n(_(y))]),_:1})]),t("div",M,[s[3]||(s[3]=t("h3",null,"书签管理",-1)),s[4]||(s[4]=t("p",null,"查看所有书签",-1)),t("span",N,e(p.value)+" 个书签",1)])]),t("div",{class:"nav-card",onClick:s[1]||(s[1]=o=>r("/bookmark/notes"))},[t("div",R,[n(i,null,{default:c(()=>[n(_(D))]),_:1})]),t("div",q,[s[5]||(s[5]=t("h3",null,"笔记管理",-1)),s[6]||(s[6]=t("p",null,"整理阅读笔记",-1)),t("span",I,e(m.value)+" 条笔记",1)])]),t("div",{class:"nav-card",onClick:s[2]||(s[2]=o=>r("/bookmark/highlights"))},[t("div",J,[n(i,null,{default:c(()=>[n(_(B))]),_:1})]),t("div",P,[s[7]||(s[7]=t("h3",null,"高亮管理",-1)),s[8]||(s[8]=t("p",null,"管理文本高亮",-1)),t("span",z,e(f.value)+" 个高亮",1)])])]),t("div",G,[s[9]||(s[9]=t("h2",null,"最近活动",-1)),t("div",H,[(d(!0),u(V,null,x(h.value,o=>(d(),u("div",{key:o.id,class:"activity-item"},[t("div",K,[n(i,null,{default:c(()=>[(d(),S(T(o.icon)))]),_:2},1024)]),t("div",O,[t("div",Q,e(o.title),1),t("div",U,e(o.book),1),t("div",W,e(b(o.time)),1)])]))),128))])])])])}}}),tt=j(X,[["__scopeId","data-v-ae7ca913"]]);export{tt as default};
