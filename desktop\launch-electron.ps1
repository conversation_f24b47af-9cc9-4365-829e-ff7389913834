Write-Host "🚀 启动 Yu Reader 桌面应用..." -ForegroundColor Green

# 设置环境变量
$env:NODE_ENV = "development"

# 检查必要文件
if (-not (Test-Path "dist\main\app.js")) {
    Write-Host "❌ 主进程文件不存在，请先运行开发服务器" -ForegroundColor Red
    Write-Host "运行命令: npm run dev" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

if (-not (Test-Path "dist\preload\index.js")) {
    Write-Host "❌ 预加载脚本不存在，请先运行开发服务器" -ForegroundColor Red
    Write-Host "运行命令: npm run dev" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "✅ 文件检查通过" -ForegroundColor Green
Write-Host "📱 启动Electron应用..." -ForegroundColor Cyan

# 启动Electron
try {
    & ".\node_modules\.bin\electron.cmd" "."
} catch {
    Write-Host "❌ 启动失败: $_" -ForegroundColor Red
    Read-Host "按任意键退出"
}

Write-Host "📱 应用已退出" -ForegroundColor Yellow
