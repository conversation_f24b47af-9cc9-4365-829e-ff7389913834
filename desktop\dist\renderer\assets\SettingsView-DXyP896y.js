import{d as W,am as A,W as T,b as E,V as w,e as I,c as N,f as t,a as s,w as n,r as p,l as u,t as m,m as o,o as P,_ as M}from"./index-DS8Rgqx0.js";const L={class:"settings-view"},Y={class:"settings-content"},D={class:"setting-section"},R={class:"setting-section"},j={class:"setting-item"},q={class:"setting-item"},G={class:"setting-item"},J={class:"setting-section"},K={class:"setting-item"},O={class:"setting-section"},Q={class:"setting-item"},X={class:"setting-item"},Z={class:"setting-section"},$={class:"about-section"},ee={class:"system-info"},te=W({__name:"SettingsView",setup(le){const d=A(),b=T(),V=E("appearance"),l=w({...d.settings}),g=w({platform:"",version:""}),c=async()=>{try{await b.setTheme(l.theme),await d.updateSettings({theme:l.theme}),o.success("主题设置已保存")}catch(r){console.error("主题设置失败:",r),o.error("保存失败")}},x=async()=>{try{await d.updateSettings({fontSize:l.fontSize}),o.success("字体大小已保存")}catch{o.error("保存失败")}},C=async()=>{try{await d.updateSettings({fontFamily:l.fontFamily}),o.success("字体设置已保存")}catch{o.error("保存失败")}},U=async()=>{try{await d.updateSettings({lineHeight:l.lineHeight}),o.success("行高设置已保存")}catch{o.error("保存失败")}},h=async()=>{try{await d.updateSettings({pageWidth:l.pageWidth}),o.success("页面宽度已保存")}catch{o.error("保存失败")}},k=async()=>{try{await d.updateSettings({autoSave:l.autoSave}),o.success("自动保存设置已保存")}catch{o.error("保存失败")}},B=async()=>{try{await d.updateSettings({autoBackup:l.autoBackup}),o.success("自动备份设置已保存")}catch{o.error("保存失败")}},F=async()=>{try{await d.updateSettings({language:l.language}),o.success("语言设置已保存")}catch{o.error("保存失败")}};return I(()=>{g.platform=window.electronAPI.system.platform,g.version=window.electronAPI.system.version}),(r,e)=>{const i=p("el-radio"),y=p("el-radio-group"),_=p("el-slider"),v=p("el-option"),z=p("el-select"),f=p("el-tab-pane"),S=p("el-switch"),H=p("el-tabs");return P(),N("div",L,[e[32]||(e[32]=t("div",{class:"page-header"},[t("h1",null,"设置"),t("p",null,"个性化您的阅读体验")],-1)),t("div",Y,[s(H,{modelValue:V.value,"onUpdate:modelValue":e[8]||(e[8]=a=>V.value=a),class:"settings-tabs"},{default:n(()=>[s(f,{label:"外观",name:"appearance"},{default:n(()=>[t("div",D,[e[17]||(e[17]=t("h3",null,"主题",-1)),s(y,{modelValue:l.theme,"onUpdate:modelValue":e[0]||(e[0]=a=>l.theme=a),onChange:c},{default:n(()=>[s(i,{value:"light"},{default:n(()=>e[9]||(e[9]=[u("明亮主题")])),_:1,__:[9]}),s(i,{value:"dark"},{default:n(()=>e[10]||(e[10]=[u("深色主题")])),_:1,__:[10]}),s(i,{value:"eye-care"},{default:n(()=>e[11]||(e[11]=[u("护眼主题")])),_:1,__:[11]}),s(i,{value:"eye-care-warm"},{default:n(()=>e[12]||(e[12]=[u("护眼暖色主题")])),_:1,__:[12]}),s(i,{value:"high-contrast"},{default:n(()=>e[13]||(e[13]=[u("高对比度主题")])),_:1,__:[13]}),s(i,{value:"night"},{default:n(()=>e[14]||(e[14]=[u("夜间模式")])),_:1,__:[14]}),s(i,{value:"natural"},{default:n(()=>e[15]||(e[15]=[u("自然模式")])),_:1,__:[15]}),s(i,{value:"auto"},{default:n(()=>e[16]||(e[16]=[u("跟随系统")])),_:1,__:[16]})]),_:1},8,["modelValue"])]),t("div",R,[e[21]||(e[21]=t("h3",null,"字体设置",-1)),t("div",j,[e[18]||(e[18]=t("label",null,"字体大小",-1)),s(_,{modelValue:l.fontSize,"onUpdate:modelValue":e[1]||(e[1]=a=>l.fontSize=a),min:12,max:24,step:1,onChange:x,style:{width:"200px"}},null,8,["modelValue"]),t("span",null,m(l.fontSize)+"px",1)]),t("div",q,[e[19]||(e[19]=t("label",null,"字体族",-1)),s(z,{modelValue:l.fontFamily,"onUpdate:modelValue":e[2]||(e[2]=a=>l.fontFamily=a),onChange:C},{default:n(()=>[s(v,{label:"系统默认",value:"system-ui"}),s(v,{label:"宋体",value:"SimSun"}),s(v,{label:"微软雅黑",value:"Microsoft YaHei"}),s(v,{label:"苹方",value:"PingFang SC"})]),_:1},8,["modelValue"])]),t("div",G,[e[20]||(e[20]=t("label",null,"行高",-1)),s(_,{modelValue:l.lineHeight,"onUpdate:modelValue":e[3]||(e[3]=a=>l.lineHeight=a),min:1.2,max:2,step:.1,onChange:U,style:{width:"200px"}},null,8,["modelValue"]),t("span",null,m(l.lineHeight),1)])])]),_:1}),s(f,{label:"阅读",name:"reading"},{default:n(()=>[t("div",J,[e[23]||(e[23]=t("h3",null,"页面设置",-1)),t("div",K,[e[22]||(e[22]=t("label",null,"页面宽度",-1)),s(_,{modelValue:l.pageWidth,"onUpdate:modelValue":e[4]||(e[4]=a=>l.pageWidth=a),min:600,max:1200,step:50,onChange:h,style:{width:"200px"}},null,8,["modelValue"]),t("span",null,m(l.pageWidth)+"px",1)])]),t("div",O,[e[26]||(e[26]=t("h3",null,"自动功能",-1)),t("div",Q,[s(S,{modelValue:l.autoSave,"onUpdate:modelValue":e[5]||(e[5]=a=>l.autoSave=a),onChange:k},null,8,["modelValue"]),e[24]||(e[24]=t("label",null,"自动保存阅读进度",-1))]),t("div",X,[s(S,{modelValue:l.autoBackup,"onUpdate:modelValue":e[6]||(e[6]=a=>l.autoBackup=a),onChange:B},null,8,["modelValue"]),e[25]||(e[25]=t("label",null,"自动备份数据",-1))])])]),_:1}),s(f,{label:"语言",name:"language"},{default:n(()=>[t("div",Z,[e[29]||(e[29]=t("h3",null,"界面语言",-1)),s(y,{modelValue:l.language,"onUpdate:modelValue":e[7]||(e[7]=a=>l.language=a),onChange:F},{default:n(()=>[s(i,{value:"zh-CN"},{default:n(()=>e[27]||(e[27]=[u("简体中文")])),_:1,__:[27]}),s(i,{value:"en-US"},{default:n(()=>e[28]||(e[28]=[u("English")])),_:1,__:[28]})]),_:1},8,["modelValue"])])]),_:1}),s(f,{label:"关于",name:"about"},{default:n(()=>[t("div",$,[e[31]||(e[31]=t("div",{class:"app-info"},[t("h2",null,"Yu Reader"),t("p",null,"版本: 1.0.0"),t("p",null,"一个现代化的电子书阅读器")],-1)),t("div",ee,[e[30]||(e[30]=t("h3",null,"系统信息",-1)),t("p",null,"平台: "+m(g.platform),1),t("p",null,"Electron: "+m(g.version),1)])])]),_:1})]),_:1},8,["modelValue"])])])}}}),ne=M(te,[["__scopeId","data-v-076897ac"]]);export{ne as default};
