directories:
  output: release
  buildResources: build
appId: com.yureader.desktop
productName: Yu Reader
files:
  - filter:
      - dist/**/*
      - node_modules/**/*
      - package.json
extraResources:
  - from: build/
    to: build/
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
  icon: build/icon.ico
mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
  icon: build/icon.icns
  category: public.app-category.productivity
linux:
  target:
    - target: AppImage
      arch:
        - x64
  icon: build/icon.png
  category: Office
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
electronVersion: 28.3.3
