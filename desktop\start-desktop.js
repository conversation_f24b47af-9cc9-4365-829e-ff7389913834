/**
 * 启动桌面端应用
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动 Yu Reader 桌面应用...');

// 设置环境变量
process.env.NODE_ENV = 'development';

// 获取electron可执行文件路径
const electronPath = path.join(__dirname, 'node_modules', 'electron', 'dist', 'electron.exe');
const appPath = '.'; // 当前目录，package.json中的main字段会指向正确的入口文件

console.log('Electron路径:', electronPath);
console.log('应用路径:', appPath);
console.log('主入口文件:', path.join(__dirname, 'dist', 'main', 'app.js'));

// 检查文件是否存在
const fs = require('fs');
if (!fs.existsSync(electronPath)) {
  console.error('❌ Electron可执行文件不存在:', electronPath);
  process.exit(1);
}

const mainFile = path.join(__dirname, 'dist', 'main', 'app.js');
if (!fs.existsSync(mainFile)) {
  console.error('❌ 主入口文件不存在:', mainFile);
  console.log('请先运行 npm run build 构建应用');
  process.exit(1);
}

console.log('✅ 文件检查通过，启动应用...');

// 启动electron
const child = spawn(electronPath, [appPath], {
  stdio: 'inherit',
  cwd: __dirname,
  env: {
    ...process.env,
    NODE_ENV: 'development'
  }
});

child.on('error', (error) => {
  console.error('❌ 启动失败:', error);
});

child.on('close', (code) => {
  console.log(`📱 应用退出，代码: ${code}`);
});

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，关闭应用...');
  child.kill('SIGINT');
});
