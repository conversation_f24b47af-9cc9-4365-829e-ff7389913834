import{d as ye,b as B,c as d,f as t,k,a as l,u as n,E as fe,w as a,l as u,t as r,F,i as I,n as te,m as p,o as i,_ as ke,p as We,q as Xe,v as ve,e as Ye,r as g,x as Ze,y as el,s as ll,z as le,A as U,B as tl,C as ol,g as al,D as sl,G as nl,H as il,I as z,J as me,K as he,j as rl,L as dl}from"./index-DS8Rgqx0.js";import{u as ul}from"./unifiedLibrary-DwIwCSXr.js";const cl={class:"database-fix-container"},_l={class:"fix-section"},gl={class:"buttons"},pl={key:0,class:"check-result"},fl={class:"stats"},vl={key:0,class:"problem-books"},ml={class:"book-list"},hl={class:"book-info"},yl={class:"problem"},kl={class:"book-path"},bl={key:1,class:"fix-result"},wl={key:0},Cl={key:0,class:"errors"},xl=ye({__name:"DatabaseFixComponent",setup(be){const w=B(!1),R=B(!1),h=B(null),f=B(null);async function S(){w.value=!0,h.value=null;try{console.log("开始检查图书路径...");const _=await window.electronAPI.database.checkBookPaths();console.log("路径检查结果:",_),h.value=_,_.invalidPaths>0?p.warning(`发现 ${_.invalidPaths} 个无效路径`):p.success("所有图书路径都正常")}catch(_){console.error("检查路径失败:",_),p.error("检查路径失败: "+(_ instanceof Error?_.message:"未知错误"))}finally{w.value=!1}}async function M(){R.value=!0,f.value=null;try{console.log("开始修复图书路径...");const _=await window.electronAPI.database.fixBookPaths();console.log("路径修复结果:",_),f.value=_,_.success?_.fixedCount>0?(p.success(`成功修复 ${_.fixedCount} 本图书的路径`),setTimeout(()=>{S()},1e3)):p.info("没有需要修复的路径"):p.error("修复失败: "+_.message)}catch(_){console.error("修复路径失败:",_),p.error("修复路径失败: "+(_ instanceof Error?_.message:"未知错误"))}finally{R.value=!1}}return(_,c)=>(i(),d("div",cl,[c[11]||(c[11]=t("h3",null,"数据库修复工具",-1)),t("div",_l,[c[2]||(c[2]=t("h4",null,"图书文件路径修复",-1)),c[3]||(c[3]=t("p",null,"修复数据库中错误的图书文件路径，解决文件无法加载的问题。",-1)),t("div",gl,[l(n(fe),{onClick:S,loading:w.value},{default:a(()=>c[0]||(c[0]=[u("检查路径")])),_:1,__:[0]},8,["loading"]),l(n(fe),{type:"primary",onClick:M,loading:R.value},{default:a(()=>c[1]||(c[1]=[u("修复路径")])),_:1,__:[1]},8,["loading"])])]),h.value?(i(),d("div",pl,[c[8]||(c[8]=t("h4",null,"路径检查结果",-1)),t("div",fl,[t("p",null,[c[4]||(c[4]=t("strong",null,"总图书数:",-1)),u(" "+r(h.value.totalBooks),1)]),t("p",null,[c[5]||(c[5]=t("strong",null,"有效路径:",-1)),u(" "+r(h.value.validPaths),1)]),t("p",null,[c[6]||(c[6]=t("strong",null,"无效路径:",-1)),u(" "+r(h.value.invalidPaths),1)])]),h.value.problemBooks.length>0?(i(),d("div",vl,[c[7]||(c[7]=t("h5",null,"问题图书列表:",-1)),t("div",ml,[(i(!0),d(F,null,I(h.value.problemBooks,$=>(i(),d("div",{key:$.id,class:"problem-book"},[t("div",hl,[t("strong",null,r($.title),1),t("span",yl,r($.problem),1)]),t("div",kl,r($.filePath),1)]))),128))])])):k("",!0)])):k("",!0),f.value?(i(),d("div",bl,[c[10]||(c[10]=t("h4",null,"修复结果",-1)),t("div",{class:te(["result-info",{success:f.value.success,error:!f.value.success}])},[t("p",null,[t("strong",null,r(f.value.message),1)]),f.value.fixedCount>0?(i(),d("p",wl,"修复了 "+r(f.value.fixedCount)+" 本图书的路径",1)):k("",!0)],2),f.value.errors&&f.value.errors.length>0?(i(),d("div",Cl,[c[9]||(c[9]=t("h5",null,"错误信息:",-1)),t("ul",null,[(i(!0),d(F,null,I(f.value.errors,$=>(i(),d("li",{key:$},r($),1))),128))])])):k("",!0)])):k("",!0),c[12]||(c[12]=t("div",{class:"instructions"},[t("h4",null,"使用说明"),t("ol",null,[t("li",null,'点击"检查路径"查看当前数据库中的路径问题'),t("li",null,'如果发现问题，点击"修复路径"自动修复'),t("li",null,"修复完成后，重新加载图书列表即可")]),t("div",{class:"warning"},[t("p",null,[t("strong",null,"注意:"),u(" 修复操作会直接修改数据库，建议在修复前备份数据库文件。")])])],-1))]))}}),zl=ke(xl,[["__scopeId","data-v-dfe37ab8"]]),Bl={class:"unified-library-view"},$l={class:"library-header"},Pl={class:"header-left"},Vl={class:"stats-info"},Sl={key:0},El={key:1},Ul={class:"header-actions"},Fl={class:"toolbar"},Il={class:"toolbar-left"},Rl={class:"toolbar-right"},Al={class:"result-count"},Ml={key:0,class:"initialization-container"},Ll={class:"initialization-content"},Dl={class:"initialization-progress"},Tl={key:1,class:"retry-container"},Nl={class:"retry-content"},jl={class:"retry-progress"},Gl={key:2,class:"loading-container"},Kl={key:3,class:"error-container"},Ql={class:"debug-info",style:{background:"#ffe6e6",padding:"10px",margin:"10px 0","font-size":"12px",border:"1px solid #ff9999"}},ql={class:"error-actions"},Hl={key:0,class:"retry-info"},Jl={key:4,class:"empty-container"},Ol={key:5,class:"books-grid"},Wl=["onClick"],Xl={class:"book-cover"},Yl=["src","alt"],Zl={key:1,class:"default-cover"},et={class:"format-badge"},lt={key:2,class:"progress-overlay"},tt={class:"book-info"},ot=["title"],at=["title"],st={class:"book-meta"},nt={class:"file-size"},it={key:0,class:"book-tags"},rt={key:0,class:"more-tags"},dt={class:"book-actions"},ut={key:6,class:"books-list"},ct={class:"list-cover"},_t=["src","alt"],gt={key:1,class:"default-list-cover"},pt={class:"book-title-cell"},ft={key:0,class:"book-description"},vt={key:7,class:"pagination-container"},A=3,mt=ye({__name:"UnifiedLibraryView",setup(be){const w=rl(),R=ul(),{books:h,loading:f,error:S,searchQuery:M,filter:_,sortField:c,sortOrder:$,viewMode:q,pageSize:H,currentPage:J,paginatedBooks:P,filteredBooks:O,stats:L,availableFormats:we,availableAuthors: <AUTHORS>
