/**
 * PDF阅读器相关类型定义
 * 基于vue3-pdf-app的PDF阅读器类型系统
 * 兼容vue3-pdf-app的API和数据结构
 */

/**
 * PDF文档接口
 */
export interface PdfDocument {
  /** 文档ID */
  id: string
  /** 文件路径 */
  filePath: string
  /** 总页数 */
  totalPages: number
  /** 文档标题 */
  title?: string
  /** 文档作者 */
  author?: string
  /** 创建日期 */
  creationDate?: Date
  /** 文档大小（字节） */
  fileSize?: number
}

/**
 * PDF页面数据
 */
export interface PdfPageData {
  /** 页码 */
  pageNumber: number
  /** 页面宽度 */
  width: number
  /** 页面高度 */
  height: number
  /** 页面内容（用于渲染） */
  content: ImageData | ArrayBuffer
  /** 文本内容（用于搜索和选择） */
  textContent: PdfTextContent[]
  /** 页面缩略图 */
  thumbnail?: string
}

/**
 * PDF文本内容
 */
export interface PdfTextContent {
  /** 文本内容 */
  text: string
  /** 文本位置 */
  x: number
  y: number
  /** 文本宽度 */
  width: number
  /** 文本高度 */
  height: number
  /** 字体大小 */
  fontSize: number
  /** 字体名称 */
  fontName?: string
}

/**
 * PDF渲染选项
 */
export interface PdfRenderOptions {
  /** 缩放级别 */
  scale: number
  /** 旋转角度 (0, 90, 180, 270) */
  rotation: number
  /** 渲染质量 */
  quality: 'low' | 'medium' | 'high'
  /** 是否渲染文本层 */
  enableTextLayer: boolean
  /** 是否渲染注释层 */
  enableAnnotations: boolean
}

/**
 * PDF搜索结果
 */
export interface PdfSearchResult {
  /** 页码 */
  pageNumber: number
  /** 匹配的文本 */
  text: string
  /** 匹配位置 */
  x: number
  y: number
  /** 匹配区域宽度 */
  width: number
  /** 匹配区域高度 */
  height: number
  /** 上下文文本 */
  context?: string
}

/**
 * PDF书签
 */
export interface PdfBookmark {
  /** 书签ID */
  id: string
  /** 书签标题 */
  title: string
  /** 目标页码 */
  pageNumber: number
  /** 创建时间 */
  createdAt: Date
  /** 书签颜色 */
  color?: string
  /** 书签备注 */
  note?: string
}

/**
 * PDF阅读器状态
 */
export interface PdfReaderState {
  /** 当前文档 */
  document: PdfDocument | null
  /** 当前页码 */
  currentPage: number
  /** 缩放级别 */
  zoomLevel: number
  /** 旋转角度 */
  rotation: number
  /** 是否正在加载 */
  isLoading: boolean
  /** 错误信息 */
  error: string | null
  /** 搜索结果 */
  searchResults: PdfSearchResult[]
  /** 当前搜索关键词 */
  searchQuery: string
  /** 书签列表 */
  bookmarks: PdfBookmark[]
  /** 是否显示侧边栏 */
  showSidebar: boolean
  /** 侧边栏当前标签 */
  sidebarTab: 'thumbnails' | 'bookmarks' | 'search'
}

/**
 * PDF阅读器配置
 */
export interface PdfReaderConfig {
  /** 默认缩放级别 */
  defaultZoom: number
  /** 最小缩放级别 */
  minZoom: number
  /** 最大缩放级别 */
  maxZoom: number
  /** 缩放步长 */
  zoomStep: number
  /** 是否启用平滑滚动 */
  smoothScrolling: boolean
  /** 预加载页面数量 */
  preloadPages: number
  /** 缓存页面数量 */
  cachePages: number
  /** 渲染质量 */
  renderQuality: 'low' | 'medium' | 'high'
  /** 是否启用文本选择 */
  enableTextSelection: boolean
  /** 是否启用右键菜单 */
  enableContextMenu: boolean
}

/**
 * PDF加载器接口
 */
export interface IPdfLoader {
  /** 从文件路径加载PDF */
  loadFromPath(filePath: string): Promise<PdfDocument>
  /** 从Buffer加载PDF */
  loadFromBuffer(buffer: ArrayBuffer, fileName?: string): Promise<PdfDocument>
  /** 获取页面数据 */
  getPageData(document: PdfDocument, pageNumber: number): Promise<PdfPageData>
  /** 获取文档元数据 */
  getMetadata(document: PdfDocument): Promise<Partial<PdfDocument>>
  /** 释放文档资源 */
  dispose(document: PdfDocument): void
}

/**
 * PDF渲染器接口
 */
export interface IPdfRenderer {
  /** 渲染页面到Canvas */
  renderPage(
    canvas: HTMLCanvasElement,
    pageData: PdfPageData,
    options: PdfRenderOptions
  ): Promise<void>
  /** 渲染缩略图 */
  renderThumbnail(
    pageData: PdfPageData,
    width: number,
    height: number
  ): Promise<string>
  /** 获取页面文本 */
  getPageText(pageData: PdfPageData): string
  /** 搜索文本 */
  searchInPage(pageData: PdfPageData, query: string): PdfSearchResult[]
}

/**
 * PDF事件类型
 */
export interface PdfReaderEvents {
  /** 文档加载完成 */
  onDocumentLoaded: (document: PdfDocument) => void
  /** 页面变化 */
  onPageChanged: (pageNumber: number) => void
  /** 缩放变化 */
  onZoomChanged: (zoomLevel: number) => void
  /** 搜索完成 */
  onSearchCompleted: (results: PdfSearchResult[]) => void
  /** 错误发生 */
  onError: (error: Error) => void
}

/**
 * Web Worker消息类型
 */
export interface PdfWorkerMessage {
  id: string
  type: 'LOAD_PDF' | 'GET_PAGE' | 'SEARCH_TEXT' | 'RENDER_THUMBNAIL'
  data: any
}

/**
 * Web Worker响应类型
 */
export interface PdfWorkerResponse {
  id: string
  type: 'SUCCESS' | 'ERROR'
  data: any
  error?: string
}

/**
 * PDF缓存项
 */
export interface PdfCacheItem {
  /** 缓存键 */
  key: string
  /** 缓存数据 */
  data: any
  /** 创建时间 */
  timestamp: number
  /** 访问次数 */
  accessCount: number
  /** 数据大小（字节） */
  size: number
}

/**
 * PDF性能指标
 */
export interface PdfPerformanceMetrics {
  /** 文档加载时间 */
  loadTime: number
  /** 页面渲染时间 */
  renderTime: number
  /** 内存使用量 */
  memoryUsage: number
  /** 缓存命中率 */
  cacheHitRate: number
}

/**
 * vue3-pdf-app特定类型定义
 */

/**
 * vue3-pdf-app应用实例接口
 */
export interface Vue3PdfAppInstance {
  /** PDF文档对象 */
  pdfDocument?: any
  /** PDF查看器对象 */
  pdfViewer?: any
  /** PDF查找控制器 */
  pdfFindController?: any
  /** 事件总线 */
  eventBus?: any
}

/**
 * vue3-pdf-app配置接口
 */
export interface Vue3PdfAppConfig {
  /** 工具栏配置 */
  toolbar?: {
    toolbarViewerLeft?: Record<string, boolean>
    toolbarViewerMiddle?: Record<string, boolean>
    toolbarViewerRight?: Record<string, boolean>
  }
  /** 密码回调函数 */
  passwordCallback?: (callback: (password: string) => void, reason: string) => void
  /** 错误回调函数 */
  errorCallback?: (error: any) => void
}

/**
 * vue3-pdf-app主题类型
 */
export type Vue3PdfAppTheme = 'light' | 'dark'

/**
 * vue3-pdf-app页面缩放类型
 */
export type Vue3PdfAppPageScale = number | 'page-width' | 'page-fit' | 'page-actual' | 'auto'
