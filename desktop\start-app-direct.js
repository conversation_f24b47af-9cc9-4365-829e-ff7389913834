/**
 * 直接启动Electron应用
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 启动 Yu Reader 桌面应用...');

// 设置环境变量
process.env.NODE_ENV = 'development';

// 检查必要文件
const mainFile = path.join(__dirname, 'dist', 'main', 'app.js');
const preloadFile = path.join(__dirname, 'dist', 'preload', 'index.js');

if (!fs.existsSync(mainFile)) {
    console.error('❌ 主进程文件不存在:', mainFile);
    console.log('请先运行: npm run dev');
    process.exit(1);
}

if (!fs.existsSync(preloadFile)) {
    console.error('❌ 预加载脚本不存在:', preloadFile);
    console.log('请先运行: npm run dev');
    process.exit(1);
}

console.log('✅ 文件检查通过');
console.log('📱 启动Electron应用...');

// 尝试多种Electron路径
const electronPaths = [
    path.join(__dirname, 'node_modules', 'electron', 'dist', 'electron.exe'),
    path.join(__dirname, 'node_modules', '.bin', 'electron.cmd'),
    path.join(__dirname, 'node_modules', '.bin', 'electron'),
    'electron'
];

let electronPath = null;
for (const ePath of electronPaths) {
    if (fs.existsSync(ePath)) {
        electronPath = ePath;
        break;
    }
}

if (!electronPath) {
    console.error('❌ 找不到Electron可执行文件');
    console.log('尝试的路径:', electronPaths);
    process.exit(1);
}

console.log('✅ 使用Electron路径:', electronPath);

// 启动Electron
const child = spawn(electronPath, ['.'], {
    stdio: 'inherit',
    cwd: __dirname,
    env: {
        ...process.env,
        NODE_ENV: 'development'
    },
    shell: true // 使用shell来执行，这样可以处理.cmd文件
});

child.on('error', (error) => {
    console.error('❌ 启动失败:', error.message);
    if (error.code === 'ENOENT') {
        console.log('💡 建议: 尝试重新安装依赖 npm install');
    }
});

child.on('close', (code) => {
    console.log(`📱 应用退出，代码: ${code}`);
});

// 处理进程退出
process.on('SIGINT', () => {
    console.log('\n🛑 收到退出信号，关闭应用...');
    child.kill('SIGINT');
});

// 5秒后检查是否成功启动
setTimeout(() => {
    if (child.exitCode === null) {
        console.log('✅ Electron应用似乎已成功启动');
    }
}, 5000);
