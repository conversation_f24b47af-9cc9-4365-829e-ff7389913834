<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src\backend">
      <UniqueIdentifier>{0601BD18-2FE3-2D4A-0C05-611A0F36D709}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src\backend">
      <UniqueIdentifier>{0601BD18-2FE3-2D4A-0C05-611A0F36D709}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src\backend">
      <UniqueIdentifier>{0601BD18-2FE3-2D4A-0C05-611A0F36D709}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src\backend">
      <UniqueIdentifier>{0601BD18-2FE3-2D4A-0C05-611A0F36D709}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src\bmp">
      <UniqueIdentifier>{C08C95BF-9646-DB44-5C81-9CB5B5F652A5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="D:">
      <UniqueIdentifier>{E5D29F2B-0177-9942-CA95-0FF700094F89}</UniqueIdentifier>
    </Filter>
    <Filter Include="D:\reader">
      <UniqueIdentifier>{E7F19F8E-2822-C132-F81E-19A3F0FA48A5}</UniqueIdentifier>
    </Filter>
    <Filter Include="D:\reader\desktop">
      <UniqueIdentifier>{FC4CCBFB-F86E-DBC0-093A-D8F87293D843}</UniqueIdentifier>
    </Filter>
    <Filter Include="D:\reader\desktop\node_modules">
      <UniqueIdentifier>{56DF7A98-063D-FB9D-485C-089023B4C16A}</UniqueIdentifier>
    </Filter>
    <Filter Include="D:\reader\desktop\node_modules\@electron">
      <UniqueIdentifier>{6FD122B1-87CE-5BE7-127F-692B6E499EA3}</UniqueIdentifier>
    </Filter>
    <Filter Include="D:\reader\desktop\node_modules\@electron\node-gyp">
      <UniqueIdentifier>{77348C0E-2034-7791-74D5-63C077DF5A3B}</UniqueIdentifier>
    </Filter>
    <Filter Include="D:\reader\desktop\node_modules\@electron\node-gyp\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\backend\Backend.cc">
      <Filter>..\src\backend</Filter>
    </ClCompile>
    <ClCompile Include="..\src\backend\ImageBackend.cc">
      <Filter>..\src\backend</Filter>
    </ClCompile>
    <ClCompile Include="..\src\backend\PdfBackend.cc">
      <Filter>..\src\backend</Filter>
    </ClCompile>
    <ClCompile Include="..\src\backend\SvgBackend.cc">
      <Filter>..\src\backend</Filter>
    </ClCompile>
    <ClCompile Include="..\src\bmp\BMPParser.cc">
      <Filter>..\src\bmp</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Backends.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Canvas.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\CanvasGradient.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\CanvasPattern.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\CanvasRenderingContext2d.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\closure.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\color.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Image.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\ImageData.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\init.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\register_font.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="..\src\FontParser.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="D:\reader\desktop\node_modules\@electron\node-gyp\src\win_delay_load_hook.cc">
      <Filter>D:\reader\desktop\node_modules\@electron\node-gyp\src</Filter>
    </ClCompile>
    <None Include="..\binding.gyp">
      <Filter>..</Filter>
    </None>
  </ItemGroup>
</Project>
