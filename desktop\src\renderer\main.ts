/**
 * Vue应用主入口文件
 * 初始化Vue应用、路由、状态管理和UI框架
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './App.vue'
import router from './router'
import './styles/index.css'
import { appReady } from './composables/useAppReady'

// 创建Vue应用实例
const app = createApp(App)

// 创建Pinia状态管理
const pinia = createPinia()

// 使用插件
app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue应用错误:', err, info)
}

// 开发环境下的调试信息
if (process.env.NODE_ENV === 'development') {
  console.log('Yu Reader 开发模式启动')
  console.log('Electron API:', window.electronAPI)
}

// 应用初始化和挂载函数
const initializeAndMountApp = async () => {
  try {
    console.log('开始应用初始化流程...')

    // PDF.js配置已由vue3-pdf-app自动处理

    // 显示初始化加载状态
    const loadingElement = document.getElementById('app')
    if (loadingElement) {
      loadingElement.innerHTML = `
        <div style="
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: 100vh;
          background-color: #f5f7fa;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
          <div style="
            width: 40px;
            height: 40px;
            border: 3px solid #e4e7ed;
            border-top: 3px solid #409eff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
          "></div>
          <div style="color: #606266; font-size: 14px;">正在初始化应用...</div>
          <style>
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          </style>
        </div>
      `
    }

    // 等待应用就绪状态初始化完成
    const success = await appReady.initializeApp()

    if (!success) {
      throw new Error('应用初始化失败')
    }

    console.log('应用初始化完成，开始挂载Vue应用...')

    // 挂载Vue应用
    app.mount('#app')

    console.log('Vue应用挂载完成')

  } catch (error) {
    console.error('应用初始化失败:', error)

    // 显示错误信息
    const errorElement = document.getElementById('app')
    if (errorElement) {
      errorElement.innerHTML = `
        <div style="
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: 100vh;
          background-color: #f5f7fa;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          text-align: center;
          padding: 20px;
        ">
          <div style="color: #f56c6c; font-size: 18px; margin-bottom: 16px;">应用初始化失败</div>
          <div style="color: #909399; font-size: 14px; margin-bottom: 20px;">${error.message}</div>
          <button onclick="location.reload()" style="
            padding: 8px 16px;
            background-color: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          ">重新加载</button>
        </div>
      `
    }
  }
}

// 延迟启动以确保Electron API完全加载
setTimeout(initializeAndMountApp, 100)
