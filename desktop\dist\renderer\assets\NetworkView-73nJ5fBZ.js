import{d as t,e as a,c as n,f as e,a as c,r,o as p,_}from"./index-DS8Rgqx0.js";const d={class:"network-view"},i={class:"network-content"},l=t({__name:"NetworkView",setup(m){return a(()=>{console.log("网络设置页面已加载")}),(f,o)=>{const s=r("el-empty");return p(),n("div",d,[o[0]||(o[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"网络设置"),e("p",{class:"page-description"},"配置网络连接")],-1)),e("div",i,[c(s,{description:"网络设置功能开发中..."})])])}}}),k=_(l,[["__scopeId","data-v-f678277a"]]);export{k as default};
