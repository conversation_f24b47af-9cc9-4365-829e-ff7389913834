"use strict";var Gn=Object.defineProperty;var Zn=(s,e,t)=>e in s?Gn(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var R=(s,e,t)=>Zn(s,typeof e!="symbol"?e+"":e,t);const O=require("electron"),B=require("path"),$=require("fs"),Yn=require("better-sqlite3"),vn=require("crypto"),Cn=require("buffer"),In=require("zlib"),Kn=require("events");class Jn{constructor(){R(this,"mainWindow",null)}async createMainWindow(){const e=O.screen.getPrimaryDisplay(),{width:t,height:r}=e.workAreaSize,o=Math.min(1200,Math.floor(t*.8)),n=Math.min(800,Math.floor(r*.8)),i=Math.floor((t-o)/2),a=Math.floor((r-n)/2);this.mainWindow=new O<PERSON>BrowserWindow({width:o,height:n,x:i,y:a,minWidth:800,minHeight:600,show:!0,frame:!0,titleBarStyle:process.platform==="darwin"?"hiddenInset":"default",webPreferences:{nodeIntegration:!1,contextIsolation:!0,enableRemoteModule:!1,preload:B.join(__dirname,"../preload/index.js"),webSecurity:!1,allowRunningInsecureContent:!0,experimentalFeatures:!1,sandbox:!1,spellcheck:!1},icon:this.getAppIcon()}),this.mainWindow.once("ready-to-show",()=>{var f,m,y,w,h,E,g,_,b;(f=this.mainWindow)==null||f.show(),(m=this.mainWindow)==null||m.focus(),(y=this.mainWindow)==null||y.moveTop(),(w=this.mainWindow)==null||w.setAlwaysOnTop(!0),(h=this.mainWindow)==null||h.center(),setTimeout(()=>{var u;(u=this.mainWindow)==null||u.setAlwaysOnTop(!1)},2e3),console.log("窗口已显示"),console.log("窗口位置:",(E=this.mainWindow)==null?void 0:E.getBounds()),console.log("窗口是否可见:",(g=this.mainWindow)==null?void 0:g.isVisible()),console.log("窗口是否最小化:",(_=this.mainWindow)==null?void 0:_.isMinimized()),(b=this.mainWindow)==null||b.webContents.reload()}),this.mainWindow.webContents.on("did-fail-load",(f,m,y,w)=>{var h;console.error("页面加载失败:",m,y,w),(h=this.mainWindow)==null||h.show()});const l=(process.env.NODE_ENV||"").trim();if(console.log("当前环境变量 NODE_ENV:",`"${process.env.NODE_ENV}"`),console.log("清理后的环境变量:",`"${l}"`),console.log("是否为开发环境:",l==="development"),l==="development"){const f="http://127.0.0.1:5173";console.log("开发模式，加载URL:",f),await this.mainWindow.loadURL(f),this.mainWindow.webContents.openDevTools()}else{const f=B.join(__dirname,"../renderer/index.html");console.log("生产模式，加载文件:",f),await this.mainWindow.loadFile(f)}return this.mainWindow.on("closed",()=>{this.mainWindow=null}),this.mainWindow.webContents.setWindowOpenHandler(({url:f})=>(O.shell.openExternal(f),{action:"deny"})),this.mainWindow.webContents.on("will-navigate",(f,m)=>{const y=new URL(m);y.protocol==="file:"||y.protocol==="http:"&&y.hostname==="127.0.0.1"||(f.preventDefault(),O.shell.openExternal(m))}),console.log("主窗口创建完成"),setTimeout(()=>{this.mainWindow&&!this.mainWindow.isVisible()&&(console.log("窗口仍然不可见，强制显示..."),this.mainWindow.show(),this.mainWindow.focus(),this.mainWindow.center(),this.mainWindow.setAlwaysOnTop(!0),setTimeout(()=>{var f;(f=this.mainWindow)==null||f.setAlwaysOnTop(!1)},3e3))},2e3),this.mainWindow}getMainWindow(){return this.mainWindow}minimizeWindow(){var e;(e=this.mainWindow)==null||e.minimize()}toggleMaximizeWindow(){var e,t;(e=this.mainWindow)!=null&&e.isMaximized()?this.mainWindow.unmaximize():(t=this.mainWindow)==null||t.maximize()}closeWindow(){var e;(e=this.mainWindow)==null||e.close()}toggleFullscreen(){var t,r;const e=((t=this.mainWindow)==null?void 0:t.isFullScreen())??!1;(r=this.mainWindow)==null||r.setFullScreen(!e)}getAppIcon(){const e=B.join(__dirname,"../../build");switch(process.platform){case"win32":return B.join(e,"icon.ico");case"darwin":return B.join(e,"icon.icns");case"linux":return B.join(e,"icon.png");default:return}}}class Qn{setupMenu(){const e=this.createMenuTemplate(),t=O.Menu.buildFromTemplate(e);O.Menu.setApplicationMenu(t)}sendThemeChange(e){const t=O.BrowserWindow.getFocusedWindow()||O.BrowserWindow.getAllWindows()[0];t&&(t.webContents.send("theme:change",e),console.log(`菜单: 发送主题切换消息 ${e}`))}createMenuTemplate(){const e=process.platform==="darwin";return[...e?[{label:O.app.getName(),submenu:[{role:"about"},{type:"separator"},{role:"services"},{type:"separator"},{role:"hide"},{role:"hideothers"},{role:"unhide"},{type:"separator"},{role:"quit"}]}]:[],{label:"文件",submenu:[{label:"打开书籍...",accelerator:"CmdOrCtrl+O",click:async()=>{await this.openBookFile()}},{label:"导入书籍文件夹...",accelerator:"CmdOrCtrl+Shift+O",click:async()=>{await this.importBookFolder()}},{type:"separator"},{label:"最近打开",submenu:[{label:"清除最近打开",click:()=>{}}]},{type:"separator"},...e?[]:[{role:"quit"}]]},{label:"编辑",submenu:[{role:"undo"},{role:"redo"},{type:"separator"},{role:"cut"},{role:"copy"},{role:"paste"},{role:"selectall"},{type:"separator"},{label:"查找...",accelerator:"CmdOrCtrl+F",click:()=>{}}]},{label:"视图",submenu:[{role:"reload"},{role:"forceReload"},{role:"toggleDevTools"},{type:"separator"},{role:"resetZoom"},{role:"zoomIn"},{role:"zoomOut"},{type:"separator"},{role:"togglefullscreen"},{type:"separator"},{label:"切换主题",submenu:[{label:"明亮主题",type:"radio",checked:!0,click:()=>{this.sendThemeChange("light")}},{label:"深色主题",type:"radio",click:()=>{this.sendThemeChange("dark")}},{label:"护眼主题",type:"radio",click:()=>{this.sendThemeChange("eye-care")}},{label:"护眼暖色主题",type:"radio",click:()=>{this.sendThemeChange("eye-care-warm")}},{label:"高对比度主题",type:"radio",click:()=>{this.sendThemeChange("high-contrast")}},{label:"夜间模式",type:"radio",click:()=>{this.sendThemeChange("night")}},{label:"自然模式",type:"radio",click:()=>{this.sendThemeChange("natural")}},{type:"separator"},{label:"跟随系统",type:"radio",click:()=>{this.sendThemeChange("auto")}}]}]},{label:"书签",submenu:[{label:"添加书签",accelerator:"CmdOrCtrl+D",click:()=>{}},{label:"管理书签...",accelerator:"CmdOrCtrl+Shift+B",click:()=>{}}]},{label:"工具",submenu:[{label:"设置...",accelerator:"CmdOrCtrl+,",click:()=>{}},{type:"separator"},{label:"导出笔记...",click:()=>{}},{label:"导入笔记...",click:()=>{}}]},{label:"窗口",submenu:[{role:"minimize"},{role:"close"},...e?[{type:"separator"},{role:"front"}]:[]]},{label:"帮助",submenu:[{label:"关于 Yu Reader",click:async()=>{await this.showAboutDialog()}},{label:"用户手册",click:async()=>{await O.shell.openExternal("https://github.com/yu-reader/desktop/wiki")}},{label:"报告问题",click:async()=>{await O.shell.openExternal("https://github.com/yu-reader/desktop/issues")}}]}]}async openBookFile(){const e=await O.dialog.showOpenDialog({title:"选择书籍文件",filters:[{name:"电子书文件",extensions:["epub","txt","mobi"]},{name:"EPUB",extensions:["epub"]},{name:"文本文件",extensions:["txt"]},{name:"MOBI",extensions:["mobi"]},{name:"所有文件",extensions:["*"]}],properties:["openFile","multiSelections"]});!e.canceled&&e.filePaths.length>0&&console.log("选中的文件:",e.filePaths)}async importBookFolder(){const e=await O.dialog.showOpenDialog({title:"选择书籍文件夹",properties:["openDirectory"]});!e.canceled&&e.filePaths.length>0&&console.log("选中的文件夹:",e.filePaths[0])}async showAboutDialog(){await O.dialog.showMessageBox({type:"info",title:"关于 Yu Reader",message:"Yu Reader",detail:`版本: ${O.app.getVersion()}

一个现代化的电子书阅读器，支持多种格式的电子书阅读。

© 2024 Yu Reader Team`,buttons:["确定"]})}}let Se;const ei=new Uint8Array(16);function ti(){if(!Se&&(Se=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Se))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Se(ei)}const ne=[];for(let s=0;s<256;++s)ne.push((s+256).toString(16).slice(1));function ri(s,e=0){return ne[s[e+0]]+ne[s[e+1]]+ne[s[e+2]]+ne[s[e+3]]+"-"+ne[s[e+4]]+ne[s[e+5]]+"-"+ne[s[e+6]]+ne[s[e+7]]+"-"+ne[s[e+8]]+ne[s[e+9]]+"-"+ne[s[e+10]]+ne[s[e+11]]+ne[s[e+12]]+ne[s[e+13]]+ne[s[e+14]]+ne[s[e+15]]}const ni=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),hr={randomUUID:ni};function ii(s,e,t){if(hr.randomUUID&&!s)return hr.randomUUID();s=s||{};const r=s.random||(s.rng||ti)();return r[6]=r[6]&15|64,r[8]=r[8]&63|128,ri(r)}class oi{constructor(){R(this,"db",null);R(this,"dbPath");R(this,"oldDbPath");this.dbPath=this.getDatabasePath(),this.oldDbPath="",console.log("数据库路径:",this.dbPath)}getDatabasePath(){if(process.env.DB_PATH){const t=B.resolve(process.env.DB_PATH);return console.log("使用环境变量指定的数据库路径:",t),t}const e=B.resolve(process.cwd(),"database","yu-reader.db");return console.log("使用项目相对路径:",e),e}findOldDatabasePath(){const e=O.app.getPath("userData"),t=[B.join(e,"yureader.db"),B.join(e,"yu-reader.db")];let r=t[0],o=0;for(const n of t)if($.existsSync(n))try{const i=require("fs").statSync(n);i.size>o&&(o=i.size,r=n)}catch(i){console.warn("检查文件大小失败:",n,i.message)}return r}ensureDatabaseDirectory(){const e=B.join(this.dbPath,"..");$.existsSync(e)||($.mkdirSync(e,{recursive:!0}),console.log("创建数据库目录:",e))}migrateOldDatabase(){if($.existsSync(this.dbPath)){console.log("新位置已存在数据库文件，跳过迁移");return}if($.existsSync(this.oldDbPath))try{console.log("检测到旧数据库文件，开始迁移..."),console.log("从:",this.oldDbPath),console.log("到:",this.dbPath),this.ensureDatabaseDirectory(),$.copyFileSync(this.oldDbPath,this.dbPath),console.log("数据库迁移完成")}catch(e){console.error("数据库迁移失败:",e),console.log("将使用新位置创建新数据库")}else console.log("未检测到旧数据库文件，将在新位置创建数据库")}async initialize(){try{return this.oldDbPath=this.findOldDatabasePath(),console.log("旧数据库路径:",this.oldDbPath),this.ensureDatabaseDirectory(),this.migrateOldDatabase(),this.db=new Yn(this.dbPath,{verbose:process.env.NODE_ENV==="development"?console.log:void 0}),console.log("数据库连接已建立"),await this.createTables(),console.log("数据库表创建完成"),await this.importTestDataIfNeeded(),Promise.resolve()}catch(e){return console.error("数据库初始化失败:",e),Promise.reject(e)}}async createTables(){if(!this.db)throw new Error("数据库未初始化");await this.migrateBooksTable(),await this.createOtherTables()}async migrateBooksTable(){if(!this.db)throw new Error("数据库未初始化");try{if(this.db.prepare(`
        SELECT name FROM sqlite_master WHERE type='table' AND name='books'
      `).get()){const r=this.db.prepare("PRAGMA table_info(books)").all().map(a=>a.name),n=["id","title","author","isbn","file_path","file_format","file_size","cover_image","description","publisher","publish_date","language","total_pages","word_count","reading_status","current_page","reading_progress_percent","last_read_time","import_time","tags","metadata_json","created_at","updated_at","deleted_at","sync_version"].filter(a=>!r.includes(a)),i=r.includes("format")&&!r.includes("file_format");n.length>0||i?(console.log("检测到旧的books表结构，开始迁移..."),await this.migrateOldBooksTable()):console.log("books表结构已是最新版本")}else console.log("创建新的books表..."),await this.createNewBooksTable()}catch(e){throw console.error("books表迁移失败:",e),e}}async migrateOldBooksTable(){if(!this.db)throw new Error("数据库未初始化");console.log("开始迁移books表结构..."),this.db.exec("CREATE TABLE books_backup AS SELECT * FROM books"),this.db.exec("DROP TABLE books"),await this.createNewBooksTable();try{const e=this.db.prepare("SELECT * FROM books_backup").all();if(e.length>0){console.log(`迁移 ${e.length} 条图书数据...`);const t=this.db.prepare(`
          INSERT INTO books (
            title, author, file_path, file_format, file_size, description,
            reading_status, current_page, reading_progress_percent,
            created_at, updated_at, deleted_at, sync_version
          ) VALUES (
            @title, @author, @file_path, @file_format, @file_size, @description,
            @reading_status, @current_page, @reading_progress_percent,
            @created_at, @updated_at, @deleted_at, @sync_version
          )
        `);for(const r of e){const o={title:r.title||"",author:r.author||"",file_path:r.file_path||"",file_format:r.format||r.file_format||"",file_size:r.file_size||0,description:r.description||"",reading_status:r.status||r.reading_status||"unread",current_page:r.current_page||0,reading_progress_percent:r.progress||r.reading_progress_percent||0,created_at:r.created_at||new Date().toISOString(),updated_at:r.updated_at||new Date().toISOString(),deleted_at:r.deleted_at||null,sync_version:r.sync_version||1};t.run(o)}console.log("数据迁移完成")}}catch(e){console.error("数据迁移失败:",e)}this.db.exec("DROP TABLE books_backup"),console.log("books表迁移完成")}async createNewBooksTable(){if(!this.db)throw new Error("数据库未初始化");this.db.exec(`
      CREATE TABLE books (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT,
        author TEXT,
        isbn TEXT,
        file_path TEXT,
        file_format TEXT,
        file_size INTEGER,
        cover_image TEXT,
        description TEXT,
        publisher TEXT,
        publish_date TEXT,
        language TEXT,
        total_pages INTEGER,
        word_count INTEGER,
        reading_status TEXT,
        current_page INTEGER,
        reading_progress_percent REAL,
        last_read_time DATETIME,
        import_time DATETIME,
        tags TEXT,
        metadata_json TEXT,
        created_at DATETIME,
        updated_at DATETIME,
        deleted_at DATETIME,
        sync_version INTEGER
      )
    `),this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_books_title ON books (title);
      CREATE INDEX IF NOT EXISTS idx_books_author ON books (author);
      CREATE INDEX IF NOT EXISTS idx_books_isbn ON books (isbn);
      CREATE UNIQUE INDEX IF NOT EXISTS idx_books_file_path ON books (file_path);
      CREATE INDEX IF NOT EXISTS idx_books_file_format ON books (file_format);
      CREATE INDEX IF NOT EXISTS idx_books_reading_status ON books (reading_status);
      CREATE INDEX IF NOT EXISTS idx_books_language ON books (language);
      CREATE INDEX IF NOT EXISTS idx_books_publisher ON books (publisher);
      CREATE INDEX IF NOT EXISTS idx_books_import_time ON books (import_time);
      CREATE INDEX IF NOT EXISTS idx_books_last_read_time ON books (last_read_time);
      CREATE INDEX IF NOT EXISTS idx_books_created_at ON books (created_at);
      CREATE INDEX IF NOT EXISTS idx_books_updated_at ON books (updated_at);
      CREATE INDEX IF NOT EXISTS idx_books_deleted_at ON books (deleted_at);
      CREATE INDEX IF NOT EXISTS idx_books_sync_version ON books (sync_version);
    `)}async createOtherTables(){if(!this.db)throw new Error("数据库未初始化");this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        email TEXT,
        avatar TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `),this.db.exec(`
      CREATE TABLE IF NOT EXISTS bookmarks (
        id TEXT PRIMARY KEY,
        book_id TEXT NOT NULL,
        chapter_id TEXT,
        type TEXT NOT NULL DEFAULT 'bookmark',
        title TEXT NOT NULL,
        content TEXT,
        position TEXT NOT NULL,
        color TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT,
        sync_version INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
      )
    `),this.db.exec(`
      CREATE TABLE IF NOT EXISTS notes (
        id TEXT PRIMARY KEY,
        book_id TEXT NOT NULL,
        chapter_id TEXT,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        position TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT,
        sync_version INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
      )
    `),this.db.exec(`
      CREATE TABLE IF NOT EXISTS learning_tasks (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        book_id TEXT,
        status TEXT NOT NULL DEFAULT 'pending',
        due_date TEXT,
        completed_at TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT,
        sync_version INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE SET NULL
      )
    `),this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        category TEXT NOT NULL,
        description TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `)}async importTestDataIfNeeded(){if(!this.db)throw new Error("数据库未初始化");try{if(this.db.prepare("SELECT COUNT(*) as count FROM books").get().count>0){console.log("数据库已有数据，跳过测试数据导入");return}console.log("正在导入测试数据...");const r=process.cwd(),o=B.resolve(r,"desktop","test-books"),n=[{title:"三体",author:"刘慈欣",isbn:"978-7-536-69293-0",file_path:B.resolve(o,"三体.txt"),file_format:"txt",file_size:1024e3,cover_image:null,description:"科幻小说经典之作，描述了人类文明与三体文明的第一次接触。",publisher:"重庆出版社",publish_date:"2006-05-01",language:"zh-CN",total_pages:180,word_count:25e4,reading_status:"reading",current_page:45,reading_progress_percent:25.5,last_read_time:new Date().toISOString(),tags:JSON.stringify(["科幻","小说","刘慈欣","三体"]),metadata_json:JSON.stringify({difficulty:"intermediate",category:"science-fiction",series:"three-body",rating:4.9}),sync_version:1},{title:"活着",author:"余华",isbn:"978-7-5063-4188-5",file_path:B.resolve(o,"活着.txt"),file_format:"txt",file_size:512e3,cover_image:null,description:"一个关于生存与坚韧的感人故事。",publisher:"作家出版社",publish_date:"1993-01-01",language:"zh-CN",total_pages:120,word_count:18e4,reading_status:"finished",current_page:120,reading_progress_percent:100,last_read_time:new Date(Date.now()-864e5).toISOString(),tags:JSON.stringify(["文学","小说","余华","现实主义"]),metadata_json:JSON.stringify({difficulty:"easy",category:"literature",series:null,rating:4.8}),sync_version:1},{title:"百年孤独",author:"加西亚·马尔克斯",isbn:"978-7-5447-4095-2",file_path:B.resolve(o,"百年孤独.txt"),file_format:"txt",file_size:768e3,cover_image:null,description:"魔幻现实主义的代表作品。",publisher:"南海出版公司",publish_date:"2011-06-01",language:"zh-CN",total_pages:200,word_count:3e5,reading_status:"unread",current_page:0,reading_progress_percent:0,last_read_time:null,tags:JSON.stringify(["文学","小说","魔幻现实主义","马尔克斯"]),metadata_json:JSON.stringify({difficulty:"advanced",category:"literature",series:null,rating:4.9}),sync_version:1},{title:"1984",author:"乔治·奥威尔",isbn:"978-7-5327-4657-5",file_path:B.resolve(o,"1984.txt"),file_format:"txt",file_size:64e4,cover_image:null,description:"反乌托邦小说的经典之作。",publisher:"上海译文出版社",publish_date:"2010-04-01",language:"zh-CN",total_pages:150,word_count:22e4,reading_status:"reading",current_page:90,reading_progress_percent:60,last_read_time:new Date(Date.now()-36e5).toISOString(),tags:JSON.stringify(["小说","反乌托邦","政治","奥威尔"]),metadata_json:JSON.stringify({difficulty:"intermediate",category:"fiction",series:null,rating:4.7}),sync_version:1},{title:"红楼梦",author:"曹雪芹",isbn:"978-7-02-008737-8",file_path:B.resolve(o,"红楼梦.txt"),file_format:"txt",file_size:2048e3,cover_image:null,description:"中国古典文学四大名著之一。",publisher:"人民文学出版社",publish_date:"2008-07-01",language:"zh-CN",total_pages:500,word_count:8e5,reading_status:"reading",current_page:80,reading_progress_percent:15,last_read_time:new Date(Date.now()-72e5).toISOString(),tags:JSON.stringify(["古典文学","小说","四大名著","曹雪芹"]),metadata_json:JSON.stringify({difficulty:"advanced",category:"classical-literature",series:"four-great-novels",rating:4.9}),sync_version:1}],a=this.db.prepare("PRAGMA table_info(books)").all().map(y=>y.name);console.log("books表字段:",a);const f=this.db.prepare(`
        INSERT INTO books (
          title, author, isbn, file_path, file_format, file_size,
          cover_image, description, publisher, publish_date, language,
          total_pages, word_count, reading_status, current_page,
          reading_progress_percent, last_read_time, tags, metadata_json,
          sync_version
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);let m=0;for(const y of n)try{f.run(y.title,y.author,y.isbn,y.file_path,y.file_format,y.file_size,y.cover_image,y.description,y.publisher,y.publish_date,y.language,y.total_pages,y.word_count,y.reading_status,y.current_page,y.reading_progress_percent,y.last_read_time,y.tags,y.metadata_json,y.sync_version),m++}catch(w){console.error(`插入测试数据失败: ${y.title}`,w)}console.log(`测试数据导入完成，成功导入 ${m} 本图书`)}catch(e){console.error("导入测试数据失败:",e)}}getDatabase(){if(!this.db)throw new Error("数据库未初始化");return this.db}getDatabaseFilePath(){return this.dbPath}getOldDatabaseFilePath(){return this.oldDbPath}async close(){return this.db&&(this.db.close(),this.db=null,console.log("数据库连接已关闭")),Promise.resolve()}getBooks(){if(!this.db)throw new Error("数据库未初始化");return this.db.prepare("SELECT * FROM books ORDER BY updated_at DESC").all()}getBook(e){if(!this.db)throw new Error("数据库未初始化");return this.db.prepare("SELECT * FROM books WHERE id = ?").get(e)}addBook(e){if(!this.db)throw new Error("数据库未初始化");const t=new Date().toISOString();return this.db.prepare(`
      INSERT INTO books (
        title, author, isbn, file_path, file_format, file_size,
        cover_image, description, publisher, publish_date, language,
        total_pages, word_count, reading_status, current_page,
        reading_progress_percent, last_read_time, import_time,
        tags, metadata_json, created_at, updated_at, sync_version
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(e.title||null,e.author||null,e.isbn||null,e.file_path||null,e.file_format||null,e.file_size||null,e.cover_image||null,e.description||null,e.publisher||null,e.publish_date||null,e.language||null,e.total_pages||null,e.word_count||null,e.reading_status||null,e.current_page||null,e.reading_progress_percent||null,e.last_read_time||null,e.import_time||t,e.tags||null,e.metadata_json||null,t,t,e.sync_version||1).lastInsertRowid}updateBook(e,t){if(!this.db)throw new Error("数据库未初始化");const r=new Date().toISOString(),o=[],n=[];if(t.title!==void 0&&(o.push("title = ?"),n.push(t.title)),t.author!==void 0&&(o.push("author = ?"),n.push(t.author)),t.isbn!==void 0&&(o.push("isbn = ?"),n.push(t.isbn)),t.file_path!==void 0&&(o.push("file_path = ?"),n.push(t.file_path)),t.file_format!==void 0&&(o.push("file_format = ?"),n.push(t.file_format)),t.file_size!==void 0&&(o.push("file_size = ?"),n.push(t.file_size)),t.cover_image!==void 0&&(o.push("cover_image = ?"),n.push(t.cover_image)),t.description!==void 0&&(o.push("description = ?"),n.push(t.description)),t.publisher!==void 0&&(o.push("publisher = ?"),n.push(t.publisher)),t.publish_date!==void 0&&(o.push("publish_date = ?"),n.push(t.publish_date)),t.language!==void 0&&(o.push("language = ?"),n.push(t.language)),t.total_pages!==void 0&&(o.push("total_pages = ?"),n.push(t.total_pages)),t.word_count!==void 0&&(o.push("word_count = ?"),n.push(t.word_count)),t.reading_status!==void 0&&(o.push("reading_status = ?"),n.push(t.reading_status)),t.current_page!==void 0&&(o.push("current_page = ?"),n.push(t.current_page)),t.reading_progress_percent!==void 0&&(o.push("reading_progress_percent = ?"),n.push(t.reading_progress_percent)),t.last_read_time!==void 0&&(o.push("last_read_time = ?"),n.push(t.last_read_time)),t.tags!==void 0&&(o.push("tags = ?"),n.push(t.tags)),t.metadata_json!==void 0&&(o.push("metadata_json = ?"),n.push(t.metadata_json)),t.deleted_at!==void 0&&(o.push("deleted_at = ?"),n.push(t.deleted_at)),t.sync_version!==void 0&&(o.push("sync_version = ?"),n.push(t.sync_version)),o.push("updated_at = ?"),n.push(r),o.length===1)return!1;const i=this.db.prepare(`
      UPDATE books SET ${o.join(", ")}
      WHERE id = ?
    `);return n.push(e),i.run(...n).changes>0}deleteBook(e){if(!this.db)throw new Error("数据库未初始化");return this.db.prepare("DELETE FROM books WHERE id = ?").run(e).changes>0}getBookmarks(e){if(!this.db)throw new Error("数据库未初始化");return this.db.prepare("SELECT * FROM bookmarks WHERE book_id = ? ORDER BY created_at DESC").all(e)}addBookmark(e){if(!this.db)throw new Error("数据库未初始化");const t=ii(),r=Date.now();return this.db.prepare(`
      INSERT INTO bookmarks (
        id, book_id, position, title, content, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run(t,e.bookId,e.position,e.title||"",e.content||"",r,r),t}getSetting(e){if(!this.db)throw new Error("数据库未初始化");const r=this.db.prepare("SELECT value FROM settings WHERE key = ?").get(e);return r?JSON.parse(r.value):null}saveSetting(e,t){if(!this.db)throw new Error("数据库未初始化");return this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value, updated_at)
      VALUES (?, ?, ?)
    `).run(e,JSON.stringify(t),Date.now()).changes>0}}class si{constructor(){R(this,"tempDir");R(this,"coversDir");const e=O.app.getPath("userData");this.tempDir=B.join(e,"temp"),this.coversDir=B.join(e,"covers")}async initialize(){try{await this.ensureDirectoryExists(this.tempDir),await this.ensureDirectoryExists(this.coversDir),await this.cleanupTempFiles(),console.log("文件管理器初始化完成")}catch(e){throw console.error("文件管理器初始化失败:",e),e}}async ensureDirectoryExists(e){try{await $.promises.access(e)}catch{await $.promises.mkdir(e,{recursive:!0})}}async readFile(e){try{if(!$.existsSync(e))throw new Error(`文件不存在: ${e}`);return await $.promises.readFile(e)}catch(t){throw console.error("读取文件失败:",t),t}}async writeFile(e,t){try{const r=B.dirname(e);await this.ensureDirectoryExists(r),await $.promises.writeFile(e,t),console.log(`文件写入成功: ${e}`)}catch(r){throw console.error("写入文件失败:",r),r}}async fileExists(e){try{let t=e;if(e.startsWith("/books/"))t=e.substring(1),console.log(`FileManager: Unix路径格式修复 ${e} -> ${t}`);else if(e.includes("/reader/desktop/books/")){const r=e.indexOf("/books/");t=e.substring(r+1),console.log(`FileManager: Windows绝对路径格式修复 ${e} -> ${t}`)}else if(e.includes("\\reader\\desktop\\books\\")){const r=e.indexOf("\\books\\");t=e.substring(r+1).replace(/\\/g,"/"),console.log(`FileManager: Windows反斜杠路径格式修复 ${e} -> ${t}`)}return await $.promises.access(t),!0}catch{return!1}}async getFileInfo(e){try{let t=e;if(e.startsWith("/books/"))t=e.substring(1),console.log(`FileManager: Unix路径格式修复 ${e} -> ${t}`);else if(e.includes("/reader/desktop/books/")){const a=e.indexOf("/books/");t=e.substring(a+1),console.log(`FileManager: Windows绝对路径格式修复 ${e} -> ${t}`)}else if(e.includes("\\reader\\desktop\\books\\")){const a=e.indexOf("\\books\\");t=e.substring(a+1).replace(/\\/g,"/"),console.log(`FileManager: Windows反斜杠路径格式修复 ${e} -> ${t}`)}const r=$.statSync(t),o=this.getFileFormat(t),n=B.basename(t,B.extname(t)),i=await this.calculateFileHash(t);return{size:r.size,format:o,name:n,hash:i}}catch(t){throw console.error("获取文件信息失败:",t),t}}getFileFormat(e){const t=B.extname(e).toLowerCase();switch(t){case".epub":return"epub";case".pdf":return"pdf";case".txt":return"txt";case".mobi":return"mobi";default:throw new Error(`不支持的文件格式: ${t}`)}}async calculateFileHash(e){try{const t=await $.promises.readFile(e);return vn.createHash("md5").update(t).digest("hex")}catch(t){throw console.error("计算文件哈希失败:",t),t}}async selectFiles(e){try{const t=[{name:"电子书文件",extensions:["epub","pdf","txt","mobi"]},{name:"EPUB",extensions:["epub"]},{name:"PDF",extensions:["pdf"]},{name:"文本文件",extensions:["txt"]},{name:"MOBI",extensions:["mobi"]},{name:"所有文件",extensions:["*"]}],r=await O.dialog.showOpenDialog({title:"选择电子书文件",filters:e||t,properties:["openFile","multiSelections"]});return r.canceled?[]:r.filePaths}catch(t){throw console.error("选择文件失败:",t),t}}async selectFolder(){try{const e=await O.dialog.showOpenDialog({title:"选择文件夹",properties:["openDirectory"]});return e.canceled?null:e.filePaths[0]}catch(e){throw console.error("选择文件夹失败:",e),e}}async scanBooksInFolder(e){try{const t=[".epub",".txt",".mobi"],r=[],o=async n=>{const i=await $.promises.readdir(n,{withFileTypes:!0});for(const a of i){const l=B.join(n,a.name);if(a.isDirectory())await o(l);else if(a.isFile()){const f=B.extname(a.name).toLowerCase();t.includes(f)&&r.push(l)}}};return await o(e),r}catch(t){throw console.error("扫描文件夹失败:",t),t}}async saveCover(e,t,r="jpg"){try{const o=`${e}.${r}`,n=B.join(this.coversDir,o);return await $.promises.writeFile(n,t),n}catch(o){throw console.error("保存封面失败:",o),o}}async deleteCover(e){try{await this.fileExists(e)&&await $.promises.unlink(e)}catch(t){console.error("删除封面失败:",t)}}async cleanupTempFiles(){try{if(await this.fileExists(this.tempDir)){const e=await $.promises.readdir(this.tempDir);for(const t of e)await $.promises.unlink(B.join(this.tempDir,t))}console.log("临时文件清理完成")}catch(e){console.error("清理临时文件失败:",e)}}async cleanup(){await this.cleanupTempFiles()}getTempDir(){return this.tempDir}getCoversDir(){return this.coversDir}}class ai{constructor(e,t){this.databaseManager=e,this.fileManager=t}async getAllBooks(){try{if(!this.databaseManager)throw new Error("数据库管理器未初始化");const e=this.databaseManager.getDatabase();if(!e)throw new Error("数据库连接失败");const r=e.prepare(`
        SELECT
          id, title, author, isbn, file_path, file_format, file_size,
          cover_image, description, publisher, publish_date, language,
          total_pages, word_count, reading_status, current_page,
          reading_progress_percent, last_read_time, import_time,
          tags, metadata_json, created_at, updated_at, deleted_at, sync_version
        FROM books
        WHERE deleted_at IS NULL
        ORDER BY
          CASE WHEN last_read_time IS NOT NULL THEN last_read_time ELSE import_time END DESC
      `).all();console.log(`BookService: 成功查询到 ${r.length} 本图书`);const o=[],n=[];return r.forEach((i,a)=>{try{const l=this.mapToBookInfo(i);o.push(l)}catch(l){console.error(`BookService: 映射第 ${a+1} 本图书失败:`,l),n.push(`图书ID ${i.id} 数据映射失败`)}}),n.length>0&&console.warn(`BookService: ${n.length} 本图书映射失败:`,n),o}catch(e){throw console.error("BookService: 获取图书列表失败:",e),e instanceof Error?e.message.includes("数据库")?new Error("数据库连接失败，请检查数据库文件是否存在"):e.message.includes("SQLITE")?new Error("数据库查询失败，可能是表结构问题"):new Error(`获取图书列表失败: ${e.message}`):new Error("获取图书列表时发生未知错误")}}async getBookById(e){try{const t=this.databaseManager.getDatabase(),r=typeof e=="string"?parseInt(e,10):e;if(isNaN(r)||r<=0)throw new Error("无效的图书ID");const n=t.prepare(`
        SELECT
          id, title, author, isbn, file_path, file_format, file_size,
          cover_image, description, publisher, publish_date, language,
          total_pages, word_count, reading_status, current_page,
          reading_progress_percent, last_read_time, import_time,
          tags, metadata_json, created_at, updated_at, deleted_at, sync_version
        FROM books
        WHERE id = ? AND deleted_at IS NULL
      `).get(r);return n?this.mapToBookInfo(n):null}catch(t){throw console.error("获取图书失败:",t),new Error("获取图书失败")}}async addBook(e){try{if(!await this.fileManager.fileExists(e))throw new Error("文件不存在");if(await this.findBookByFilePath(e))throw new Error("该文件已经导入");const r=await this.fileManager.getFileInfo(e),i={title:this.extractTitleFromFileName(r.name),author:"未知作者",isbn:null,file_path:e,file_format:r.format.toLowerCase(),file_size:r.size,cover_image:null,description:"",publisher:null,publish_date:null,language:"zh-CN",total_pages:0,word_count:0,reading_status:"unread",current_page:0,reading_progress_percent:0,last_read_time:null,tags:null,metadata_json:null,deleted_at:null,sync_version:1},f=this.databaseManager.getDatabase().prepare(`
        INSERT INTO books (
          title, author, isbn, file_path, file_format, file_size,
          cover_image, description, publisher, publish_date, language,
          total_pages, word_count, reading_status, current_page,
          reading_progress_percent, last_read_time, tags, metadata_json,
          sync_version
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(i.title,i.author,i.isbn,i.file_path,i.file_format,i.file_size,i.cover_image,i.description,i.publisher,i.publish_date,i.language,i.total_pages,i.word_count,i.reading_status,i.current_page,i.reading_progress_percent,i.last_read_time,i.tags,i.metadata_json,i.sync_version),m=await this.getBookById(f.lastInsertRowid);if(!m)throw new Error("插入图书后无法获取记录");return m}catch(t){throw console.error("添加图书失败:",t),t}}async removeBook(e){try{const t=this.databaseManager.getDatabase(),r=typeof e=="string"?parseInt(e,10):e;if(isNaN(r)||r<=0)throw new Error("无效的图书ID");return t.prepare(`
        UPDATE books
        SET deleted_at = CURRENT_TIMESTAMP
        WHERE id = ? AND deleted_at IS NULL
      `).run(r).changes>0}catch(t){throw console.error("删除图书失败:",t),new Error("删除图书失败")}}async updateProgress(e,t,r){try{const o=this.databaseManager.getDatabase(),n=typeof e=="string"?parseInt(e,10):e;if(isNaN(n)||n<=0)throw new Error("无效的图书ID");let i="reading";return t===0?i="unread":t>=100&&(i="finished"),o.prepare(`
        UPDATE books
        SET reading_progress_percent = ?,
            current_page = ?,
            reading_status = ?,
            last_read_time = CURRENT_TIMESTAMP
        WHERE id = ? AND deleted_at IS NULL
      `).run(t,r||0,i,n).changes>0}catch(o){throw console.error("更新阅读进度失败:",o),new Error("更新阅读进度失败")}}async searchBooks(e,t=50){try{const r=this.databaseManager.getDatabase(),o=`%${e}%`;return r.prepare(`
        SELECT
          id, title, author, isbn, file_path, file_format, file_size,
          cover_image, description, publisher, publish_date, language,
          total_pages, word_count, reading_status, current_page,
          reading_progress_percent, last_read_time, import_time,
          tags, metadata_json, created_at, updated_at, deleted_at, sync_version
        FROM books
        WHERE deleted_at IS NULL
          AND (title LIKE ? OR author LIKE ? OR description LIKE ? OR publisher LIKE ?)
        ORDER BY
          CASE
            WHEN title LIKE ? THEN 1
            WHEN author LIKE ? THEN 2
            WHEN publisher LIKE ? THEN 3
            ELSE 4
          END,
          title
        LIMIT ?
      `).all(o,o,o,o,o,o,o,t).map(a=>this.mapToBookInfo(a))}catch(r){throw console.error("搜索图书失败:",r),new Error("搜索图书失败")}}async findBookByFilePath(e){try{return this.databaseManager.getDatabase().prepare(`
        SELECT * FROM books 
        WHERE file_path = ? AND deleted_at IS NULL
      `).get(e)}catch(t){return console.error("根据文件路径查找图书失败:",t),null}}extractTitleFromFileName(e){return e.replace(/\.[^/.]+$/,"").replace(/[_-]/g," ").replace(/\s+/g," ").trim()}mapToBookInfo(e){try{if(!e.id)throw new Error("图书ID不能为空");if(!e.title||e.title.trim()==="")throw new Error("图书标题不能为空");if(!e.file_path||e.file_path.trim()==="")throw new Error("图书文件路径不能为空");let t=[],r={};if(e.tags)try{t=JSON.parse(e.tags),Array.isArray(t)||(console.warn(`图书ID ${e.id}: tags字段不是数组格式，使用默认值`),t=[])}catch{console.warn(`图书ID ${e.id}: tags字段JSON解析失败，使用默认值`),t=[]}if(e.metadata_json)try{r=JSON.parse(e.metadata_json),(typeof r!="object"||r===null)&&(console.warn(`图书ID ${e.id}: metadata_json字段不是对象格式，使用默认值`),r={})}catch{console.warn(`图书ID ${e.id}: metadata_json字段JSON解析失败，使用默认值`),r={}}let o,n=null,i,a,l=null;try{if(o=new Date(e.import_time),isNaN(o.getTime()))throw new Error("导入时间格式无效")}catch{console.warn(`图书ID ${e.id}: 导入时间解析失败，使用当前时间`),o=new Date}if(e.last_read_time)try{n=new Date(e.last_read_time),isNaN(n.getTime())&&(n=null)}catch{console.warn(`图书ID ${e.id}: 最后阅读时间解析失败`),n=null}try{i=new Date(e.created_at),isNaN(i.getTime())&&(i=o)}catch{i=o}try{a=new Date(e.updated_at),isNaN(a.getTime())&&(a=i)}catch{a=i}if(e.deleted_at)try{l=new Date(e.deleted_at),isNaN(l.getTime())&&(l=null)}catch{l=null}const f=Math.max(0,Math.min(100,e.reading_progress_percent||0)),m=Math.max(0,e.file_size||0),y=Math.max(0,e.current_page||0),w=Math.max(0,e.total_pages||0),h=Math.max(0,e.word_count||0),E=Math.max(1,e.sync_version||1);return{id:e.id.toString(),title:e.title.trim(),author:e.author||null,isbn:e.isbn||null,filePath:e.file_path.trim(),format:e.file_format||"txt",fileSize:m,coverPath:e.cover_image||null,description:e.description||null,publisher:e.publisher||null,publishDate:e.publish_date||null,language:e.language||"zh-CN",totalPages:w,wordCount:h,readingStatus:e.reading_status||"unread",currentPage:y,readProgress:f,lastReadAt:n,addedAt:o,tags:t,metadata:r,createdAt:i,updatedAt:a,deletedAt:l,syncVersion:E}}catch(t){throw console.error(`BookService: 映射图书数据失败 (ID: ${e.id}):`,t),new Error(`图书数据映射失败: ${t instanceof Error?t.message:"未知错误"}`)}}}class ci{constructor(e){this.bookService=e}registerHandlers(){O.ipcMain.handle("book:list",async()=>{try{console.log("IPC: 开始获取图书列表");const e=await this.bookService.getAllBooks();return console.log(`IPC: 成功获取 ${e.length} 本图书`),e}catch(e){throw console.error("IPC: 获取图书列表失败:",e),new Error(e instanceof Error?e.message:"获取图书列表失败")}}),O.ipcMain.handle("book:get",async(e,t)=>{try{if(console.log(`IPC: 开始获取图书 ${t}`),!t||typeof t!="string")throw new Error("图书ID无效");const r=await this.bookService.getBookById(t);return console.log(`IPC: ${r?"成功获取":"未找到"}图书 ${t}`),r}catch(r){throw console.error(`IPC: 获取图书失败 ${t}:`,r),new Error(r instanceof Error?r.message:"获取图书失败")}}),O.ipcMain.handle("book:add",async(e,t)=>{try{if(console.log(`IPC: 开始添加图书 ${t}`),!t||typeof t!="string")throw new Error("文件路径无效");const r=await this.bookService.addBook(t);return console.log(`IPC: 成功添加图书 ${r.title}`),r}catch(r){throw console.error(`IPC: 添加图书失败 ${t}:`,r),new Error(r instanceof Error?r.message:"添加图书失败")}}),O.ipcMain.handle("book:remove",async(e,t)=>{try{if(console.log(`IPC: 开始删除图书 ${t}`),!t||typeof t!="string")throw new Error("图书ID无效");const r=await this.bookService.removeBook(t);return console.log(`IPC: ${r?"成功":"失败"}删除图书 ${t}`),r}catch(r){throw console.error(`IPC: 删除图书失败 ${t}:`,r),new Error(r instanceof Error?r.message:"删除图书失败")}}),O.ipcMain.handle("book:update-progress",async(e,t,r,o)=>{try{if(console.log(`IPC: 开始更新阅读进度 ${t}, 进度: ${r}%`),!t||typeof t!="string")throw new Error("图书ID无效");if(typeof r!="number"||r<0||r>100)throw new Error("阅读进度必须是0-100之间的数字");if(o!==void 0&&(typeof o!="number"||o<0))throw new Error("当前页码必须是非负数");const n=await this.bookService.updateProgress(t,r,o);return console.log(`IPC: ${n?"成功":"失败"}更新阅读进度 ${t}`),n}catch(n){throw console.error(`IPC: 更新阅读进度失败 ${t}:`,n),new Error(n instanceof Error?n.message:"更新阅读进度失败")}}),O.ipcMain.handle("book:search",async(e,t,r)=>{try{if(console.log(`IPC: 开始搜索图书 "${t}"`),!t||typeof t!="string")throw new Error("搜索关键词无效");if(t.trim().length===0)return[];const o=r&&typeof r=="number"&&r>0?r:50,n=await this.bookService.searchBooks(t.trim(),o);return console.log(`IPC: 搜索到 ${n.length} 本图书`),n}catch(o){throw console.error(`IPC: 搜索图书失败 "${t}":`,o),new Error(o instanceof Error?o.message:"搜索图书失败")}}),O.ipcMain.handle("book:stats",async()=>{try{console.log("IPC: 开始获取图书统计信息");const e=await this.bookService.getAllBooks(),t={total:e.length,unread:e.filter(r=>r.readProgress===0).length,reading:e.filter(r=>r.readProgress>0&&r.readProgress<100).length,finished:e.filter(r=>r.readProgress>=100).length};return console.log("IPC: 成功获取图书统计信息",t),t}catch(e){throw console.error("IPC: 获取图书统计信息失败:",e),new Error(e instanceof Error?e.message:"获取图书统计信息失败")}}),O.ipcMain.handle("book:remove-batch",async(e,t)=>{try{if(console.log(`IPC: 开始批量删除 ${t.length} 本图书`),!Array.isArray(t)||t.length===0)throw new Error("图书ID列表无效");let r=0,o=0;const n=[];for(const a of t)try{await this.bookService.removeBook(a)?r++:(o++,n.push(`删除图书失败: ${a}`))}catch(l){o++,n.push(`删除图书失败 ${a}: ${l instanceof Error?l.message:"未知错误"}`)}const i={success:r,failed:o,errors:n};return console.log("IPC: 批量删除完成",i),i}catch(r){throw console.error("IPC: 批量删除图书失败:",r),new Error(r instanceof Error?r.message:"批量删除图书失败")}}),O.ipcMain.handle("book:get-cover",async(e,t)=>{try{if(console.log(`IPC: 开始获取封面图片 ${t}`),!t||typeof t!="string")return null;const r=require("fs"),o=require("path");if(!r.existsSync(t))return console.log(`IPC: 封面文件不存在 ${t}`),null;const n=r.readFileSync(t),i=o.extname(t).toLowerCase();let a="image/png";i===".jpg"||i===".jpeg"?a="image/jpeg":i===".svg"?a="image/svg+xml":i===".png"&&(a="image/png");const l=n.toString("base64"),f=`data:${a};base64,${l}`;return console.log(`IPC: 成功获取封面图片 ${t}`),f}catch(r){return console.error(`IPC: 获取封面图片失败 ${t}:`,r),null}}),console.log("图书IPC处理器注册完成")}unregisterHandlers(){["book:list","book:get","book:add","book:remove","book:update-progress","book:search","book:stats","book:remove-batch","book:get-cover"].forEach(t=>{O.ipcMain.removeAllListeners(t)}),console.log("图书IPC处理器注销完成")}}var U=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function li(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}function ui(s){if(s.__esModule)return s;var e=s.default;if(typeof e=="function"){var t=function r(){return this instanceof r?Reflect.construct(e,arguments,this.constructor):e.apply(this,arguments)};t.prototype=e.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(s).forEach(function(r){var o=Object.getOwnPropertyDescriptor(s,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return s[r]}})}),t}var xn={exports:{}},Oe=Cn,Te=Oe.Buffer,se={},ae;for(ae in Oe)Oe.hasOwnProperty(ae)&&(ae==="SlowBuffer"||ae==="Buffer"||(se[ae]=Oe[ae]));var _e=se.Buffer={};for(ae in Te)Te.hasOwnProperty(ae)&&(ae==="allocUnsafe"||ae==="allocUnsafeSlow"||(_e[ae]=Te[ae]));se.Buffer.prototype=Te.prototype;(!_e.from||_e.from===Uint8Array.from)&&(_e.from=function(s,e,t){if(typeof s=="number")throw new TypeError('The "value" argument must not be of type number. Received type '+typeof s);if(s&&typeof s.length>"u")throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof s);return Te(s,e,t)});_e.alloc||(_e.alloc=function(s,e,t){if(typeof s!="number")throw new TypeError('The "size" argument must be of type number. Received type '+typeof s);if(s<0||s>=2*(1<<30))throw new RangeError('The value "'+s+'" is invalid for option "size"');var r=Te(s);return!e||e.length===0?r.fill(0):typeof t=="string"?r.fill(e,t):r.fill(e),r});if(!se.kStringMaxLength)try{se.kStringMaxLength=process.binding("buffer").kStringMaxLength}catch{}se.constants||(se.constants={MAX_LENGTH:se.kMaxLength},se.kStringMaxLength&&(se.constants.MAX_STRING_LENGTH=se.kStringMaxLength));var ge=se,Ht={},Dn="\uFEFF";Ht.PrependBOM=qt;function qt(s,e){this.encoder=s,this.addBOM=!0}qt.prototype.write=function(s){return this.addBOM&&(s=Dn+s,this.addBOM=!1),this.encoder.write(s)};qt.prototype.end=function(){return this.encoder.end()};Ht.StripBOM=Vt;function Vt(s,e){this.decoder=s,this.pass=!1,this.options=e||{}}Vt.prototype.write=function(s){var e=this.decoder.write(s);return this.pass||!e||(e[0]===Dn&&(e=e.slice(1),typeof this.options.stripBOM=="function"&&this.options.stripBOM()),this.pass=!0),e};Vt.prototype.end=function(){return this.decoder.end()};var qe={},Ve={},Le={exports:{}};/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */var dr;function fi(){return dr||(dr=1,function(s,e){var t=Cn,r=t.Buffer;function o(i,a){for(var l in i)a[l]=i[l]}r.from&&r.alloc&&r.allocUnsafe&&r.allocUnsafeSlow?s.exports=t:(o(t,e),e.Buffer=n);function n(i,a,l){return r(i,a,l)}n.prototype=Object.create(r.prototype),o(r,n),n.from=function(i,a,l){if(typeof i=="number")throw new TypeError("Argument must not be a number");return r(i,a,l)},n.alloc=function(i,a,l){if(typeof i!="number")throw new TypeError("Argument must be a number");var f=r(i);return a!==void 0?typeof l=="string"?f.fill(a,l):f.fill(a):f.fill(0),f},n.allocUnsafe=function(i){if(typeof i!="number")throw new TypeError("Argument must be a number");return r(i)},n.allocUnsafeSlow=function(i){if(typeof i!="number")throw new TypeError("Argument must be a number");return t.SlowBuffer(i)}}(Le,Le.exports)),Le.exports}var pr;function Nn(){if(pr)return Ve;pr=1;var s=fi().Buffer,e=s.isEncoding||function(b){switch(b=""+b,b&&b.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function t(b){if(!b)return"utf8";for(var u;;)switch(b){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return b;default:if(u)return;b=(""+b).toLowerCase(),u=!0}}function r(b){var u=t(b);if(typeof u!="string"&&(s.isEncoding===e||!e(b)))throw new Error("Unknown encoding: "+b);return u||b}Ve.StringDecoder=o;function o(b){this.encoding=r(b);var u;switch(this.encoding){case"utf16le":this.text=y,this.end=w,u=4;break;case"utf8":this.fillLast=l,u=4;break;case"base64":this.text=h,this.end=E,u=3;break;default:this.write=g,this.end=_;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=s.allocUnsafe(u)}o.prototype.write=function(b){if(b.length===0)return"";var u,c;if(this.lastNeed){if(u=this.fillLast(b),u===void 0)return"";c=this.lastNeed,this.lastNeed=0}else c=0;return c<b.length?u?u+this.text(b,c):this.text(b,c):u||""},o.prototype.end=m,o.prototype.text=f,o.prototype.fillLast=function(b){if(this.lastNeed<=b.length)return b.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);b.copy(this.lastChar,this.lastTotal-this.lastNeed,0,b.length),this.lastNeed-=b.length};function n(b){return b<=127?0:b>>5===6?2:b>>4===14?3:b>>3===30?4:b>>6===2?-1:-2}function i(b,u,c){var p=u.length-1;if(p<c)return 0;var v=n(u[p]);return v>=0?(v>0&&(b.lastNeed=v-1),v):--p<c||v===-2?0:(v=n(u[p]),v>=0?(v>0&&(b.lastNeed=v-2),v):--p<c||v===-2?0:(v=n(u[p]),v>=0?(v>0&&(v===2?v=0:b.lastNeed=v-3),v):0))}function a(b,u,c){if((u[0]&192)!==128)return b.lastNeed=0,"�";if(b.lastNeed>1&&u.length>1){if((u[1]&192)!==128)return b.lastNeed=1,"�";if(b.lastNeed>2&&u.length>2&&(u[2]&192)!==128)return b.lastNeed=2,"�"}}function l(b){var u=this.lastTotal-this.lastNeed,c=a(this,b);if(c!==void 0)return c;if(this.lastNeed<=b.length)return b.copy(this.lastChar,u,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);b.copy(this.lastChar,u,0,b.length),this.lastNeed-=b.length}function f(b,u){var c=i(this,b,u);if(!this.lastNeed)return b.toString("utf8",u);this.lastTotal=c;var p=b.length-(c-this.lastNeed);return b.copy(this.lastChar,0,p),b.toString("utf8",u,p)}function m(b){var u=b&&b.length?this.write(b):"";return this.lastNeed?u+"�":u}function y(b,u){if((b.length-u)%2===0){var c=b.toString("utf16le",u);if(c){var p=c.charCodeAt(c.length-1);if(p>=55296&&p<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=b[b.length-2],this.lastChar[1]=b[b.length-1],c.slice(0,-1)}return c}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=b[b.length-1],b.toString("utf16le",u,b.length-1)}function w(b){var u=b&&b.length?this.write(b):"";if(this.lastNeed){var c=this.lastTotal-this.lastNeed;return u+this.lastChar.toString("utf16le",0,c)}return u}function h(b,u){var c=(b.length-u)%3;return c===0?b.toString("base64",u):(this.lastNeed=3-c,this.lastTotal=3,c===1?this.lastChar[0]=b[b.length-1]:(this.lastChar[0]=b[b.length-2],this.lastChar[1]=b[b.length-1]),b.toString("base64",u,b.length-c))}function E(b){var u=b&&b.length?this.write(b):"";return this.lastNeed?u+this.lastChar.toString("base64",0,3-this.lastNeed):u}function g(b){return b.toString(this.encoding)}function _(b){return b&&b.length?this.write(b):""}return Ve}var We,gr;function hi(){if(gr)return We;gr=1;var s=ge.Buffer;We={utf8:{type:"_internal",bomAware:!0},cesu8:{type:"_internal",bomAware:!0},unicode11utf8:"utf8",ucs2:{type:"_internal",bomAware:!0},utf16le:"ucs2",binary:{type:"_internal"},base64:{type:"_internal"},hex:{type:"_internal"},_internal:e};function e(l,f){this.enc=l.encodingName,this.bomAware=l.bomAware,this.enc==="base64"?this.encoder=n:this.enc==="cesu8"&&(this.enc="utf8",this.encoder=i,s.from("eda0bdedb2a9","hex").toString()!=="💩"&&(this.decoder=a,this.defaultCharUnicode=f.defaultCharUnicode))}e.prototype.encoder=o,e.prototype.decoder=r;var t=Nn().StringDecoder;t.prototype.end||(t.prototype.end=function(){});function r(l,f){this.decoder=new t(f.enc)}r.prototype.write=function(l){return s.isBuffer(l)||(l=s.from(l)),this.decoder.write(l)},r.prototype.end=function(){return this.decoder.end()};function o(l,f){this.enc=f.enc}o.prototype.write=function(l){return s.from(l,this.enc)},o.prototype.end=function(){};function n(l,f){this.prevStr=""}n.prototype.write=function(l){l=this.prevStr+l;var f=l.length-l.length%4;return this.prevStr=l.slice(f),l=l.slice(0,f),s.from(l,"base64")},n.prototype.end=function(){return s.from(this.prevStr,"base64")};function i(l,f){}i.prototype.write=function(l){for(var f=s.alloc(l.length*3),m=0,y=0;y<l.length;y++){var w=l.charCodeAt(y);w<128?f[m++]=w:w<2048?(f[m++]=192+(w>>>6),f[m++]=128+(w&63)):(f[m++]=224+(w>>>12),f[m++]=128+(w>>>6&63),f[m++]=128+(w&63))}return f.slice(0,m)},i.prototype.end=function(){};function a(l,f){this.acc=0,this.contBytes=0,this.accBytes=0,this.defaultCharUnicode=f.defaultCharUnicode}return a.prototype.write=function(l){for(var f=this.acc,m=this.contBytes,y=this.accBytes,w="",h=0;h<l.length;h++){var E=l[h];(E&192)!==128?(m>0&&(w+=this.defaultCharUnicode,m=0),E<128?w+=String.fromCharCode(E):E<224?(f=E&31,m=1,y=1):E<240?(f=E&15,m=2,y=1):w+=this.defaultCharUnicode):m>0?(f=f<<6|E&63,m--,y++,m===0&&(y===2&&f<128&&f>0?w+=this.defaultCharUnicode:y===3&&f<2048?w+=this.defaultCharUnicode:w+=String.fromCharCode(f))):w+=this.defaultCharUnicode}return this.acc=f,this.contBytes=m,this.accBytes=y,w},a.prototype.end=function(){var l=0;return this.contBytes>0&&(l+=this.defaultCharUnicode),l},We}var ue={},mr;function di(){if(mr)return ue;mr=1;var s=ge.Buffer;ue._utf32=e;function e(f,m){this.iconv=m,this.bomAware=!0,this.isLE=f.isLE}ue.utf32le={type:"_utf32",isLE:!0},ue.utf32be={type:"_utf32",isLE:!1},ue.ucs4le="utf32le",ue.ucs4be="utf32be",e.prototype.encoder=t,e.prototype.decoder=r;function t(f,m){this.isLE=m.isLE,this.highSurrogate=0}t.prototype.write=function(f){for(var m=s.from(f,"ucs2"),y=s.alloc(m.length*2),w=this.isLE?y.writeUInt32LE:y.writeUInt32BE,h=0,E=0;E<m.length;E+=2){var g=m.readUInt16LE(E),_=55296<=g&&g<56320,b=56320<=g&&g<57344;if(this.highSurrogate)if(_||!b)w.call(y,this.highSurrogate,h),h+=4;else{var u=(this.highSurrogate-55296<<10|g-56320)+65536;w.call(y,u,h),h+=4,this.highSurrogate=0;continue}_?this.highSurrogate=g:(w.call(y,g,h),h+=4,this.highSurrogate=0)}return h<y.length&&(y=y.slice(0,h)),y},t.prototype.end=function(){if(this.highSurrogate){var f=s.alloc(4);return this.isLE?f.writeUInt32LE(this.highSurrogate,0):f.writeUInt32BE(this.highSurrogate,0),this.highSurrogate=0,f}};function r(f,m){this.isLE=m.isLE,this.badChar=m.iconv.defaultCharUnicode.charCodeAt(0),this.overflow=[]}r.prototype.write=function(f){if(f.length===0)return"";var m=0,y=0,w=s.alloc(f.length+4),h=0,E=this.isLE,g=this.overflow,_=this.badChar;if(g.length>0){for(;m<f.length&&g.length<4;m++)g.push(f[m]);g.length===4&&(E?y=g[m]|g[m+1]<<8|g[m+2]<<16|g[m+3]<<24:y=g[m+3]|g[m+2]<<8|g[m+1]<<16|g[m]<<24,g.length=0,h=o(w,h,y,_))}for(;m<f.length-3;m+=4)E?y=f[m]|f[m+1]<<8|f[m+2]<<16|f[m+3]<<24:y=f[m+3]|f[m+2]<<8|f[m+1]<<16|f[m]<<24,h=o(w,h,y,_);for(;m<f.length;m++)g.push(f[m]);return w.slice(0,h).toString("ucs2")};function o(f,m,y,w){if((y<0||y>1114111)&&(y=w),y>=65536){y-=65536;var h=55296|y>>10;f[m++]=h&255,f[m++]=h>>8;var y=56320|y&1023}return f[m++]=y&255,f[m++]=y>>8,m}r.prototype.end=function(){this.overflow.length=0},ue.utf32=n,ue.ucs4="utf32";function n(f,m){this.iconv=m}n.prototype.encoder=i,n.prototype.decoder=a;function i(f,m){f=f||{},f.addBOM===void 0&&(f.addBOM=!0),this.encoder=m.iconv.getEncoder(f.defaultEncoding||"utf-32le",f)}i.prototype.write=function(f){return this.encoder.write(f)},i.prototype.end=function(){return this.encoder.end()};function a(f,m){this.decoder=null,this.initialBufs=[],this.initialBufsLen=0,this.options=f||{},this.iconv=m.iconv}a.prototype.write=function(f){if(!this.decoder){if(this.initialBufs.push(f),this.initialBufsLen+=f.length,this.initialBufsLen<32)return"";var m=l(this.initialBufs,this.options.defaultEncoding);this.decoder=this.iconv.getDecoder(m,this.options);for(var y="",w=0;w<this.initialBufs.length;w++)y+=this.decoder.write(this.initialBufs[w]);return this.initialBufs.length=this.initialBufsLen=0,y}return this.decoder.write(f)},a.prototype.end=function(){if(!this.decoder){var f=l(this.initialBufs,this.options.defaultEncoding);this.decoder=this.iconv.getDecoder(f,this.options);for(var m="",y=0;y<this.initialBufs.length;y++)m+=this.decoder.write(this.initialBufs[y]);var w=this.decoder.end();return w&&(m+=w),this.initialBufs.length=this.initialBufsLen=0,m}return this.decoder.end()};function l(f,m){var y=[],w=0,h=0,E=0,g=0,_=0;e:for(var b=0;b<f.length;b++)for(var u=f[b],c=0;c<u.length;c++)if(y.push(u[c]),y.length===4){if(w===0){if(y[0]===255&&y[1]===254&&y[2]===0&&y[3]===0)return"utf-32le";if(y[0]===0&&y[1]===0&&y[2]===254&&y[3]===255)return"utf-32be"}if((y[0]!==0||y[1]>16)&&E++,(y[3]!==0||y[2]>16)&&h++,y[0]===0&&y[1]===0&&(y[2]!==0||y[3]!==0)&&_++,(y[0]!==0||y[1]!==0)&&y[2]===0&&y[3]===0&&g++,y.length=0,w++,w>=100)break e}return _-E>g-h?"utf-32be":_-E<g-h?"utf-32le":m||"utf-32le"}return ue}var Pe={},yr;function pi(){if(yr)return Pe;yr=1;var s=ge.Buffer;Pe.utf16be=e;function e(){}e.prototype.encoder=t,e.prototype.decoder=r,e.prototype.bomAware=!0;function t(){}t.prototype.write=function(l){for(var f=s.from(l,"ucs2"),m=0;m<f.length;m+=2){var y=f[m];f[m]=f[m+1],f[m+1]=y}return f},t.prototype.end=function(){};function r(){this.overflowByte=-1}r.prototype.write=function(l){if(l.length==0)return"";var f=s.alloc(l.length+1),m=0,y=0;for(this.overflowByte!==-1&&(f[0]=l[0],f[1]=this.overflowByte,m=1,y=2);m<l.length-1;m+=2,y+=2)f[y]=l[m+1],f[y+1]=l[m];return this.overflowByte=m==l.length-1?l[l.length-1]:-1,f.slice(0,y).toString("ucs2")},r.prototype.end=function(){this.overflowByte=-1},Pe.utf16=o;function o(l,f){this.iconv=f}o.prototype.encoder=n,o.prototype.decoder=i;function n(l,f){l=l||{},l.addBOM===void 0&&(l.addBOM=!0),this.encoder=f.iconv.getEncoder("utf-16le",l)}n.prototype.write=function(l){return this.encoder.write(l)},n.prototype.end=function(){return this.encoder.end()};function i(l,f){this.decoder=null,this.initialBufs=[],this.initialBufsLen=0,this.options=l||{},this.iconv=f.iconv}i.prototype.write=function(l){if(!this.decoder){if(this.initialBufs.push(l),this.initialBufsLen+=l.length,this.initialBufsLen<16)return"";var f=a(this.initialBufs,this.options.defaultEncoding);this.decoder=this.iconv.getDecoder(f,this.options);for(var m="",y=0;y<this.initialBufs.length;y++)m+=this.decoder.write(this.initialBufs[y]);return this.initialBufs.length=this.initialBufsLen=0,m}return this.decoder.write(l)},i.prototype.end=function(){if(!this.decoder){var l=a(this.initialBufs,this.options.defaultEncoding);this.decoder=this.iconv.getDecoder(l,this.options);for(var f="",m=0;m<this.initialBufs.length;m++)f+=this.decoder.write(this.initialBufs[m]);var y=this.decoder.end();return y&&(f+=y),this.initialBufs.length=this.initialBufsLen=0,f}return this.decoder.end()};function a(l,f){var m=[],y=0,w=0,h=0;e:for(var E=0;E<l.length;E++)for(var g=l[E],_=0;_<g.length;_++)if(m.push(g[_]),m.length===2){if(y===0){if(m[0]===255&&m[1]===254)return"utf-16le";if(m[0]===254&&m[1]===255)return"utf-16be"}if(m[0]===0&&m[1]!==0&&h++,m[0]!==0&&m[1]===0&&w++,m.length=0,y++,y>=100)break e}return h>w?"utf-16be":h<w?"utf-16le":f||"utf-16le"}return Pe}var Ce={},Er;function gi(){if(Er)return Ce;Er=1;var s=ge.Buffer;Ce.utf7=e,Ce.unicode11utf7="utf7";function e(g,_){this.iconv=_}e.prototype.encoder=r,e.prototype.decoder=o,e.prototype.bomAware=!0;var t=/[^A-Za-z0-9'\(\),-\.\/:\? \n\r\t]+/g;function r(g,_){this.iconv=_.iconv}r.prototype.write=function(g){return s.from(g.replace(t,(function(_){return"+"+(_==="+"?"":this.iconv.encode(_,"utf16-be").toString("base64").replace(/=+$/,""))+"-"}).bind(this)))},r.prototype.end=function(){};function o(g,_){this.iconv=_.iconv,this.inBase64=!1,this.base64Accum=""}for(var n=/[A-Za-z0-9\/+]/,i=[],a=0;a<256;a++)i[a]=n.test(String.fromCharCode(a));var l=43,f=45,m=38;o.prototype.write=function(g){for(var _="",b=0,u=this.inBase64,c=this.base64Accum,p=0;p<g.length;p++)if(!u)g[p]==l&&(_+=this.iconv.decode(g.slice(b,p),"ascii"),b=p+1,u=!0);else if(!i[g[p]]){if(p==b&&g[p]==f)_+="+";else{var v=c+this.iconv.decode(g.slice(b,p),"ascii");_+=this.iconv.decode(s.from(v,"base64"),"utf16-be")}g[p]!=f&&p--,b=p+1,u=!1,c=""}if(!u)_+=this.iconv.decode(g.slice(b),"ascii");else{var v=c+this.iconv.decode(g.slice(b),"ascii"),I=v.length-v.length%8;c=v.slice(I),v=v.slice(0,I),_+=this.iconv.decode(s.from(v,"base64"),"utf16-be")}return this.inBase64=u,this.base64Accum=c,_},o.prototype.end=function(){var g="";return this.inBase64&&this.base64Accum.length>0&&(g=this.iconv.decode(s.from(this.base64Accum,"base64"),"utf16-be")),this.inBase64=!1,this.base64Accum="",g},Ce.utf7imap=y;function y(g,_){this.iconv=_}y.prototype.encoder=w,y.prototype.decoder=h,y.prototype.bomAware=!0;function w(g,_){this.iconv=_.iconv,this.inBase64=!1,this.base64Accum=s.alloc(6),this.base64AccumIdx=0}w.prototype.write=function(g){for(var _=this.inBase64,b=this.base64Accum,u=this.base64AccumIdx,c=s.alloc(g.length*5+10),p=0,v=0;v<g.length;v++){var I=g.charCodeAt(v);32<=I&&I<=126?(_&&(u>0&&(p+=c.write(b.slice(0,u).toString("base64").replace(/\//g,",").replace(/=+$/,""),p),u=0),c[p++]=f,_=!1),_||(c[p++]=I,I===m&&(c[p++]=f))):(_||(c[p++]=m,_=!0),_&&(b[u++]=I>>8,b[u++]=I&255,u==b.length&&(p+=c.write(b.toString("base64").replace(/\//g,","),p),u=0)))}return this.inBase64=_,this.base64AccumIdx=u,c.slice(0,p)},w.prototype.end=function(){var g=s.alloc(10),_=0;return this.inBase64&&(this.base64AccumIdx>0&&(_+=g.write(this.base64Accum.slice(0,this.base64AccumIdx).toString("base64").replace(/\//g,",").replace(/=+$/,""),_),this.base64AccumIdx=0),g[_++]=f,this.inBase64=!1),g.slice(0,_)};function h(g,_){this.iconv=_.iconv,this.inBase64=!1,this.base64Accum=""}var E=i.slice();return E[44]=!0,h.prototype.write=function(g){for(var _="",b=0,u=this.inBase64,c=this.base64Accum,p=0;p<g.length;p++)if(!u)g[p]==m&&(_+=this.iconv.decode(g.slice(b,p),"ascii"),b=p+1,u=!0);else if(!E[g[p]]){if(p==b&&g[p]==f)_+="&";else{var v=c+this.iconv.decode(g.slice(b,p),"ascii").replace(/,/g,"/");_+=this.iconv.decode(s.from(v,"base64"),"utf16-be")}g[p]!=f&&p--,b=p+1,u=!1,c=""}if(!u)_+=this.iconv.decode(g.slice(b),"ascii");else{var v=c+this.iconv.decode(g.slice(b),"ascii").replace(/,/g,"/"),I=v.length-v.length%8;c=v.slice(I),v=v.slice(0,I),_+=this.iconv.decode(s.from(v,"base64"),"utf16-be")}return this.inBase64=u,this.base64Accum=c,_},h.prototype.end=function(){var g="";return this.inBase64&&this.base64Accum.length>0&&(g=this.iconv.decode(s.from(this.base64Accum,"base64"),"utf16-be")),this.inBase64=!1,this.base64Accum="",g},Ce}var Ge={},br;function mi(){if(br)return Ge;br=1;var s=ge.Buffer;Ge._sbcs=e;function e(o,n){if(!o)throw new Error("SBCS codec is called without the data.");if(!o.chars||o.chars.length!==128&&o.chars.length!==256)throw new Error("Encoding '"+o.type+"' has incorrect 'chars' (must be of len 128 or 256)");if(o.chars.length===128){for(var i="",a=0;a<128;a++)i+=String.fromCharCode(a);o.chars=i+o.chars}this.decodeBuf=s.from(o.chars,"ucs2");for(var l=s.alloc(65536,n.defaultCharSingleByte.charCodeAt(0)),a=0;a<o.chars.length;a++)l[o.chars.charCodeAt(a)]=a;this.encodeBuf=l}e.prototype.encoder=t,e.prototype.decoder=r;function t(o,n){this.encodeBuf=n.encodeBuf}t.prototype.write=function(o){for(var n=s.alloc(o.length),i=0;i<o.length;i++)n[i]=this.encodeBuf[o.charCodeAt(i)];return n},t.prototype.end=function(){};function r(o,n){this.decodeBuf=n.decodeBuf}return r.prototype.write=function(o){for(var n=this.decodeBuf,i=s.alloc(o.length*2),a=0,l=0,f=0;f<o.length;f++)a=o[f]*2,l=f*2,i[l]=n[a],i[l+1]=n[a+1];return i.toString("ucs2")},r.prototype.end=function(){},Ge}var Ze,wr;function yi(){return wr||(wr=1,Ze={10029:"maccenteuro",maccenteuro:{type:"_sbcs",chars:"ÄĀāÉĄÖÜáąČäčĆćéŹźĎíďĒēĖóėôöõúĚěü†°Ę£§•¶ß®©™ę¨≠ģĮįĪ≤≥īĶ∂∑łĻļĽľĹĺŅņŃ¬√ńŇ∆«»… ňŐÕőŌ–—“”‘’÷◊ōŔŕŘ‹›řŖŗŠ‚„šŚśÁŤťÍŽžŪÓÔūŮÚůŰűŲųÝýķŻŁżĢˇ"},808:"cp808",ibm808:"cp808",cp808:{type:"_sbcs",chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀рстуфхцчшщъыьэюяЁёЄєЇїЎў°∙·√№€■ "},mik:{type:"_sbcs",chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя└┴┬├─┼╣║╚╔╩╦╠═╬┐░▒▓│┤№§╗╝┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp720:{type:"_sbcs",chars:"éâàçêëèïîّْô¤ـûùءآأؤ£إئابةتثجحخدذرزسشص«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀ضطظعغفµقكلمنهوىي≡ًٌٍَُِ≈°∙·√ⁿ²■ "},ascii8bit:"ascii",usascii:"ascii",ansix34:"ascii",ansix341968:"ascii",ansix341986:"ascii",csascii:"ascii",cp367:"ascii",ibm367:"ascii",isoir6:"ascii",iso646us:"ascii",iso646irv:"ascii",us:"ascii",latin1:"iso88591",latin2:"iso88592",latin3:"iso88593",latin4:"iso88594",latin5:"iso88599",latin6:"iso885910",latin7:"iso885913",latin8:"iso885914",latin9:"iso885915",latin10:"iso885916",csisolatin1:"iso88591",csisolatin2:"iso88592",csisolatin3:"iso88593",csisolatin4:"iso88594",csisolatincyrillic:"iso88595",csisolatinarabic:"iso88596",csisolatingreek:"iso88597",csisolatinhebrew:"iso88598",csisolatin5:"iso88599",csisolatin6:"iso885910",l1:"iso88591",l2:"iso88592",l3:"iso88593",l4:"iso88594",l5:"iso88599",l6:"iso885910",l7:"iso885913",l8:"iso885914",l9:"iso885915",l10:"iso885916",isoir14:"iso646jp",isoir57:"iso646cn",isoir100:"iso88591",isoir101:"iso88592",isoir109:"iso88593",isoir110:"iso88594",isoir144:"iso88595",isoir127:"iso88596",isoir126:"iso88597",isoir138:"iso88598",isoir148:"iso88599",isoir157:"iso885910",isoir166:"tis620",isoir179:"iso885913",isoir199:"iso885914",isoir203:"iso885915",isoir226:"iso885916",cp819:"iso88591",ibm819:"iso88591",cyrillic:"iso88595",arabic:"iso88596",arabic8:"iso88596",ecma114:"iso88596",asmo708:"iso88596",greek:"iso88597",greek8:"iso88597",ecma118:"iso88597",elot928:"iso88597",hebrew:"iso88598",hebrew8:"iso88598",turkish:"iso88599",turkish8:"iso88599",thai:"iso885911",thai8:"iso885911",celtic:"iso885914",celtic8:"iso885914",isoceltic:"iso885914",tis6200:"tis620",tis62025291:"tis620",tis62025330:"tis620",1e4:"macroman",10006:"macgreek",10007:"maccyrillic",10079:"maciceland",10081:"macturkish",cspc8codepage437:"cp437",cspc775baltic:"cp775",cspc850multilingual:"cp850",cspcp852:"cp852",cspc862latinhebrew:"cp862",cpgr:"cp869",msee:"cp1250",mscyrl:"cp1251",msansi:"cp1252",msgreek:"cp1253",msturk:"cp1254",mshebr:"cp1255",msarab:"cp1256",winbaltrim:"cp1257",cp20866:"koi8r",20866:"koi8r",ibm878:"koi8r",cskoi8r:"koi8r",cp21866:"koi8u",21866:"koi8u",ibm1168:"koi8u",strk10482002:"rk1048",tcvn5712:"tcvn",tcvn57121:"tcvn",gb198880:"iso646cn",cn:"iso646cn",csiso14jisc6220ro:"iso646jp",jisc62201969ro:"iso646jp",jp:"iso646jp",cshproman8:"hproman8",r8:"hproman8",roman8:"hproman8",xroman8:"hproman8",ibm1051:"hproman8",mac:"macintosh",csmacintosh:"macintosh"}),Ze}var Ye,Tr;function Ei(){return Tr||(Tr=1,Ye={437:"cp437",737:"cp737",775:"cp775",850:"cp850",852:"cp852",855:"cp855",856:"cp856",857:"cp857",858:"cp858",860:"cp860",861:"cp861",862:"cp862",863:"cp863",864:"cp864",865:"cp865",866:"cp866",869:"cp869",874:"windows874",922:"cp922",1046:"cp1046",1124:"cp1124",1125:"cp1125",1129:"cp1129",1133:"cp1133",1161:"cp1161",1162:"cp1162",1163:"cp1163",1250:"windows1250",1251:"windows1251",1252:"windows1252",1253:"windows1253",1254:"windows1254",1255:"windows1255",1256:"windows1256",1257:"windows1257",1258:"windows1258",28591:"iso88591",28592:"iso88592",28593:"iso88593",28594:"iso88594",28595:"iso88595",28596:"iso88596",28597:"iso88597",28598:"iso88598",28599:"iso88599",28600:"iso885910",28601:"iso885911",28603:"iso885913",28604:"iso885914",28605:"iso885915",28606:"iso885916",windows874:{type:"_sbcs",chars:"€����…�����������‘’“”•–—�������� กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู����฿เแโใไๅๆ็่้๊๋์ํ๎๏๐๑๒๓๔๕๖๗๘๙๚๛����"},win874:"windows874",cp874:"windows874",windows1250:{type:"_sbcs",chars:"€�‚�„…†‡�‰Š‹ŚŤŽŹ�‘’“”•–—�™š›śťžź ˇ˘Ł¤Ą¦§¨©Ş«¬­®Ż°±˛ł´µ¶·¸ąş»Ľ˝ľżŔÁÂĂÄĹĆÇČÉĘËĚÍÎĎĐŃŇÓÔŐÖ×ŘŮÚŰÜÝŢßŕáâăäĺćçčéęëěíîďđńňóôőö÷řůúűüýţ˙"},win1250:"windows1250",cp1250:"windows1250",windows1251:{type:"_sbcs",chars:"ЂЃ‚ѓ„…†‡€‰Љ‹ЊЌЋЏђ‘’“”•–—�™љ›њќћџ ЎўЈ¤Ґ¦§Ё©Є«¬­®Ї°±Ііґµ¶·ё№є»јЅѕїАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя"},win1251:"windows1251",cp1251:"windows1251",windows1252:{type:"_sbcs",chars:"€�‚ƒ„…†‡ˆ‰Š‹Œ�Ž��‘’“”•–—˜™š›œ�žŸ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖ×ØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ"},win1252:"windows1252",cp1252:"windows1252",windows1253:{type:"_sbcs",chars:"€�‚ƒ„…†‡�‰�‹�����‘’“”•–—�™�›���� ΅Ά£¤¥¦§¨©�«¬­®―°±²³΄µ¶·ΈΉΊ»Ό½ΎΏΐΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡ�ΣΤΥΦΧΨΩΪΫάέήίΰαβγδεζηθικλμνξοπρςστυφχψωϊϋόύώ�"},win1253:"windows1253",cp1253:"windows1253",windows1254:{type:"_sbcs",chars:"€�‚ƒ„…†‡ˆ‰Š‹Œ����‘’“”•–—˜™š›œ��Ÿ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏĞÑÒÓÔÕÖ×ØÙÚÛÜİŞßàáâãäåæçèéêëìíîïğñòóôõö÷øùúûüışÿ"},win1254:"windows1254",cp1254:"windows1254",windows1255:{type:"_sbcs",chars:"€�‚ƒ„…†‡ˆ‰�‹�����‘’“”•–—˜™�›���� ¡¢£₪¥¦§¨©×«¬­®¯°±²³´µ¶·¸¹÷»¼½¾¿ְֱֲֳִֵֶַָֹֺֻּֽ־ֿ׀ׁׂ׃װױײ׳״�������אבגדהוזחטיךכלםמןנסעףפץצקרשת��‎‏�"},win1255:"windows1255",cp1255:"windows1255",windows1256:{type:"_sbcs",chars:"€پ‚ƒ„…†‡ˆ‰ٹ‹Œچژڈگ‘’“”•–—ک™ڑ›œ‌‍ں ،¢£¤¥¦§¨©ھ«¬­®¯°±²³´µ¶·¸¹؛»¼½¾؟ہءآأؤإئابةتثجحخدذرزسشصض×طظعغـفقكàلâمنهوçèéêëىيîïًٌٍَôُِ÷ّùْûü‎‏ے"},win1256:"windows1256",cp1256:"windows1256",windows1257:{type:"_sbcs",chars:"€�‚�„…†‡�‰�‹�¨ˇ¸�‘’“”•–—�™�›�¯˛� �¢£¤�¦§Ø©Ŗ«¬­®Æ°±²³´µ¶·ø¹ŗ»¼½¾æĄĮĀĆÄÅĘĒČÉŹĖĢĶĪĻŠŃŅÓŌÕÖ×ŲŁŚŪÜŻŽßąįāćäåęēčéźėģķīļšńņóōõö÷ųłśūüżž˙"},win1257:"windows1257",cp1257:"windows1257",windows1258:{type:"_sbcs",chars:"€�‚ƒ„…†‡ˆ‰�‹Œ����‘’“”•–—˜™�›œ��Ÿ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂĂÄÅÆÇÈÉÊË̀ÍÎÏĐÑ̉ÓÔƠÖ×ØÙÚÛÜỮßàáâăäåæçèéêë́íîïđṇ̃óôơö÷øùúûüư₫ÿ"},win1258:"windows1258",cp1258:"windows1258",iso88591:{type:"_sbcs",chars:" ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖ×ØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ"},cp28591:"iso88591",iso88592:{type:"_sbcs",chars:" Ą˘Ł¤ĽŚ§¨ŠŞŤŹ­ŽŻ°ą˛ł´ľśˇ¸šşťź˝žżŔÁÂĂÄĹĆÇČÉĘËĚÍÎĎĐŃŇÓÔŐÖ×ŘŮÚŰÜÝŢßŕáâăäĺćçčéęëěíîďđńňóôőö÷řůúűüýţ˙"},cp28592:"iso88592",iso88593:{type:"_sbcs",chars:" Ħ˘£¤�Ĥ§¨İŞĞĴ­�Ż°ħ²³´µĥ·¸ışğĵ½�żÀÁÂ�ÄĊĈÇÈÉÊËÌÍÎÏ�ÑÒÓÔĠÖ×ĜÙÚÛÜŬŜßàáâ�äċĉçèéêëìíîï�ñòóôġö÷ĝùúûüŭŝ˙"},cp28593:"iso88593",iso88594:{type:"_sbcs",chars:" ĄĸŖ¤ĨĻ§¨ŠĒĢŦ­Ž¯°ą˛ŗ´ĩļˇ¸šēģŧŊžŋĀÁÂÃÄÅÆĮČÉĘËĖÍÎĪĐŅŌĶÔÕÖ×ØŲÚÛÜŨŪßāáâãäåæįčéęëėíîīđņōķôõö÷øųúûüũū˙"},cp28594:"iso88594",iso88595:{type:"_sbcs",chars:" ЁЂЃЄЅІЇЈЉЊЋЌ­ЎЏАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя№ёђѓєѕіїјљњћќ§ўџ"},cp28595:"iso88595",iso88596:{type:"_sbcs",chars:" ���¤�������،­�������������؛���؟�ءآأؤإئابةتثجحخدذرزسشصضطظعغ�����ـفقكلمنهوىيًٌٍَُِّْ�������������"},cp28596:"iso88596",iso88597:{type:"_sbcs",chars:" ‘’£€₯¦§¨©ͺ«¬­�―°±²³΄΅Ά·ΈΉΊ»Ό½ΎΏΐΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡ�ΣΤΥΦΧΨΩΪΫάέήίΰαβγδεζηθικλμνξοπρςστυφχψωϊϋόύώ�"},cp28597:"iso88597",iso88598:{type:"_sbcs",chars:" �¢£¤¥¦§¨©×«¬­®¯°±²³´µ¶·¸¹÷»¼½¾��������������������������������‗אבגדהוזחטיךכלםמןנסעףפץצקרשת��‎‏�"},cp28598:"iso88598",iso88599:{type:"_sbcs",chars:" ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏĞÑÒÓÔÕÖ×ØÙÚÛÜİŞßàáâãäåæçèéêëìíîïğñòóôõö÷øùúûüışÿ"},cp28599:"iso88599",iso885910:{type:"_sbcs",chars:" ĄĒĢĪĨĶ§ĻĐŠŦŽ­ŪŊ°ąēģīĩķ·ļđšŧž―ūŋĀÁÂÃÄÅÆĮČÉĘËĖÍÎÏÐŅŌÓÔÕÖŨØŲÚÛÜÝÞßāáâãäåæįčéęëėíîïðņōóôõöũøųúûüýþĸ"},cp28600:"iso885910",iso885911:{type:"_sbcs",chars:" กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู����฿เแโใไๅๆ็่้๊๋์ํ๎๏๐๑๒๓๔๕๖๗๘๙๚๛����"},cp28601:"iso885911",iso885913:{type:"_sbcs",chars:" ”¢£¤„¦§Ø©Ŗ«¬­®Æ°±²³“µ¶·ø¹ŗ»¼½¾æĄĮĀĆÄÅĘĒČÉŹĖĢĶĪĻŠŃŅÓŌÕÖ×ŲŁŚŪÜŻŽßąįāćäåęēčéźėģķīļšńņóōõö÷ųłśūüżž’"},cp28603:"iso885913",iso885914:{type:"_sbcs",chars:" Ḃḃ£ĊċḊ§Ẁ©ẂḋỲ­®ŸḞḟĠġṀṁ¶ṖẁṗẃṠỳẄẅṡÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏŴÑÒÓÔÕÖṪØÙÚÛÜÝŶßàáâãäåæçèéêëìíîïŵñòóôõöṫøùúûüýŷÿ"},cp28604:"iso885914",iso885915:{type:"_sbcs",chars:" ¡¢£€¥Š§š©ª«¬­®¯°±²³Žµ¶·ž¹º»ŒœŸ¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖ×ØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ"},cp28605:"iso885915",iso885916:{type:"_sbcs",chars:" ĄąŁ€„Š§š©Ș«Ź­źŻ°±ČłŽ”¶·žčș»ŒœŸżÀÁÂĂÄĆÆÇÈÉÊËÌÍÎÏĐŃÒÓÔŐÖŚŰÙÚÛÜĘȚßàáâăäćæçèéêëìíîïđńòóôőöśűùúûüęțÿ"},cp28606:"iso885916",cp437:{type:"_sbcs",chars:"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},ibm437:"cp437",csibm437:"cp437",cp737:{type:"_sbcs",chars:"ΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩαβγδεζηθικλμνξοπρσςτυφχψ░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀ωάέήϊίόύϋώΆΈΉΊΌΎΏ±≥≤ΪΫ÷≈°∙·√ⁿ²■ "},ibm737:"cp737",csibm737:"cp737",cp775:{type:"_sbcs",chars:"ĆüéāäģåćłēŖŗīŹÄÅÉæÆōöĢ¢ŚśÖÜø£Ø×¤ĀĪóŻżź”¦©®¬½¼Ł«»░▒▓│┤ĄČĘĖ╣║╗╝ĮŠ┐└┴┬├─┼ŲŪ╚╔╩╦╠═╬Žąčęėįšųūž┘┌█▄▌▐▀ÓßŌŃõÕµńĶķĻļņĒŅ’­±“¾¶§÷„°∙·¹³²■ "},ibm775:"cp775",csibm775:"cp775",cp850:{type:"_sbcs",chars:"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜø£Ø×ƒáíóúñÑªº¿®¬½¼¡«»░▒▓│┤ÁÂÀ©╣║╗╝¢¥┐└┴┬├─┼ãÃ╚╔╩╦╠═╬¤ðÐÊËÈıÍÎÏ┘┌█▄¦Ì▀ÓßÔÒõÕµþÞÚÛÙýÝ¯´­±‗¾¶§÷¸°¨·¹³²■ "},ibm850:"cp850",csibm850:"cp850",cp852:{type:"_sbcs",chars:"ÇüéâäůćçłëŐőîŹÄĆÉĹĺôöĽľŚśÖÜŤťŁ×čáíóúĄąŽžĘę¬źČş«»░▒▓│┤ÁÂĚŞ╣║╗╝Żż┐└┴┬├─┼Ăă╚╔╩╦╠═╬¤đĐĎËďŇÍÎě┘┌█▄ŢŮ▀ÓßÔŃńňŠšŔÚŕŰýÝţ´­˝˛ˇ˘§÷¸°¨˙űŘř■ "},ibm852:"cp852",csibm852:"cp852",cp855:{type:"_sbcs",chars:"ђЂѓЃёЁєЄѕЅіІїЇјЈљЉњЊћЋќЌўЎџЏюЮъЪаАбБцЦдДеЕфФгГ«»░▒▓│┤хХиИ╣║╗╝йЙ┐└┴┬├─┼кК╚╔╩╦╠═╬¤лЛмМнНоОп┘┌█▄Пя▀ЯрРсСтТуУжЖвВьЬ№­ыЫзЗшШэЭщЩчЧ§■ "},ibm855:"cp855",csibm855:"cp855",cp856:{type:"_sbcs",chars:"אבגדהוזחטיךכלםמןנסעףפץצקרשת�£�×����������®¬½¼�«»░▒▓│┤���©╣║╗╝¢¥┐└┴┬├─┼��╚╔╩╦╠═╬¤���������┘┌█▄¦�▀������µ�������¯´­±‗¾¶§÷¸°¨·¹³²■ "},ibm856:"cp856",csibm856:"cp856",cp857:{type:"_sbcs",chars:"ÇüéâäàåçêëèïîıÄÅÉæÆôöòûùİÖÜø£ØŞşáíóúñÑĞğ¿®¬½¼¡«»░▒▓│┤ÁÂÀ©╣║╗╝¢¥┐└┴┬├─┼ãÃ╚╔╩╦╠═╬¤ºªÊËÈ�ÍÎÏ┘┌█▄¦Ì▀ÓßÔÒõÕµ�×ÚÛÙìÿ¯´­±�¾¶§÷¸°¨·¹³²■ "},ibm857:"cp857",csibm857:"cp857",cp858:{type:"_sbcs",chars:"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜø£Ø×ƒáíóúñÑªº¿®¬½¼¡«»░▒▓│┤ÁÂÀ©╣║╗╝¢¥┐└┴┬├─┼ãÃ╚╔╩╦╠═╬¤ðÐÊËÈ€ÍÎÏ┘┌█▄¦Ì▀ÓßÔÒõÕµþÞÚÛÙýÝ¯´­±‗¾¶§÷¸°¨·¹³²■ "},ibm858:"cp858",csibm858:"cp858",cp860:{type:"_sbcs",chars:"ÇüéâãàÁçêÊèÍÔìÃÂÉÀÈôõòÚùÌÕÜ¢£Ù₧ÓáíóúñÑªº¿Ò¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},ibm860:"cp860",csibm860:"cp860",cp861:{type:"_sbcs",chars:"ÇüéâäàåçêëèÐðÞÄÅÉæÆôöþûÝýÖÜø£Ø₧ƒáíóúÁÍÓÚ¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},ibm861:"cp861",csibm861:"cp861",cp862:{type:"_sbcs",chars:"אבגדהוזחטיךכלםמןנסעףפץצקרשת¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},ibm862:"cp862",csibm862:"cp862",cp863:{type:"_sbcs",chars:"ÇüéâÂà¶çêëèïî‗À§ÉÈÊôËÏûù¤ÔÜ¢£ÙÛƒ¦´óú¨¸³¯Î⌐¬½¼¾«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},ibm863:"cp863",csibm863:"cp863",cp864:{type:"_sbcs",chars:`\0\x07\b	
\v\f\r\x1B !"#$٪&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_\`abcdefghijklmnopqrstuvwxyz{|}~°·∙√▒─│┼┤┬├┴┐┌└┘β∞φ±½¼≈«»ﻷﻸ��ﻻﻼ� ­ﺂ£¤ﺄ��ﺎﺏﺕﺙ،ﺝﺡﺥ٠١٢٣٤٥٦٧٨٩ﻑ؛ﺱﺵﺹ؟¢ﺀﺁﺃﺅﻊﺋﺍﺑﺓﺗﺛﺟﺣﺧﺩﺫﺭﺯﺳﺷﺻﺿﻁﻅﻋﻏ¦¬÷×ﻉـﻓﻗﻛﻟﻣﻧﻫﻭﻯﻳﺽﻌﻎﻍﻡﹽّﻥﻩﻬﻰﻲﻐﻕﻵﻶﻝﻙﻱ■�`},ibm864:"cp864",csibm864:"cp864",cp865:{type:"_sbcs",chars:"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜø£Ø₧ƒáíóúñÑªº¿⌐¬½¼¡«¤░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},ibm865:"cp865",csibm865:"cp865",cp866:{type:"_sbcs",chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀рстуфхцчшщъыьэюяЁёЄєЇїЎў°∙·√№¤■ "},ibm866:"cp866",csibm866:"cp866",cp869:{type:"_sbcs",chars:"������Ά�·¬¦‘’Έ―ΉΊΪΌ��ΎΫ©Ώ²³ά£έήίϊΐόύΑΒΓΔΕΖΗ½ΘΙ«»░▒▓│┤ΚΛΜΝ╣║╗╝ΞΟ┐└┴┬├─┼ΠΡ╚╔╩╦╠═╬ΣΤΥΦΧΨΩαβγ┘┌█▄δε▀ζηθικλμνξοπρσςτ΄­±υφχ§ψ΅°¨ωϋΰώ■ "},ibm869:"cp869",csibm869:"cp869",cp922:{type:"_sbcs",chars:" ¡¢£¤¥¦§¨©ª«¬­®‾°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏŠÑÒÓÔÕÖ×ØÙÚÛÜÝŽßàáâãäåæçèéêëìíîïšñòóôõö÷øùúûüýžÿ"},ibm922:"cp922",csibm922:"cp922",cp1046:{type:"_sbcs",chars:"ﺈ×÷ﹱ■│─┐┌└┘ﹹﹻﹽﹿﹷﺊﻰﻳﻲﻎﻏﻐﻶﻸﻺﻼ ¤ﺋﺑﺗﺛﺟﺣ،­ﺧﺳ٠١٢٣٤٥٦٧٨٩ﺷ؛ﺻﺿﻊ؟ﻋءآأؤإئابةتثجحخدذرزسشصضطﻇعغﻌﺂﺄﺎﻓـفقكلمنهوىيًٌٍَُِّْﻗﻛﻟﻵﻷﻹﻻﻣﻧﻬﻩ�"},ibm1046:"cp1046",csibm1046:"cp1046",cp1124:{type:"_sbcs",chars:" ЁЂҐЄЅІЇЈЉЊЋЌ­ЎЏАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя№ёђґєѕіїјљњћќ§ўџ"},ibm1124:"cp1124",csibm1124:"cp1124",cp1125:{type:"_sbcs",chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀рстуфхцчшщъыьэюяЁёҐґЄєІіЇї·√№¤■ "},ibm1125:"cp1125",csibm1125:"cp1125",cp1129:{type:"_sbcs",chars:" ¡¢£¤¥¦§œ©ª«¬­®¯°±²³Ÿµ¶·Œ¹º»¼½¾¿ÀÁÂĂÄÅÆÇÈÉÊË̀ÍÎÏĐÑ̉ÓÔƠÖ×ØÙÚÛÜỮßàáâăäåæçèéêë́íîïđṇ̃óôơö÷øùúûüư₫ÿ"},ibm1129:"cp1129",csibm1129:"cp1129",cp1133:{type:"_sbcs",chars:" ກຂຄງຈສຊຍດຕຖທນບປຜຝພຟມຢຣລວຫອຮ���ຯະາຳິີຶືຸູຼັົຽ���ເແໂໃໄ່້໊໋໌ໍໆ�ໜໝ₭����������������໐໑໒໓໔໕໖໗໘໙��¢¬¦�"},ibm1133:"cp1133",csibm1133:"cp1133",cp1161:{type:"_sbcs",chars:"��������������������������������่กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู้๊๋€฿เแโใไๅๆ็่้๊๋์ํ๎๏๐๑๒๓๔๕๖๗๘๙๚๛¢¬¦ "},ibm1161:"cp1161",csibm1161:"cp1161",cp1162:{type:"_sbcs",chars:"€…‘’“”•–— กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู����฿เแโใไๅๆ็่้๊๋์ํ๎๏๐๑๒๓๔๕๖๗๘๙๚๛����"},ibm1162:"cp1162",csibm1162:"cp1162",cp1163:{type:"_sbcs",chars:" ¡¢£€¥¦§œ©ª«¬­®¯°±²³Ÿµ¶·Œ¹º»¼½¾¿ÀÁÂĂÄÅÆÇÈÉÊË̀ÍÎÏĐÑ̉ÓÔƠÖ×ØÙÚÛÜỮßàáâăäåæçèéêë́íîïđṇ̃óôơö÷øùúûüư₫ÿ"},ibm1163:"cp1163",csibm1163:"cp1163",maccroatian:{type:"_sbcs",chars:"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®Š™´¨≠ŽØ∞±≤≥∆µ∂∑∏š∫ªºΩžø¿¡¬√ƒ≈Ć«Č… ÀÃÕŒœĐ—“”‘’÷◊�©⁄¤‹›Æ»–·‚„‰ÂćÁčÈÍÎÏÌÓÔđÒÚÛÙıˆ˜¯πË˚¸Êæˇ"},maccyrillic:{type:"_sbcs",chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ†°¢£§•¶І®©™Ђђ≠Ѓѓ∞±≤≥іµ∂ЈЄєЇїЉљЊњјЅ¬√ƒ≈∆«»… ЋћЌќѕ–—“”‘’÷„ЎўЏџ№Ёёяабвгдежзийклмнопрстуфхцчшщъыьэю¤"},macgreek:{type:"_sbcs",chars:"Ä¹²É³ÖÜ΅àâä΄¨çéèêë£™îï•½‰ôö¦­ùûü†ΓΔΘΛΞΠß®©ΣΪ§≠°·Α±≤≥¥ΒΕΖΗΙΚΜΦΫΨΩάΝ¬ΟΡ≈Τ«»… ΥΧΆΈœ–―“”‘’÷ΉΊΌΎέήίόΏύαβψδεφγηιξκλμνοπώρστθωςχυζϊϋΐΰ�"},maciceland:{type:"_sbcs",chars:"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûüÝ°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄¤ÐðÞþý·‚„‰ÂÊÁËÈÍÎÏÌÓÔ�ÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ"},macroman:{type:"_sbcs",chars:"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄¤‹›ﬁﬂ‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔ�ÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ"},macromania:{type:"_sbcs",chars:"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ĂŞ∞±≤≥¥µ∂∑∏π∫ªºΩăş¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄¤‹›Ţţ‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔ�ÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ"},macthai:{type:"_sbcs",chars:"«»…“”�•‘’� กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู\uFEFF​–—฿เแโใไๅๆ็่้๊๋์ํ™๏๐๑๒๓๔๕๖๗๘๙®©����"},macturkish:{type:"_sbcs",chars:"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸĞğİıŞş‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔ�ÒÚÛÙ�ˆ˜¯˘˙˚¸˝˛ˇ"},macukraine:{type:"_sbcs",chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ†°Ґ£§•¶І®©™Ђђ≠Ѓѓ∞±≤≥іµґЈЄєЇїЉљЊњјЅ¬√ƒ≈∆«»… ЋћЌќѕ–—“”‘’÷„ЎўЏџ№Ёёяабвгдежзийклмнопрстуфхцчшщъыьэю¤"},koi8r:{type:"_sbcs",chars:"─│┌┐└┘├┤┬┴┼▀▄█▌▐░▒▓⌠■∙√≈≤≥ ⌡°²·÷═║╒ё╓╔╕╖╗╘╙╚╛╜╝╞╟╠╡Ё╢╣╤╥╦╧╨╩╪╫╬©юабцдефгхийклмнопярстужвьызшэщчъЮАБЦДЕФГХИЙКЛМНОПЯРСТУЖВЬЫЗШЭЩЧЪ"},koi8u:{type:"_sbcs",chars:"─│┌┐└┘├┤┬┴┼▀▄█▌▐░▒▓⌠■∙√≈≤≥ ⌡°²·÷═║╒ёє╔ії╗╘╙╚╛ґ╝╞╟╠╡ЁЄ╣ІЇ╦╧╨╩╪Ґ╬©юабцдефгхийклмнопярстужвьызшэщчъЮАБЦДЕФГХИЙКЛМНОПЯРСТУЖВЬЫЗШЭЩЧЪ"},koi8ru:{type:"_sbcs",chars:"─│┌┐└┘├┤┬┴┼▀▄█▌▐░▒▓⌠■∙√≈≤≥ ⌡°²·÷═║╒ёє╔ії╗╘╙╚╛ґў╞╟╠╡ЁЄ╣ІЇ╦╧╨╩╪ҐЎ©юабцдефгхийклмнопярстужвьызшэщчъЮАБЦДЕФГХИЙКЛМНОПЯРСТУЖВЬЫЗШЭЩЧЪ"},koi8t:{type:"_sbcs",chars:"қғ‚Ғ„…†‡�‰ҳ‹ҲҷҶ�Қ‘’“”•–—�™�›�����ӯӮё¤ӣ¦§���«¬­®�°±²Ё�Ӣ¶·�№�»���©юабцдефгхийклмнопярстужвьызшэщчъЮАБЦДЕФГХИЙКЛМНОПЯРСТУЖВЬЫЗШЭЩЧЪ"},armscii8:{type:"_sbcs",chars:" �և։)(»«—.՝,-֊…՜՛՞ԱաԲբԳգԴդԵեԶզԷէԸըԹթԺժԻիԼլԽխԾծԿկՀհՁձՂղՃճՄմՅյՆնՇշՈոՉչՊպՋջՌռՍսՎվՏտՐրՑցՒւՓփՔքՕօՖֆ՚�"},rk1048:{type:"_sbcs",chars:"ЂЃ‚ѓ„…†‡€‰Љ‹ЊҚҺЏђ‘’“”•–—�™љ›њқһџ ҰұӘ¤Ө¦§Ё©Ғ«¬­®Ү°±Ііөµ¶·ё№ғ»әҢңүАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя"},tcvn:{type:"_sbcs",chars:`\0ÚỤỪỬỮ\x07\b	
\v\f\rỨỰỲỶỸÝỴ\x1B !"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_\`abcdefghijklmnopqrstuvwxyz{|}~ÀẢÃÁẠẶẬÈẺẼÉẸỆÌỈĨÍỊÒỎÕÓỌỘỜỞỠỚỢÙỦŨ ĂÂÊÔƠƯĐăâêôơưđẶ̀̀̉̃́àảãáạẲằẳẵắẴẮẦẨẪẤỀặầẩẫấậèỂẻẽéẹềểễếệìỉỄẾỒĩíịòỔỏõóọồổỗốộờởỡớợùỖủũúụừửữứựỳỷỹýỵỐ`},georgianacademy:{type:"_sbcs",chars:"‚ƒ„…†‡ˆ‰Š‹Œ‘’“”•–—˜™š›œŸ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿აბგდევზთიკლმნოპჟრსტუფქღყშჩცძწჭხჯჰჱჲჳჴჵჶçèéêëìíîïðñòóôõö÷øùúûüýþÿ"},georgianps:{type:"_sbcs",chars:"‚ƒ„…†‡ˆ‰Š‹Œ‘’“”•–—˜™š›œŸ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿აბგდევზჱთიკლმნჲოპჟრსტჳუფქღყშჩცძწჭხჴჯჰჵæçèéêëìíîïðñòóôõö÷øùúûüýþÿ"},pt154:{type:"_sbcs",chars:"ҖҒӮғ„…ҶҮҲүҠӢҢҚҺҸҗ‘’“”•–—ҳҷҡӣңқһҹ ЎўЈӨҘҰ§Ё©Ә«¬ӯ®Ҝ°ұІіҙө¶·ё№ә»јҪҫҝАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя"},viscii:{type:"_sbcs",chars:`\0ẲẴẪ\x07\b	
\v\f\rỶỸ\x1BỴ !"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_\`abcdefghijklmnopqrstuvwxyz{|}~ẠẮẰẶẤẦẨẬẼẸẾỀỂỄỆỐỒỔỖỘỢỚỜỞỊỎỌỈỦŨỤỲÕắằặấầẩậẽẹếềểễệốồổỗỠƠộờởịỰỨỪỬơớƯÀÁÂÃẢĂẳẵÈÉÊẺÌÍĨỳĐứÒÓÔạỷừửÙÚỹỵÝỡưàáâãảăữẫèéêẻìíĩỉđựòóôõỏọụùúũủýợỮ`},iso646cn:{type:"_sbcs",chars:`\0\x07\b	
\v\f\r\x1B !"#¥%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_\`abcdefghijklmnopqrstuvwxyz{|}‾��������������������������������������������������������������������������������������������������������������������������������`},iso646jp:{type:"_sbcs",chars:`\0\x07\b	
\v\f\r\x1B !"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[¥]^_\`abcdefghijklmnopqrstuvwxyz{|}‾��������������������������������������������������������������������������������������������������������������������������������`},hproman8:{type:"_sbcs",chars:" ÀÂÈÊËÎÏ´ˋˆ¨˜ÙÛ₤¯Ýý°ÇçÑñ¡¿¤£¥§ƒ¢âêôûáéóúàèòùäëöüÅîØÆåíøæÄìÖÜÉïßÔÁÃãÐðÍÌÓÒÕõŠšÚŸÿÞþ·µ¶¾—¼½ªº«■»±�"},macintosh:{type:"_sbcs",chars:"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄¤‹›ﬁﬂ‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔ�ÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ"},ascii:{type:"_sbcs",chars:"��������������������������������������������������������������������������������������������������������������������������������"},tis620:{type:"_sbcs",chars:"���������������������������������กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู����฿เแโใไๅๆ็่้๊๋์ํ๎๏๐๑๒๓๔๕๖๗๘๙๚๛����"}}),Ye}var Ke={},_r;function bi(){if(_r)return Ke;_r=1;var s=ge.Buffer;Ke._dbcs=l;for(var e=-1,t=-2,r=-10,o=-1e3,n=new Array(256),i=-1,a=0;a<256;a++)n[a]=e;function l(w,h){if(this.encodingName=w.encodingName,!w)throw new Error("DBCS codec is called without the data.");if(!w.table)throw new Error("Encoding '"+this.encodingName+"' has no data.");var E=w.table();this.decodeTables=[],this.decodeTables[0]=n.slice(0),this.decodeTableSeq=[];for(var g=0;g<E.length;g++)this._addDecodeChunk(E[g]);if(typeof w.gb18030=="function"){this.gb18030=w.gb18030();var _=this.decodeTables.length;this.decodeTables.push(n.slice(0));var b=this.decodeTables.length;this.decodeTables.push(n.slice(0));for(var u=this.decodeTables[0],g=129;g<=254;g++)for(var c=this.decodeTables[o-u[g]],p=48;p<=57;p++){if(c[p]===e)c[p]=o-_;else if(c[p]>o)throw new Error("gb18030 decode tables conflict at byte 2");for(var v=this.decodeTables[o-c[p]],I=129;I<=254;I++){if(v[I]===e)v[I]=o-b;else{if(v[I]===o-b)continue;if(v[I]>o)throw new Error("gb18030 decode tables conflict at byte 3")}for(var N=this.decodeTables[o-v[I]],S=48;S<=57;S++)N[S]===e&&(N[S]=t)}}}this.defaultCharUnicode=h.defaultCharUnicode,this.encodeTable=[],this.encodeTableSeq=[];var d={};if(w.encodeSkipVals)for(var g=0;g<w.encodeSkipVals.length;g++){var D=w.encodeSkipVals[g];if(typeof D=="number")d[D]=!0;else for(var p=D.from;p<=D.to;p++)d[p]=!0}if(this._fillEncodeTable(0,0,d),w.encodeAdd)for(var L in w.encodeAdd)Object.prototype.hasOwnProperty.call(w.encodeAdd,L)&&this._setEncodeChar(L.charCodeAt(0),w.encodeAdd[L]);this.defCharSB=this.encodeTable[0][h.defaultCharSingleByte.charCodeAt(0)],this.defCharSB===e&&(this.defCharSB=this.encodeTable[0]["?"]),this.defCharSB===e&&(this.defCharSB=63)}l.prototype.encoder=f,l.prototype.decoder=m,l.prototype._getDecodeTrieNode=function(w){for(var h=[];w>0;w>>>=8)h.push(w&255);h.length==0&&h.push(0);for(var E=this.decodeTables[0],g=h.length-1;g>0;g--){var _=E[h[g]];if(_==e)E[h[g]]=o-this.decodeTables.length,this.decodeTables.push(E=n.slice(0));else if(_<=o)E=this.decodeTables[o-_];else throw new Error("Overwrite byte in "+this.encodingName+", addr: "+w.toString(16))}return E},l.prototype._addDecodeChunk=function(w){var h=parseInt(w[0],16),E=this._getDecodeTrieNode(h);h=h&255;for(var g=1;g<w.length;g++){var _=w[g];if(typeof _=="string")for(var b=0;b<_.length;){var u=_.charCodeAt(b++);if(55296<=u&&u<56320){var c=_.charCodeAt(b++);if(56320<=c&&c<57344)E[h++]=65536+(u-55296)*1024+(c-56320);else throw new Error("Incorrect surrogate pair in "+this.encodingName+" at chunk "+w[0])}else if(4080<u&&u<=4095){for(var p=4095-u+2,v=[],I=0;I<p;I++)v.push(_.charCodeAt(b++));E[h++]=r-this.decodeTableSeq.length,this.decodeTableSeq.push(v)}else E[h++]=u}else if(typeof _=="number")for(var N=E[h-1]+1,b=0;b<_;b++)E[h++]=N++;else throw new Error("Incorrect type '"+typeof _+"' given in "+this.encodingName+" at chunk "+w[0])}if(h>255)throw new Error("Incorrect chunk in "+this.encodingName+" at addr "+w[0]+": too long"+h)},l.prototype._getEncodeBucket=function(w){var h=w>>8;return this.encodeTable[h]===void 0&&(this.encodeTable[h]=n.slice(0)),this.encodeTable[h]},l.prototype._setEncodeChar=function(w,h){var E=this._getEncodeBucket(w),g=w&255;E[g]<=r?this.encodeTableSeq[r-E[g]][i]=h:E[g]==e&&(E[g]=h)},l.prototype._setEncodeSequence=function(w,h){var E=w[0],g=this._getEncodeBucket(E),_=E&255,b;g[_]<=r?b=this.encodeTableSeq[r-g[_]]:(b={},g[_]!==e&&(b[i]=g[_]),g[_]=r-this.encodeTableSeq.length,this.encodeTableSeq.push(b));for(var u=1;u<w.length-1;u++){var c=b[E];typeof c=="object"?b=c:(b=b[E]={},c!==void 0&&(b[i]=c))}E=w[w.length-1],b[E]=h},l.prototype._fillEncodeTable=function(w,h,E){for(var g=this.decodeTables[w],_=!1,b={},u=0;u<256;u++){var c=g[u],p=h+u;if(!E[p])if(c>=0)this._setEncodeChar(c,p),_=!0;else if(c<=o){var v=o-c;if(!b[v]){var I=p<<8>>>0;this._fillEncodeTable(v,I,E)?_=!0:b[v]=!0}}else c<=r&&(this._setEncodeSequence(this.decodeTableSeq[r-c],p),_=!0)}return _};function f(w,h){this.leadSurrogate=-1,this.seqObj=void 0,this.encodeTable=h.encodeTable,this.encodeTableSeq=h.encodeTableSeq,this.defaultCharSingleByte=h.defCharSB,this.gb18030=h.gb18030}f.prototype.write=function(w){for(var h=s.alloc(w.length*(this.gb18030?4:3)),E=this.leadSurrogate,g=this.seqObj,_=-1,b=0,u=0;;){if(_===-1){if(b==w.length)break;var c=w.charCodeAt(b++)}else{var c=_;_=-1}if(55296<=c&&c<57344)if(c<56320)if(E===-1){E=c;continue}else E=c,c=e;else E!==-1?(c=65536+(E-55296)*1024+(c-56320),E=-1):c=e;else E!==-1&&(_=c,c=e,E=-1);var p=e;if(g!==void 0&&c!=e){var v=g[c];if(typeof v=="object"){g=v;continue}else typeof v=="number"?p=v:v==null&&(v=g[i],v!==void 0&&(p=v,_=c));g=void 0}else if(c>=0){var I=this.encodeTable[c>>8];if(I!==void 0&&(p=I[c&255]),p<=r){g=this.encodeTableSeq[r-p];continue}if(p==e&&this.gb18030){var N=y(this.gb18030.uChars,c);if(N!=-1){var p=this.gb18030.gbChars[N]+(c-this.gb18030.uChars[N]);h[u++]=129+Math.floor(p/12600),p=p%12600,h[u++]=48+Math.floor(p/1260),p=p%1260,h[u++]=129+Math.floor(p/10),p=p%10,h[u++]=48+p;continue}}}p===e&&(p=this.defaultCharSingleByte),p<256?h[u++]=p:p<65536?(h[u++]=p>>8,h[u++]=p&255):p<16777216?(h[u++]=p>>16,h[u++]=p>>8&255,h[u++]=p&255):(h[u++]=p>>>24,h[u++]=p>>>16&255,h[u++]=p>>>8&255,h[u++]=p&255)}return this.seqObj=g,this.leadSurrogate=E,h.slice(0,u)},f.prototype.end=function(){if(!(this.leadSurrogate===-1&&this.seqObj===void 0)){var w=s.alloc(10),h=0;if(this.seqObj){var E=this.seqObj[i];E!==void 0&&(E<256?w[h++]=E:(w[h++]=E>>8,w[h++]=E&255)),this.seqObj=void 0}return this.leadSurrogate!==-1&&(w[h++]=this.defaultCharSingleByte,this.leadSurrogate=-1),w.slice(0,h)}},f.prototype.findIdx=y;function m(w,h){this.nodeIdx=0,this.prevBytes=[],this.decodeTables=h.decodeTables,this.decodeTableSeq=h.decodeTableSeq,this.defaultCharUnicode=h.defaultCharUnicode,this.gb18030=h.gb18030}m.prototype.write=function(w){for(var h=s.alloc(w.length*2),E=this.nodeIdx,g=this.prevBytes,_=this.prevBytes.length,b=-this.prevBytes.length,u,c=0,p=0;c<w.length;c++){var v=c>=0?w[c]:g[c+_],u=this.decodeTables[E][v];if(!(u>=0))if(u===e)u=this.defaultCharUnicode.charCodeAt(0),c=b;else if(u===t){if(c>=3)var I=(w[c-3]-129)*12600+(w[c-2]-48)*1260+(w[c-1]-129)*10+(v-48);else var I=(g[c-3+_]-129)*12600+((c-2>=0?w[c-2]:g[c-2+_])-48)*1260+((c-1>=0?w[c-1]:g[c-1+_])-129)*10+(v-48);var N=y(this.gb18030.gbChars,I);u=this.gb18030.uChars[N]+I-this.gb18030.gbChars[N]}else if(u<=o){E=o-u;continue}else if(u<=r){for(var S=this.decodeTableSeq[r-u],d=0;d<S.length-1;d++)u=S[d],h[p++]=u&255,h[p++]=u>>8;u=S[S.length-1]}else throw new Error("iconv-lite internal error: invalid decoding table value "+u+" at "+E+"/"+v);if(u>=65536){u-=65536;var D=55296|u>>10;h[p++]=D&255,h[p++]=D>>8,u=56320|u&1023}h[p++]=u&255,h[p++]=u>>8,E=0,b=c+1}return this.nodeIdx=E,this.prevBytes=b>=0?Array.prototype.slice.call(w,b):g.slice(b+_).concat(Array.prototype.slice.call(w)),h.slice(0,p).toString("ucs2")},m.prototype.end=function(){for(var w="";this.prevBytes.length>0;){w+=this.defaultCharUnicode;var h=this.prevBytes.slice(1);this.prevBytes=[],this.nodeIdx=0,h.length>0&&(w+=this.write(h))}return this.prevBytes=[],this.nodeIdx=0,w};function y(w,h){if(w[0]>h)return-1;for(var E=0,g=w.length;E<g-1;){var _=E+(g-E+1>>1);w[_]<=h?E=_:g=_}return E}return Ke}const wi=[["0","\0",128],["a1","｡",62],["8140","　、。，．・：；？！゛゜´｀¨＾￣＿ヽヾゝゞ〃仝々〆〇ー―‐／＼～∥｜…‥‘’“”（）〔〕［］｛｝〈",9,"＋－±×"],["8180","÷＝≠＜＞≦≧∞∴♂♀°′″℃￥＄￠￡％＃＆＊＠§☆★○●◎◇◆□■△▲▽▼※〒→←↑↓〓"],["81b8","∈∋⊆⊇⊂⊃∪∩"],["81c8","∧∨￢⇒⇔∀∃"],["81da","∠⊥⌒∂∇≡≒≪≫√∽∝∵∫∬"],["81f0","Å‰♯♭♪†‡¶"],["81fc","◯"],["824f","０",9],["8260","Ａ",25],["8281","ａ",25],["829f","ぁ",82],["8340","ァ",62],["8380","ム",22],["839f","Α",16,"Σ",6],["83bf","α",16,"σ",6],["8440","А",5,"ЁЖ",25],["8470","а",5,"ёж",7],["8480","о",17],["849f","─│┌┐┘└├┬┤┴┼━┃┏┓┛┗┣┳┫┻╋┠┯┨┷┿┝┰┥┸╂"],["8740","①",19,"Ⅰ",9],["875f","㍉㌔㌢㍍㌘㌧㌃㌶㍑㍗㌍㌦㌣㌫㍊㌻㎜㎝㎞㎎㎏㏄㎡"],["877e","㍻"],["8780","〝〟№㏍℡㊤",4,"㈱㈲㈹㍾㍽㍼≒≡∫∮∑√⊥∠∟⊿∵∩∪"],["889f","亜唖娃阿哀愛挨姶逢葵茜穐悪握渥旭葦芦鯵梓圧斡扱宛姐虻飴絢綾鮎或粟袷安庵按暗案闇鞍杏以伊位依偉囲夷委威尉惟意慰易椅為畏異移維緯胃萎衣謂違遺医井亥域育郁磯一壱溢逸稲茨芋鰯允印咽員因姻引飲淫胤蔭"],["8940","院陰隠韻吋右宇烏羽迂雨卯鵜窺丑碓臼渦嘘唄欝蔚鰻姥厩浦瓜閏噂云運雲荏餌叡営嬰影映曳栄永泳洩瑛盈穎頴英衛詠鋭液疫益駅悦謁越閲榎厭円"],["8980","園堰奄宴延怨掩援沿演炎焔煙燕猿縁艶苑薗遠鉛鴛塩於汚甥凹央奥往応押旺横欧殴王翁襖鴬鴎黄岡沖荻億屋憶臆桶牡乙俺卸恩温穏音下化仮何伽価佳加可嘉夏嫁家寡科暇果架歌河火珂禍禾稼箇花苛茄荷華菓蝦課嘩貨迦過霞蚊俄峨我牙画臥芽蛾賀雅餓駕介会解回塊壊廻快怪悔恢懐戒拐改"],["8a40","魁晦械海灰界皆絵芥蟹開階貝凱劾外咳害崖慨概涯碍蓋街該鎧骸浬馨蛙垣柿蛎鈎劃嚇各廓拡撹格核殻獲確穫覚角赫較郭閣隔革学岳楽額顎掛笠樫"],["8a80","橿梶鰍潟割喝恰括活渇滑葛褐轄且鰹叶椛樺鞄株兜竃蒲釜鎌噛鴨栢茅萱粥刈苅瓦乾侃冠寒刊勘勧巻喚堪姦完官寛干幹患感慣憾換敢柑桓棺款歓汗漢澗潅環甘監看竿管簡緩缶翰肝艦莞観諌貫還鑑間閑関陥韓館舘丸含岸巌玩癌眼岩翫贋雁頑顔願企伎危喜器基奇嬉寄岐希幾忌揮机旗既期棋棄"],["8b40","機帰毅気汽畿祈季稀紀徽規記貴起軌輝飢騎鬼亀偽儀妓宜戯技擬欺犠疑祇義蟻誼議掬菊鞠吉吃喫桔橘詰砧杵黍却客脚虐逆丘久仇休及吸宮弓急救"],["8b80","朽求汲泣灸球究窮笈級糾給旧牛去居巨拒拠挙渠虚許距鋸漁禦魚亨享京供侠僑兇競共凶協匡卿叫喬境峡強彊怯恐恭挟教橋況狂狭矯胸脅興蕎郷鏡響饗驚仰凝尭暁業局曲極玉桐粁僅勤均巾錦斤欣欽琴禁禽筋緊芹菌衿襟謹近金吟銀九倶句区狗玖矩苦躯駆駈駒具愚虞喰空偶寓遇隅串櫛釧屑屈"],["8c40","掘窟沓靴轡窪熊隈粂栗繰桑鍬勲君薫訓群軍郡卦袈祁係傾刑兄啓圭珪型契形径恵慶慧憩掲携敬景桂渓畦稽系経継繋罫茎荊蛍計詣警軽頚鶏芸迎鯨"],["8c80","劇戟撃激隙桁傑欠決潔穴結血訣月件倹倦健兼券剣喧圏堅嫌建憲懸拳捲検権牽犬献研硯絹県肩見謙賢軒遣鍵険顕験鹸元原厳幻弦減源玄現絃舷言諺限乎個古呼固姑孤己庫弧戸故枯湖狐糊袴股胡菰虎誇跨鈷雇顧鼓五互伍午呉吾娯後御悟梧檎瑚碁語誤護醐乞鯉交佼侯候倖光公功効勾厚口向"],["8d40","后喉坑垢好孔孝宏工巧巷幸広庚康弘恒慌抗拘控攻昂晃更杭校梗構江洪浩港溝甲皇硬稿糠紅紘絞綱耕考肯肱腔膏航荒行衡講貢購郊酵鉱砿鋼閤降"],["8d80","項香高鴻剛劫号合壕拷濠豪轟麹克刻告国穀酷鵠黒獄漉腰甑忽惚骨狛込此頃今困坤墾婚恨懇昏昆根梱混痕紺艮魂些佐叉唆嵯左差査沙瑳砂詐鎖裟坐座挫債催再最哉塞妻宰彩才採栽歳済災采犀砕砦祭斎細菜裁載際剤在材罪財冴坂阪堺榊肴咲崎埼碕鷺作削咋搾昨朔柵窄策索錯桜鮭笹匙冊刷"],["8e40","察拶撮擦札殺薩雑皐鯖捌錆鮫皿晒三傘参山惨撒散桟燦珊産算纂蚕讃賛酸餐斬暫残仕仔伺使刺司史嗣四士始姉姿子屍市師志思指支孜斯施旨枝止"],["8e80","死氏獅祉私糸紙紫肢脂至視詞詩試誌諮資賜雌飼歯事似侍児字寺慈持時次滋治爾璽痔磁示而耳自蒔辞汐鹿式識鴫竺軸宍雫七叱執失嫉室悉湿漆疾質実蔀篠偲柴芝屡蕊縞舎写射捨赦斜煮社紗者謝車遮蛇邪借勺尺杓灼爵酌釈錫若寂弱惹主取守手朱殊狩珠種腫趣酒首儒受呪寿授樹綬需囚収周"],["8f40","宗就州修愁拾洲秀秋終繍習臭舟蒐衆襲讐蹴輯週酋酬集醜什住充十従戎柔汁渋獣縦重銃叔夙宿淑祝縮粛塾熟出術述俊峻春瞬竣舜駿准循旬楯殉淳"],["8f80","準潤盾純巡遵醇順処初所暑曙渚庶緒署書薯藷諸助叙女序徐恕鋤除傷償勝匠升召哨商唱嘗奨妾娼宵将小少尚庄床廠彰承抄招掌捷昇昌昭晶松梢樟樵沼消渉湘焼焦照症省硝礁祥称章笑粧紹肖菖蒋蕉衝裳訟証詔詳象賞醤鉦鍾鐘障鞘上丈丞乗冗剰城場壌嬢常情擾条杖浄状畳穣蒸譲醸錠嘱埴飾"],["9040","拭植殖燭織職色触食蝕辱尻伸信侵唇娠寝審心慎振新晋森榛浸深申疹真神秦紳臣芯薪親診身辛進針震人仁刃塵壬尋甚尽腎訊迅陣靭笥諏須酢図厨"],["9080","逗吹垂帥推水炊睡粋翠衰遂酔錐錘随瑞髄崇嵩数枢趨雛据杉椙菅頗雀裾澄摺寸世瀬畝是凄制勢姓征性成政整星晴棲栖正清牲生盛精聖声製西誠誓請逝醒青静斉税脆隻席惜戚斥昔析石積籍績脊責赤跡蹟碩切拙接摂折設窃節説雪絶舌蝉仙先千占宣専尖川戦扇撰栓栴泉浅洗染潜煎煽旋穿箭線"],["9140","繊羨腺舛船薦詮賎践選遷銭銑閃鮮前善漸然全禅繕膳糎噌塑岨措曾曽楚狙疏疎礎祖租粗素組蘇訴阻遡鼠僧創双叢倉喪壮奏爽宋層匝惣想捜掃挿掻"],["9180","操早曹巣槍槽漕燥争痩相窓糟総綜聡草荘葬蒼藻装走送遭鎗霜騒像増憎臓蔵贈造促側則即息捉束測足速俗属賊族続卒袖其揃存孫尊損村遜他多太汰詑唾堕妥惰打柁舵楕陀駄騨体堆対耐岱帯待怠態戴替泰滞胎腿苔袋貸退逮隊黛鯛代台大第醍題鷹滝瀧卓啄宅托択拓沢濯琢託鐸濁諾茸凧蛸只"],["9240","叩但達辰奪脱巽竪辿棚谷狸鱈樽誰丹単嘆坦担探旦歎淡湛炭短端箪綻耽胆蛋誕鍛団壇弾断暖檀段男談値知地弛恥智池痴稚置致蜘遅馳築畜竹筑蓄"],["9280","逐秩窒茶嫡着中仲宙忠抽昼柱注虫衷註酎鋳駐樗瀦猪苧著貯丁兆凋喋寵帖帳庁弔張彫徴懲挑暢朝潮牒町眺聴脹腸蝶調諜超跳銚長頂鳥勅捗直朕沈珍賃鎮陳津墜椎槌追鎚痛通塚栂掴槻佃漬柘辻蔦綴鍔椿潰坪壷嬬紬爪吊釣鶴亭低停偵剃貞呈堤定帝底庭廷弟悌抵挺提梯汀碇禎程締艇訂諦蹄逓"],["9340","邸鄭釘鼎泥摘擢敵滴的笛適鏑溺哲徹撤轍迭鉄典填天展店添纏甜貼転顛点伝殿澱田電兎吐堵塗妬屠徒斗杜渡登菟賭途都鍍砥砺努度土奴怒倒党冬"],["9380","凍刀唐塔塘套宕島嶋悼投搭東桃梼棟盗淘湯涛灯燈当痘祷等答筒糖統到董蕩藤討謄豆踏逃透鐙陶頭騰闘働動同堂導憧撞洞瞳童胴萄道銅峠鴇匿得徳涜特督禿篤毒独読栃橡凸突椴届鳶苫寅酉瀞噸屯惇敦沌豚遁頓呑曇鈍奈那内乍凪薙謎灘捺鍋楢馴縄畷南楠軟難汝二尼弐迩匂賑肉虹廿日乳入"],["9440","如尿韮任妊忍認濡禰祢寧葱猫熱年念捻撚燃粘乃廼之埜嚢悩濃納能脳膿農覗蚤巴把播覇杷波派琶破婆罵芭馬俳廃拝排敗杯盃牌背肺輩配倍培媒梅"],["9480","楳煤狽買売賠陪這蝿秤矧萩伯剥博拍柏泊白箔粕舶薄迫曝漠爆縛莫駁麦函箱硲箸肇筈櫨幡肌畑畠八鉢溌発醗髪伐罰抜筏閥鳩噺塙蛤隼伴判半反叛帆搬斑板氾汎版犯班畔繁般藩販範釆煩頒飯挽晩番盤磐蕃蛮匪卑否妃庇彼悲扉批披斐比泌疲皮碑秘緋罷肥被誹費避非飛樋簸備尾微枇毘琵眉美"],["9540","鼻柊稗匹疋髭彦膝菱肘弼必畢筆逼桧姫媛紐百謬俵彪標氷漂瓢票表評豹廟描病秒苗錨鋲蒜蛭鰭品彬斌浜瀕貧賓頻敏瓶不付埠夫婦富冨布府怖扶敷"],["9580","斧普浮父符腐膚芙譜負賦赴阜附侮撫武舞葡蕪部封楓風葺蕗伏副復幅服福腹複覆淵弗払沸仏物鮒分吻噴墳憤扮焚奮粉糞紛雰文聞丙併兵塀幣平弊柄並蔽閉陛米頁僻壁癖碧別瞥蔑箆偏変片篇編辺返遍便勉娩弁鞭保舗鋪圃捕歩甫補輔穂募墓慕戊暮母簿菩倣俸包呆報奉宝峰峯崩庖抱捧放方朋"],["9640","法泡烹砲縫胞芳萌蓬蜂褒訪豊邦鋒飽鳳鵬乏亡傍剖坊妨帽忘忙房暴望某棒冒紡肪膨謀貌貿鉾防吠頬北僕卜墨撲朴牧睦穆釦勃没殆堀幌奔本翻凡盆"],["9680","摩磨魔麻埋妹昧枚毎哩槙幕膜枕鮪柾鱒桝亦俣又抹末沫迄侭繭麿万慢満漫蔓味未魅巳箕岬密蜜湊蓑稔脈妙粍民眠務夢無牟矛霧鵡椋婿娘冥名命明盟迷銘鳴姪牝滅免棉綿緬面麺摸模茂妄孟毛猛盲網耗蒙儲木黙目杢勿餅尤戻籾貰問悶紋門匁也冶夜爺耶野弥矢厄役約薬訳躍靖柳薮鑓愉愈油癒"],["9740","諭輸唯佑優勇友宥幽悠憂揖有柚湧涌猶猷由祐裕誘遊邑郵雄融夕予余与誉輿預傭幼妖容庸揚揺擁曜楊様洋溶熔用窯羊耀葉蓉要謡踊遥陽養慾抑欲"],["9780","沃浴翌翼淀羅螺裸来莱頼雷洛絡落酪乱卵嵐欄濫藍蘭覧利吏履李梨理璃痢裏裡里離陸律率立葎掠略劉流溜琉留硫粒隆竜龍侶慮旅虜了亮僚両凌寮料梁涼猟療瞭稜糧良諒遼量陵領力緑倫厘林淋燐琳臨輪隣鱗麟瑠塁涙累類令伶例冷励嶺怜玲礼苓鈴隷零霊麗齢暦歴列劣烈裂廉恋憐漣煉簾練聯"],["9840","蓮連錬呂魯櫓炉賂路露労婁廊弄朗楼榔浪漏牢狼篭老聾蝋郎六麓禄肋録論倭和話歪賄脇惑枠鷲亙亘鰐詫藁蕨椀湾碗腕"],["989f","弌丐丕个丱丶丼丿乂乖乘亂亅豫亊舒弍于亞亟亠亢亰亳亶从仍仄仆仂仗仞仭仟价伉佚估佛佝佗佇佶侈侏侘佻佩佰侑佯來侖儘俔俟俎俘俛俑俚俐俤俥倚倨倔倪倥倅伜俶倡倩倬俾俯們倆偃假會偕偐偈做偖偬偸傀傚傅傴傲"],["9940","僉僊傳僂僖僞僥僭僣僮價僵儉儁儂儖儕儔儚儡儺儷儼儻儿兀兒兌兔兢竸兩兪兮冀冂囘册冉冏冑冓冕冖冤冦冢冩冪冫决冱冲冰况冽凅凉凛几處凩凭"],["9980","凰凵凾刄刋刔刎刧刪刮刳刹剏剄剋剌剞剔剪剴剩剳剿剽劍劔劒剱劈劑辨辧劬劭劼劵勁勍勗勞勣勦飭勠勳勵勸勹匆匈甸匍匐匏匕匚匣匯匱匳匸區卆卅丗卉卍凖卞卩卮夘卻卷厂厖厠厦厥厮厰厶參簒雙叟曼燮叮叨叭叺吁吽呀听吭吼吮吶吩吝呎咏呵咎呟呱呷呰咒呻咀呶咄咐咆哇咢咸咥咬哄哈咨"],["9a40","咫哂咤咾咼哘哥哦唏唔哽哮哭哺哢唹啀啣啌售啜啅啖啗唸唳啝喙喀咯喊喟啻啾喘喞單啼喃喩喇喨嗚嗅嗟嗄嗜嗤嗔嘔嗷嘖嗾嗽嘛嗹噎噐營嘴嘶嘲嘸"],["9a80","噫噤嘯噬噪嚆嚀嚊嚠嚔嚏嚥嚮嚶嚴囂嚼囁囃囀囈囎囑囓囗囮囹圀囿圄圉圈國圍圓團圖嗇圜圦圷圸坎圻址坏坩埀垈坡坿垉垓垠垳垤垪垰埃埆埔埒埓堊埖埣堋堙堝塲堡塢塋塰毀塒堽塹墅墹墟墫墺壞墻墸墮壅壓壑壗壙壘壥壜壤壟壯壺壹壻壼壽夂夊夐夛梦夥夬夭夲夸夾竒奕奐奎奚奘奢奠奧奬奩"],["9b40","奸妁妝佞侫妣妲姆姨姜妍姙姚娥娟娑娜娉娚婀婬婉娵娶婢婪媚媼媾嫋嫂媽嫣嫗嫦嫩嫖嫺嫻嬌嬋嬖嬲嫐嬪嬶嬾孃孅孀孑孕孚孛孥孩孰孳孵學斈孺宀"],["9b80","它宦宸寃寇寉寔寐寤實寢寞寥寫寰寶寳尅將專對尓尠尢尨尸尹屁屆屎屓屐屏孱屬屮乢屶屹岌岑岔妛岫岻岶岼岷峅岾峇峙峩峽峺峭嶌峪崋崕崗嵜崟崛崑崔崢崚崙崘嵌嵒嵎嵋嵬嵳嵶嶇嶄嶂嶢嶝嶬嶮嶽嶐嶷嶼巉巍巓巒巖巛巫已巵帋帚帙帑帛帶帷幄幃幀幎幗幔幟幢幤幇幵并幺麼广庠廁廂廈廐廏"],["9c40","廖廣廝廚廛廢廡廨廩廬廱廳廰廴廸廾弃弉彝彜弋弑弖弩弭弸彁彈彌彎弯彑彖彗彙彡彭彳彷徃徂彿徊很徑徇從徙徘徠徨徭徼忖忻忤忸忱忝悳忿怡恠"],["9c80","怙怐怩怎怱怛怕怫怦怏怺恚恁恪恷恟恊恆恍恣恃恤恂恬恫恙悁悍惧悃悚悄悛悖悗悒悧悋惡悸惠惓悴忰悽惆悵惘慍愕愆惶惷愀惴惺愃愡惻惱愍愎慇愾愨愧慊愿愼愬愴愽慂慄慳慷慘慙慚慫慴慯慥慱慟慝慓慵憙憖憇憬憔憚憊憑憫憮懌懊應懷懈懃懆憺懋罹懍懦懣懶懺懴懿懽懼懾戀戈戉戍戌戔戛"],["9d40","戞戡截戮戰戲戳扁扎扞扣扛扠扨扼抂抉找抒抓抖拔抃抔拗拑抻拏拿拆擔拈拜拌拊拂拇抛拉挌拮拱挧挂挈拯拵捐挾捍搜捏掖掎掀掫捶掣掏掉掟掵捫"],["9d80","捩掾揩揀揆揣揉插揶揄搖搴搆搓搦搶攝搗搨搏摧摯摶摎攪撕撓撥撩撈撼據擒擅擇撻擘擂擱擧舉擠擡抬擣擯攬擶擴擲擺攀擽攘攜攅攤攣攫攴攵攷收攸畋效敖敕敍敘敞敝敲數斂斃變斛斟斫斷旃旆旁旄旌旒旛旙无旡旱杲昊昃旻杳昵昶昴昜晏晄晉晁晞晝晤晧晨晟晢晰暃暈暎暉暄暘暝曁暹曉暾暼"],["9e40","曄暸曖曚曠昿曦曩曰曵曷朏朖朞朦朧霸朮朿朶杁朸朷杆杞杠杙杣杤枉杰枩杼杪枌枋枦枡枅枷柯枴柬枳柩枸柤柞柝柢柮枹柎柆柧檜栞框栩桀桍栲桎"],["9e80","梳栫桙档桷桿梟梏梭梔條梛梃檮梹桴梵梠梺椏梍桾椁棊椈棘椢椦棡椌棍棔棧棕椶椒椄棗棣椥棹棠棯椨椪椚椣椡棆楹楷楜楸楫楔楾楮椹楴椽楙椰楡楞楝榁楪榲榮槐榿槁槓榾槎寨槊槝榻槃榧樮榑榠榜榕榴槞槨樂樛槿權槹槲槧樅榱樞槭樔槫樊樒櫁樣樓橄樌橲樶橸橇橢橙橦橈樸樢檐檍檠檄檢檣"],["9f40","檗蘗檻櫃櫂檸檳檬櫞櫑櫟檪櫚櫪櫻欅蘖櫺欒欖鬱欟欸欷盜欹飮歇歃歉歐歙歔歛歟歡歸歹歿殀殄殃殍殘殕殞殤殪殫殯殲殱殳殷殼毆毋毓毟毬毫毳毯"],["9f80","麾氈氓气氛氤氣汞汕汢汪沂沍沚沁沛汾汨汳沒沐泄泱泓沽泗泅泝沮沱沾沺泛泯泙泪洟衍洶洫洽洸洙洵洳洒洌浣涓浤浚浹浙涎涕濤涅淹渕渊涵淇淦涸淆淬淞淌淨淒淅淺淙淤淕淪淮渭湮渮渙湲湟渾渣湫渫湶湍渟湃渺湎渤滿渝游溂溪溘滉溷滓溽溯滄溲滔滕溏溥滂溟潁漑灌滬滸滾漿滲漱滯漲滌"],["e040","漾漓滷澆潺潸澁澀潯潛濳潭澂潼潘澎澑濂潦澳澣澡澤澹濆澪濟濕濬濔濘濱濮濛瀉瀋濺瀑瀁瀏濾瀛瀚潴瀝瀘瀟瀰瀾瀲灑灣炙炒炯烱炬炸炳炮烟烋烝"],["e080","烙焉烽焜焙煥煕熈煦煢煌煖煬熏燻熄熕熨熬燗熹熾燒燉燔燎燠燬燧燵燼燹燿爍爐爛爨爭爬爰爲爻爼爿牀牆牋牘牴牾犂犁犇犒犖犢犧犹犲狃狆狄狎狒狢狠狡狹狷倏猗猊猜猖猝猴猯猩猥猾獎獏默獗獪獨獰獸獵獻獺珈玳珎玻珀珥珮珞璢琅瑯琥珸琲琺瑕琿瑟瑙瑁瑜瑩瑰瑣瑪瑶瑾璋璞璧瓊瓏瓔珱"],["e140","瓠瓣瓧瓩瓮瓲瓰瓱瓸瓷甄甃甅甌甎甍甕甓甞甦甬甼畄畍畊畉畛畆畚畩畤畧畫畭畸當疆疇畴疊疉疂疔疚疝疥疣痂疳痃疵疽疸疼疱痍痊痒痙痣痞痾痿"],["e180","痼瘁痰痺痲痳瘋瘍瘉瘟瘧瘠瘡瘢瘤瘴瘰瘻癇癈癆癜癘癡癢癨癩癪癧癬癰癲癶癸發皀皃皈皋皎皖皓皙皚皰皴皸皹皺盂盍盖盒盞盡盥盧盪蘯盻眈眇眄眩眤眞眥眦眛眷眸睇睚睨睫睛睥睿睾睹瞎瞋瞑瞠瞞瞰瞶瞹瞿瞼瞽瞻矇矍矗矚矜矣矮矼砌砒礦砠礪硅碎硴碆硼碚碌碣碵碪碯磑磆磋磔碾碼磅磊磬"],["e240","磧磚磽磴礇礒礑礙礬礫祀祠祗祟祚祕祓祺祿禊禝禧齋禪禮禳禹禺秉秕秧秬秡秣稈稍稘稙稠稟禀稱稻稾稷穃穗穉穡穢穩龝穰穹穽窈窗窕窘窖窩竈窰"],["e280","窶竅竄窿邃竇竊竍竏竕竓站竚竝竡竢竦竭竰笂笏笊笆笳笘笙笞笵笨笶筐筺笄筍笋筌筅筵筥筴筧筰筱筬筮箝箘箟箍箜箚箋箒箏筝箙篋篁篌篏箴篆篝篩簑簔篦篥籠簀簇簓篳篷簗簍篶簣簧簪簟簷簫簽籌籃籔籏籀籐籘籟籤籖籥籬籵粃粐粤粭粢粫粡粨粳粲粱粮粹粽糀糅糂糘糒糜糢鬻糯糲糴糶糺紆"],["e340","紂紜紕紊絅絋紮紲紿紵絆絳絖絎絲絨絮絏絣經綉絛綏絽綛綺綮綣綵緇綽綫總綢綯緜綸綟綰緘緝緤緞緻緲緡縅縊縣縡縒縱縟縉縋縢繆繦縻縵縹繃縷"],["e380","縲縺繧繝繖繞繙繚繹繪繩繼繻纃緕繽辮繿纈纉續纒纐纓纔纖纎纛纜缸缺罅罌罍罎罐网罕罔罘罟罠罨罩罧罸羂羆羃羈羇羌羔羞羝羚羣羯羲羹羮羶羸譱翅翆翊翕翔翡翦翩翳翹飜耆耄耋耒耘耙耜耡耨耿耻聊聆聒聘聚聟聢聨聳聲聰聶聹聽聿肄肆肅肛肓肚肭冐肬胛胥胙胝胄胚胖脉胯胱脛脩脣脯腋"],["e440","隋腆脾腓腑胼腱腮腥腦腴膃膈膊膀膂膠膕膤膣腟膓膩膰膵膾膸膽臀臂膺臉臍臑臙臘臈臚臟臠臧臺臻臾舁舂舅與舊舍舐舖舩舫舸舳艀艙艘艝艚艟艤"],["e480","艢艨艪艫舮艱艷艸艾芍芒芫芟芻芬苡苣苟苒苴苳苺莓范苻苹苞茆苜茉苙茵茴茖茲茱荀茹荐荅茯茫茗茘莅莚莪莟莢莖茣莎莇莊荼莵荳荵莠莉莨菴萓菫菎菽萃菘萋菁菷萇菠菲萍萢萠莽萸蔆菻葭萪萼蕚蒄葷葫蒭葮蒂葩葆萬葯葹萵蓊葢蒹蒿蒟蓙蓍蒻蓚蓐蓁蓆蓖蒡蔡蓿蓴蔗蔘蔬蔟蔕蔔蓼蕀蕣蕘蕈"],["e540","蕁蘂蕋蕕薀薤薈薑薊薨蕭薔薛藪薇薜蕷蕾薐藉薺藏薹藐藕藝藥藜藹蘊蘓蘋藾藺蘆蘢蘚蘰蘿虍乕虔號虧虱蚓蚣蚩蚪蚋蚌蚶蚯蛄蛆蚰蛉蠣蚫蛔蛞蛩蛬"],["e580","蛟蛛蛯蜒蜆蜈蜀蜃蛻蜑蜉蜍蛹蜊蜴蜿蜷蜻蜥蜩蜚蝠蝟蝸蝌蝎蝴蝗蝨蝮蝙蝓蝣蝪蠅螢螟螂螯蟋螽蟀蟐雖螫蟄螳蟇蟆螻蟯蟲蟠蠏蠍蟾蟶蟷蠎蟒蠑蠖蠕蠢蠡蠱蠶蠹蠧蠻衄衂衒衙衞衢衫袁衾袞衵衽袵衲袂袗袒袮袙袢袍袤袰袿袱裃裄裔裘裙裝裹褂裼裴裨裲褄褌褊褓襃褞褥褪褫襁襄褻褶褸襌褝襠襞"],["e640","襦襤襭襪襯襴襷襾覃覈覊覓覘覡覩覦覬覯覲覺覽覿觀觚觜觝觧觴觸訃訖訐訌訛訝訥訶詁詛詒詆詈詼詭詬詢誅誂誄誨誡誑誥誦誚誣諄諍諂諚諫諳諧"],["e680","諤諱謔諠諢諷諞諛謌謇謚諡謖謐謗謠謳鞫謦謫謾謨譁譌譏譎證譖譛譚譫譟譬譯譴譽讀讌讎讒讓讖讙讚谺豁谿豈豌豎豐豕豢豬豸豺貂貉貅貊貍貎貔豼貘戝貭貪貽貲貳貮貶賈賁賤賣賚賽賺賻贄贅贊贇贏贍贐齎贓賍贔贖赧赭赱赳趁趙跂趾趺跏跚跖跌跛跋跪跫跟跣跼踈踉跿踝踞踐踟蹂踵踰踴蹊"],["e740","蹇蹉蹌蹐蹈蹙蹤蹠踪蹣蹕蹶蹲蹼躁躇躅躄躋躊躓躑躔躙躪躡躬躰軆躱躾軅軈軋軛軣軼軻軫軾輊輅輕輒輙輓輜輟輛輌輦輳輻輹轅轂輾轌轉轆轎轗轜"],["e780","轢轣轤辜辟辣辭辯辷迚迥迢迪迯邇迴逅迹迺逑逕逡逍逞逖逋逧逶逵逹迸遏遐遑遒逎遉逾遖遘遞遨遯遶隨遲邂遽邁邀邊邉邏邨邯邱邵郢郤扈郛鄂鄒鄙鄲鄰酊酖酘酣酥酩酳酲醋醉醂醢醫醯醪醵醴醺釀釁釉釋釐釖釟釡釛釼釵釶鈞釿鈔鈬鈕鈑鉞鉗鉅鉉鉤鉈銕鈿鉋鉐銜銖銓銛鉚鋏銹銷鋩錏鋺鍄錮"],["e840","錙錢錚錣錺錵錻鍜鍠鍼鍮鍖鎰鎬鎭鎔鎹鏖鏗鏨鏥鏘鏃鏝鏐鏈鏤鐚鐔鐓鐃鐇鐐鐶鐫鐵鐡鐺鑁鑒鑄鑛鑠鑢鑞鑪鈩鑰鑵鑷鑽鑚鑼鑾钁鑿閂閇閊閔閖閘閙"],["e880","閠閨閧閭閼閻閹閾闊濶闃闍闌闕闔闖關闡闥闢阡阨阮阯陂陌陏陋陷陜陞陝陟陦陲陬隍隘隕隗險隧隱隲隰隴隶隸隹雎雋雉雍襍雜霍雕雹霄霆霈霓霎霑霏霖霙霤霪霰霹霽霾靄靆靈靂靉靜靠靤靦靨勒靫靱靹鞅靼鞁靺鞆鞋鞏鞐鞜鞨鞦鞣鞳鞴韃韆韈韋韜韭齏韲竟韶韵頏頌頸頤頡頷頽顆顏顋顫顯顰"],["e940","顱顴顳颪颯颱颶飄飃飆飩飫餃餉餒餔餘餡餝餞餤餠餬餮餽餾饂饉饅饐饋饑饒饌饕馗馘馥馭馮馼駟駛駝駘駑駭駮駱駲駻駸騁騏騅駢騙騫騷驅驂驀驃"],["e980","騾驕驍驛驗驟驢驥驤驩驫驪骭骰骼髀髏髑髓體髞髟髢髣髦髯髫髮髴髱髷髻鬆鬘鬚鬟鬢鬣鬥鬧鬨鬩鬪鬮鬯鬲魄魃魏魍魎魑魘魴鮓鮃鮑鮖鮗鮟鮠鮨鮴鯀鯊鮹鯆鯏鯑鯒鯣鯢鯤鯔鯡鰺鯲鯱鯰鰕鰔鰉鰓鰌鰆鰈鰒鰊鰄鰮鰛鰥鰤鰡鰰鱇鰲鱆鰾鱚鱠鱧鱶鱸鳧鳬鳰鴉鴈鳫鴃鴆鴪鴦鶯鴣鴟鵄鴕鴒鵁鴿鴾鵆鵈"],["ea40","鵝鵞鵤鵑鵐鵙鵲鶉鶇鶫鵯鵺鶚鶤鶩鶲鷄鷁鶻鶸鶺鷆鷏鷂鷙鷓鷸鷦鷭鷯鷽鸚鸛鸞鹵鹹鹽麁麈麋麌麒麕麑麝麥麩麸麪麭靡黌黎黏黐黔黜點黝黠黥黨黯"],["ea80","黴黶黷黹黻黼黽鼇鼈皷鼕鼡鼬鼾齊齒齔齣齟齠齡齦齧齬齪齷齲齶龕龜龠堯槇遙瑤凜熙"],["ed40","纊褜鍈銈蓜俉炻昱棈鋹曻彅丨仡仼伀伃伹佖侒侊侚侔俍偀倢俿倞偆偰偂傔僴僘兊兤冝冾凬刕劜劦勀勛匀匇匤卲厓厲叝﨎咜咊咩哿喆坙坥垬埈埇﨏"],["ed80","塚增墲夋奓奛奝奣妤妺孖寀甯寘寬尞岦岺峵崧嵓﨑嵂嵭嶸嶹巐弡弴彧德忞恝悅悊惞惕愠惲愑愷愰憘戓抦揵摠撝擎敎昀昕昻昉昮昞昤晥晗晙晴晳暙暠暲暿曺朎朗杦枻桒柀栁桄棏﨓楨﨔榘槢樰橫橆橳橾櫢櫤毖氿汜沆汯泚洄涇浯涖涬淏淸淲淼渹湜渧渼溿澈澵濵瀅瀇瀨炅炫焏焄煜煆煇凞燁燾犱"],["ee40","犾猤猪獷玽珉珖珣珒琇珵琦琪琩琮瑢璉璟甁畯皂皜皞皛皦益睆劯砡硎硤硺礰礼神祥禔福禛竑竧靖竫箞精絈絜綷綠緖繒罇羡羽茁荢荿菇菶葈蒴蕓蕙"],["ee80","蕫﨟薰蘒﨡蠇裵訒訷詹誧誾諟諸諶譓譿賰賴贒赶﨣軏﨤逸遧郞都鄕鄧釚釗釞釭釮釤釥鈆鈐鈊鈺鉀鈼鉎鉙鉑鈹鉧銧鉷鉸鋧鋗鋙鋐﨧鋕鋠鋓錥錡鋻﨨錞鋿錝錂鍰鍗鎤鏆鏞鏸鐱鑅鑈閒隆﨩隝隯霳霻靃靍靏靑靕顗顥飯飼餧館馞驎髙髜魵魲鮏鮱鮻鰀鵰鵫鶴鸙黑"],["eeef","ⅰ",9,"￢￤＇＂"],["f040","",62],["f080","",124],["f140","",62],["f180","",124],["f240","",62],["f280","",124],["f340","",62],["f380","",124],["f440","",62],["f480","",124],["f540","",62],["f580","",124],["f640","",62],["f680","",124],["f740","",62],["f780","",124],["f840","",62],["f880","",124],["f940",""],["fa40","ⅰ",9,"Ⅰ",9,"￢￤＇＂㈱№℡∵纊褜鍈銈蓜俉炻昱棈鋹曻彅丨仡仼伀伃伹佖侒侊侚侔俍偀倢俿倞偆偰偂傔僴僘兊"],["fa80","兤冝冾凬刕劜劦勀勛匀匇匤卲厓厲叝﨎咜咊咩哿喆坙坥垬埈埇﨏塚增墲夋奓奛奝奣妤妺孖寀甯寘寬尞岦岺峵崧嵓﨑嵂嵭嶸嶹巐弡弴彧德忞恝悅悊惞惕愠惲愑愷愰憘戓抦揵摠撝擎敎昀昕昻昉昮昞昤晥晗晙晴晳暙暠暲暿曺朎朗杦枻桒柀栁桄棏﨓楨﨔榘槢樰橫橆橳橾櫢櫤毖氿汜沆汯泚洄涇浯"],["fb40","涖涬淏淸淲淼渹湜渧渼溿澈澵濵瀅瀇瀨炅炫焏焄煜煆煇凞燁燾犱犾猤猪獷玽珉珖珣珒琇珵琦琪琩琮瑢璉璟甁畯皂皜皞皛皦益睆劯砡硎硤硺礰礼神"],["fb80","祥禔福禛竑竧靖竫箞精絈絜綷綠緖繒罇羡羽茁荢荿菇菶葈蒴蕓蕙蕫﨟薰蘒﨡蠇裵訒訷詹誧誾諟諸諶譓譿賰賴贒赶﨣軏﨤逸遧郞都鄕鄧釚釗釞釭釮釤釥鈆鈐鈊鈺鉀鈼鉎鉙鉑鈹鉧銧鉷鉸鋧鋗鋙鋐﨧鋕鋠鋓錥錡鋻﨨錞鋿錝錂鍰鍗鎤鏆鏞鏸鐱鑅鑈閒隆﨩隝隯霳霻靃靍靏靑靕顗顥飯飼餧館馞驎髙"],["fc40","髜魵魲鮏鮱鮻鰀鵰鵫鶴鸙黑"]],Ti=[["0","\0",127],["8ea1","｡",62],["a1a1","　、。，．・：；？！゛゜´｀¨＾￣＿ヽヾゝゞ〃仝々〆〇ー―‐／＼～∥｜…‥‘’“”（）〔〕［］｛｝〈",9,"＋－±×÷＝≠＜＞≦≧∞∴♂♀°′″℃￥＄￠￡％＃＆＊＠§☆★○●◎◇"],["a2a1","◆□■△▲▽▼※〒→←↑↓〓"],["a2ba","∈∋⊆⊇⊂⊃∪∩"],["a2ca","∧∨￢⇒⇔∀∃"],["a2dc","∠⊥⌒∂∇≡≒≪≫√∽∝∵∫∬"],["a2f2","Å‰♯♭♪†‡¶"],["a2fe","◯"],["a3b0","０",9],["a3c1","Ａ",25],["a3e1","ａ",25],["a4a1","ぁ",82],["a5a1","ァ",85],["a6a1","Α",16,"Σ",6],["a6c1","α",16,"σ",6],["a7a1","А",5,"ЁЖ",25],["a7d1","а",5,"ёж",25],["a8a1","─│┌┐┘└├┬┤┴┼━┃┏┓┛┗┣┳┫┻╋┠┯┨┷┿┝┰┥┸╂"],["ada1","①",19,"Ⅰ",9],["adc0","㍉㌔㌢㍍㌘㌧㌃㌶㍑㍗㌍㌦㌣㌫㍊㌻㎜㎝㎞㎎㎏㏄㎡"],["addf","㍻〝〟№㏍℡㊤",4,"㈱㈲㈹㍾㍽㍼≒≡∫∮∑√⊥∠∟⊿∵∩∪"],["b0a1","亜唖娃阿哀愛挨姶逢葵茜穐悪握渥旭葦芦鯵梓圧斡扱宛姐虻飴絢綾鮎或粟袷安庵按暗案闇鞍杏以伊位依偉囲夷委威尉惟意慰易椅為畏異移維緯胃萎衣謂違遺医井亥域育郁磯一壱溢逸稲茨芋鰯允印咽員因姻引飲淫胤蔭"],["b1a1","院陰隠韻吋右宇烏羽迂雨卯鵜窺丑碓臼渦嘘唄欝蔚鰻姥厩浦瓜閏噂云運雲荏餌叡営嬰影映曳栄永泳洩瑛盈穎頴英衛詠鋭液疫益駅悦謁越閲榎厭円園堰奄宴延怨掩援沿演炎焔煙燕猿縁艶苑薗遠鉛鴛塩於汚甥凹央奥往応"],["b2a1","押旺横欧殴王翁襖鴬鴎黄岡沖荻億屋憶臆桶牡乙俺卸恩温穏音下化仮何伽価佳加可嘉夏嫁家寡科暇果架歌河火珂禍禾稼箇花苛茄荷華菓蝦課嘩貨迦過霞蚊俄峨我牙画臥芽蛾賀雅餓駕介会解回塊壊廻快怪悔恢懐戒拐改"],["b3a1","魁晦械海灰界皆絵芥蟹開階貝凱劾外咳害崖慨概涯碍蓋街該鎧骸浬馨蛙垣柿蛎鈎劃嚇各廓拡撹格核殻獲確穫覚角赫較郭閣隔革学岳楽額顎掛笠樫橿梶鰍潟割喝恰括活渇滑葛褐轄且鰹叶椛樺鞄株兜竃蒲釜鎌噛鴨栢茅萱"],["b4a1","粥刈苅瓦乾侃冠寒刊勘勧巻喚堪姦完官寛干幹患感慣憾換敢柑桓棺款歓汗漢澗潅環甘監看竿管簡緩缶翰肝艦莞観諌貫還鑑間閑関陥韓館舘丸含岸巌玩癌眼岩翫贋雁頑顔願企伎危喜器基奇嬉寄岐希幾忌揮机旗既期棋棄"],["b5a1","機帰毅気汽畿祈季稀紀徽規記貴起軌輝飢騎鬼亀偽儀妓宜戯技擬欺犠疑祇義蟻誼議掬菊鞠吉吃喫桔橘詰砧杵黍却客脚虐逆丘久仇休及吸宮弓急救朽求汲泣灸球究窮笈級糾給旧牛去居巨拒拠挙渠虚許距鋸漁禦魚亨享京"],["b6a1","供侠僑兇競共凶協匡卿叫喬境峡強彊怯恐恭挟教橋況狂狭矯胸脅興蕎郷鏡響饗驚仰凝尭暁業局曲極玉桐粁僅勤均巾錦斤欣欽琴禁禽筋緊芹菌衿襟謹近金吟銀九倶句区狗玖矩苦躯駆駈駒具愚虞喰空偶寓遇隅串櫛釧屑屈"],["b7a1","掘窟沓靴轡窪熊隈粂栗繰桑鍬勲君薫訓群軍郡卦袈祁係傾刑兄啓圭珪型契形径恵慶慧憩掲携敬景桂渓畦稽系経継繋罫茎荊蛍計詣警軽頚鶏芸迎鯨劇戟撃激隙桁傑欠決潔穴結血訣月件倹倦健兼券剣喧圏堅嫌建憲懸拳捲"],["b8a1","検権牽犬献研硯絹県肩見謙賢軒遣鍵険顕験鹸元原厳幻弦減源玄現絃舷言諺限乎個古呼固姑孤己庫弧戸故枯湖狐糊袴股胡菰虎誇跨鈷雇顧鼓五互伍午呉吾娯後御悟梧檎瑚碁語誤護醐乞鯉交佼侯候倖光公功効勾厚口向"],["b9a1","后喉坑垢好孔孝宏工巧巷幸広庚康弘恒慌抗拘控攻昂晃更杭校梗構江洪浩港溝甲皇硬稿糠紅紘絞綱耕考肯肱腔膏航荒行衡講貢購郊酵鉱砿鋼閤降項香高鴻剛劫号合壕拷濠豪轟麹克刻告国穀酷鵠黒獄漉腰甑忽惚骨狛込"],["baa1","此頃今困坤墾婚恨懇昏昆根梱混痕紺艮魂些佐叉唆嵯左差査沙瑳砂詐鎖裟坐座挫債催再最哉塞妻宰彩才採栽歳済災采犀砕砦祭斎細菜裁載際剤在材罪財冴坂阪堺榊肴咲崎埼碕鷺作削咋搾昨朔柵窄策索錯桜鮭笹匙冊刷"],["bba1","察拶撮擦札殺薩雑皐鯖捌錆鮫皿晒三傘参山惨撒散桟燦珊産算纂蚕讃賛酸餐斬暫残仕仔伺使刺司史嗣四士始姉姿子屍市師志思指支孜斯施旨枝止死氏獅祉私糸紙紫肢脂至視詞詩試誌諮資賜雌飼歯事似侍児字寺慈持時"],["bca1","次滋治爾璽痔磁示而耳自蒔辞汐鹿式識鴫竺軸宍雫七叱執失嫉室悉湿漆疾質実蔀篠偲柴芝屡蕊縞舎写射捨赦斜煮社紗者謝車遮蛇邪借勺尺杓灼爵酌釈錫若寂弱惹主取守手朱殊狩珠種腫趣酒首儒受呪寿授樹綬需囚収周"],["bda1","宗就州修愁拾洲秀秋終繍習臭舟蒐衆襲讐蹴輯週酋酬集醜什住充十従戎柔汁渋獣縦重銃叔夙宿淑祝縮粛塾熟出術述俊峻春瞬竣舜駿准循旬楯殉淳準潤盾純巡遵醇順処初所暑曙渚庶緒署書薯藷諸助叙女序徐恕鋤除傷償"],["bea1","勝匠升召哨商唱嘗奨妾娼宵将小少尚庄床廠彰承抄招掌捷昇昌昭晶松梢樟樵沼消渉湘焼焦照症省硝礁祥称章笑粧紹肖菖蒋蕉衝裳訟証詔詳象賞醤鉦鍾鐘障鞘上丈丞乗冗剰城場壌嬢常情擾条杖浄状畳穣蒸譲醸錠嘱埴飾"],["bfa1","拭植殖燭織職色触食蝕辱尻伸信侵唇娠寝審心慎振新晋森榛浸深申疹真神秦紳臣芯薪親診身辛進針震人仁刃塵壬尋甚尽腎訊迅陣靭笥諏須酢図厨逗吹垂帥推水炊睡粋翠衰遂酔錐錘随瑞髄崇嵩数枢趨雛据杉椙菅頗雀裾"],["c0a1","澄摺寸世瀬畝是凄制勢姓征性成政整星晴棲栖正清牲生盛精聖声製西誠誓請逝醒青静斉税脆隻席惜戚斥昔析石積籍績脊責赤跡蹟碩切拙接摂折設窃節説雪絶舌蝉仙先千占宣専尖川戦扇撰栓栴泉浅洗染潜煎煽旋穿箭線"],["c1a1","繊羨腺舛船薦詮賎践選遷銭銑閃鮮前善漸然全禅繕膳糎噌塑岨措曾曽楚狙疏疎礎祖租粗素組蘇訴阻遡鼠僧創双叢倉喪壮奏爽宋層匝惣想捜掃挿掻操早曹巣槍槽漕燥争痩相窓糟総綜聡草荘葬蒼藻装走送遭鎗霜騒像増憎"],["c2a1","臓蔵贈造促側則即息捉束測足速俗属賊族続卒袖其揃存孫尊損村遜他多太汰詑唾堕妥惰打柁舵楕陀駄騨体堆対耐岱帯待怠態戴替泰滞胎腿苔袋貸退逮隊黛鯛代台大第醍題鷹滝瀧卓啄宅托択拓沢濯琢託鐸濁諾茸凧蛸只"],["c3a1","叩但達辰奪脱巽竪辿棚谷狸鱈樽誰丹単嘆坦担探旦歎淡湛炭短端箪綻耽胆蛋誕鍛団壇弾断暖檀段男談値知地弛恥智池痴稚置致蜘遅馳築畜竹筑蓄逐秩窒茶嫡着中仲宙忠抽昼柱注虫衷註酎鋳駐樗瀦猪苧著貯丁兆凋喋寵"],["c4a1","帖帳庁弔張彫徴懲挑暢朝潮牒町眺聴脹腸蝶調諜超跳銚長頂鳥勅捗直朕沈珍賃鎮陳津墜椎槌追鎚痛通塚栂掴槻佃漬柘辻蔦綴鍔椿潰坪壷嬬紬爪吊釣鶴亭低停偵剃貞呈堤定帝底庭廷弟悌抵挺提梯汀碇禎程締艇訂諦蹄逓"],["c5a1","邸鄭釘鼎泥摘擢敵滴的笛適鏑溺哲徹撤轍迭鉄典填天展店添纏甜貼転顛点伝殿澱田電兎吐堵塗妬屠徒斗杜渡登菟賭途都鍍砥砺努度土奴怒倒党冬凍刀唐塔塘套宕島嶋悼投搭東桃梼棟盗淘湯涛灯燈当痘祷等答筒糖統到"],["c6a1","董蕩藤討謄豆踏逃透鐙陶頭騰闘働動同堂導憧撞洞瞳童胴萄道銅峠鴇匿得徳涜特督禿篤毒独読栃橡凸突椴届鳶苫寅酉瀞噸屯惇敦沌豚遁頓呑曇鈍奈那内乍凪薙謎灘捺鍋楢馴縄畷南楠軟難汝二尼弐迩匂賑肉虹廿日乳入"],["c7a1","如尿韮任妊忍認濡禰祢寧葱猫熱年念捻撚燃粘乃廼之埜嚢悩濃納能脳膿農覗蚤巴把播覇杷波派琶破婆罵芭馬俳廃拝排敗杯盃牌背肺輩配倍培媒梅楳煤狽買売賠陪這蝿秤矧萩伯剥博拍柏泊白箔粕舶薄迫曝漠爆縛莫駁麦"],["c8a1","函箱硲箸肇筈櫨幡肌畑畠八鉢溌発醗髪伐罰抜筏閥鳩噺塙蛤隼伴判半反叛帆搬斑板氾汎版犯班畔繁般藩販範釆煩頒飯挽晩番盤磐蕃蛮匪卑否妃庇彼悲扉批披斐比泌疲皮碑秘緋罷肥被誹費避非飛樋簸備尾微枇毘琵眉美"],["c9a1","鼻柊稗匹疋髭彦膝菱肘弼必畢筆逼桧姫媛紐百謬俵彪標氷漂瓢票表評豹廟描病秒苗錨鋲蒜蛭鰭品彬斌浜瀕貧賓頻敏瓶不付埠夫婦富冨布府怖扶敷斧普浮父符腐膚芙譜負賦赴阜附侮撫武舞葡蕪部封楓風葺蕗伏副復幅服"],["caa1","福腹複覆淵弗払沸仏物鮒分吻噴墳憤扮焚奮粉糞紛雰文聞丙併兵塀幣平弊柄並蔽閉陛米頁僻壁癖碧別瞥蔑箆偏変片篇編辺返遍便勉娩弁鞭保舗鋪圃捕歩甫補輔穂募墓慕戊暮母簿菩倣俸包呆報奉宝峰峯崩庖抱捧放方朋"],["cba1","法泡烹砲縫胞芳萌蓬蜂褒訪豊邦鋒飽鳳鵬乏亡傍剖坊妨帽忘忙房暴望某棒冒紡肪膨謀貌貿鉾防吠頬北僕卜墨撲朴牧睦穆釦勃没殆堀幌奔本翻凡盆摩磨魔麻埋妹昧枚毎哩槙幕膜枕鮪柾鱒桝亦俣又抹末沫迄侭繭麿万慢満"],["cca1","漫蔓味未魅巳箕岬密蜜湊蓑稔脈妙粍民眠務夢無牟矛霧鵡椋婿娘冥名命明盟迷銘鳴姪牝滅免棉綿緬面麺摸模茂妄孟毛猛盲網耗蒙儲木黙目杢勿餅尤戻籾貰問悶紋門匁也冶夜爺耶野弥矢厄役約薬訳躍靖柳薮鑓愉愈油癒"],["cda1","諭輸唯佑優勇友宥幽悠憂揖有柚湧涌猶猷由祐裕誘遊邑郵雄融夕予余与誉輿預傭幼妖容庸揚揺擁曜楊様洋溶熔用窯羊耀葉蓉要謡踊遥陽養慾抑欲沃浴翌翼淀羅螺裸来莱頼雷洛絡落酪乱卵嵐欄濫藍蘭覧利吏履李梨理璃"],["cea1","痢裏裡里離陸律率立葎掠略劉流溜琉留硫粒隆竜龍侶慮旅虜了亮僚両凌寮料梁涼猟療瞭稜糧良諒遼量陵領力緑倫厘林淋燐琳臨輪隣鱗麟瑠塁涙累類令伶例冷励嶺怜玲礼苓鈴隷零霊麗齢暦歴列劣烈裂廉恋憐漣煉簾練聯"],["cfa1","蓮連錬呂魯櫓炉賂路露労婁廊弄朗楼榔浪漏牢狼篭老聾蝋郎六麓禄肋録論倭和話歪賄脇惑枠鷲亙亘鰐詫藁蕨椀湾碗腕"],["d0a1","弌丐丕个丱丶丼丿乂乖乘亂亅豫亊舒弍于亞亟亠亢亰亳亶从仍仄仆仂仗仞仭仟价伉佚估佛佝佗佇佶侈侏侘佻佩佰侑佯來侖儘俔俟俎俘俛俑俚俐俤俥倚倨倔倪倥倅伜俶倡倩倬俾俯們倆偃假會偕偐偈做偖偬偸傀傚傅傴傲"],["d1a1","僉僊傳僂僖僞僥僭僣僮價僵儉儁儂儖儕儔儚儡儺儷儼儻儿兀兒兌兔兢竸兩兪兮冀冂囘册冉冏冑冓冕冖冤冦冢冩冪冫决冱冲冰况冽凅凉凛几處凩凭凰凵凾刄刋刔刎刧刪刮刳刹剏剄剋剌剞剔剪剴剩剳剿剽劍劔劒剱劈劑辨"],["d2a1","辧劬劭劼劵勁勍勗勞勣勦飭勠勳勵勸勹匆匈甸匍匐匏匕匚匣匯匱匳匸區卆卅丗卉卍凖卞卩卮夘卻卷厂厖厠厦厥厮厰厶參簒雙叟曼燮叮叨叭叺吁吽呀听吭吼吮吶吩吝呎咏呵咎呟呱呷呰咒呻咀呶咄咐咆哇咢咸咥咬哄哈咨"],["d3a1","咫哂咤咾咼哘哥哦唏唔哽哮哭哺哢唹啀啣啌售啜啅啖啗唸唳啝喙喀咯喊喟啻啾喘喞單啼喃喩喇喨嗚嗅嗟嗄嗜嗤嗔嘔嗷嘖嗾嗽嘛嗹噎噐營嘴嘶嘲嘸噫噤嘯噬噪嚆嚀嚊嚠嚔嚏嚥嚮嚶嚴囂嚼囁囃囀囈囎囑囓囗囮囹圀囿圄圉"],["d4a1","圈國圍圓團圖嗇圜圦圷圸坎圻址坏坩埀垈坡坿垉垓垠垳垤垪垰埃埆埔埒埓堊埖埣堋堙堝塲堡塢塋塰毀塒堽塹墅墹墟墫墺壞墻墸墮壅壓壑壗壙壘壥壜壤壟壯壺壹壻壼壽夂夊夐夛梦夥夬夭夲夸夾竒奕奐奎奚奘奢奠奧奬奩"],["d5a1","奸妁妝佞侫妣妲姆姨姜妍姙姚娥娟娑娜娉娚婀婬婉娵娶婢婪媚媼媾嫋嫂媽嫣嫗嫦嫩嫖嫺嫻嬌嬋嬖嬲嫐嬪嬶嬾孃孅孀孑孕孚孛孥孩孰孳孵學斈孺宀它宦宸寃寇寉寔寐寤實寢寞寥寫寰寶寳尅將專對尓尠尢尨尸尹屁屆屎屓"],["d6a1","屐屏孱屬屮乢屶屹岌岑岔妛岫岻岶岼岷峅岾峇峙峩峽峺峭嶌峪崋崕崗嵜崟崛崑崔崢崚崙崘嵌嵒嵎嵋嵬嵳嵶嶇嶄嶂嶢嶝嶬嶮嶽嶐嶷嶼巉巍巓巒巖巛巫已巵帋帚帙帑帛帶帷幄幃幀幎幗幔幟幢幤幇幵并幺麼广庠廁廂廈廐廏"],["d7a1","廖廣廝廚廛廢廡廨廩廬廱廳廰廴廸廾弃弉彝彜弋弑弖弩弭弸彁彈彌彎弯彑彖彗彙彡彭彳彷徃徂彿徊很徑徇從徙徘徠徨徭徼忖忻忤忸忱忝悳忿怡恠怙怐怩怎怱怛怕怫怦怏怺恚恁恪恷恟恊恆恍恣恃恤恂恬恫恙悁悍惧悃悚"],["d8a1","悄悛悖悗悒悧悋惡悸惠惓悴忰悽惆悵惘慍愕愆惶惷愀惴惺愃愡惻惱愍愎慇愾愨愧慊愿愼愬愴愽慂慄慳慷慘慙慚慫慴慯慥慱慟慝慓慵憙憖憇憬憔憚憊憑憫憮懌懊應懷懈懃懆憺懋罹懍懦懣懶懺懴懿懽懼懾戀戈戉戍戌戔戛"],["d9a1","戞戡截戮戰戲戳扁扎扞扣扛扠扨扼抂抉找抒抓抖拔抃抔拗拑抻拏拿拆擔拈拜拌拊拂拇抛拉挌拮拱挧挂挈拯拵捐挾捍搜捏掖掎掀掫捶掣掏掉掟掵捫捩掾揩揀揆揣揉插揶揄搖搴搆搓搦搶攝搗搨搏摧摯摶摎攪撕撓撥撩撈撼"],["daa1","據擒擅擇撻擘擂擱擧舉擠擡抬擣擯攬擶擴擲擺攀擽攘攜攅攤攣攫攴攵攷收攸畋效敖敕敍敘敞敝敲數斂斃變斛斟斫斷旃旆旁旄旌旒旛旙无旡旱杲昊昃旻杳昵昶昴昜晏晄晉晁晞晝晤晧晨晟晢晰暃暈暎暉暄暘暝曁暹曉暾暼"],["dba1","曄暸曖曚曠昿曦曩曰曵曷朏朖朞朦朧霸朮朿朶杁朸朷杆杞杠杙杣杤枉杰枩杼杪枌枋枦枡枅枷柯枴柬枳柩枸柤柞柝柢柮枹柎柆柧檜栞框栩桀桍栲桎梳栫桙档桷桿梟梏梭梔條梛梃檮梹桴梵梠梺椏梍桾椁棊椈棘椢椦棡椌棍"],["dca1","棔棧棕椶椒椄棗棣椥棹棠棯椨椪椚椣椡棆楹楷楜楸楫楔楾楮椹楴椽楙椰楡楞楝榁楪榲榮槐榿槁槓榾槎寨槊槝榻槃榧樮榑榠榜榕榴槞槨樂樛槿權槹槲槧樅榱樞槭樔槫樊樒櫁樣樓橄樌橲樶橸橇橢橙橦橈樸樢檐檍檠檄檢檣"],["dda1","檗蘗檻櫃櫂檸檳檬櫞櫑櫟檪櫚櫪櫻欅蘖櫺欒欖鬱欟欸欷盜欹飮歇歃歉歐歙歔歛歟歡歸歹歿殀殄殃殍殘殕殞殤殪殫殯殲殱殳殷殼毆毋毓毟毬毫毳毯麾氈氓气氛氤氣汞汕汢汪沂沍沚沁沛汾汨汳沒沐泄泱泓沽泗泅泝沮沱沾"],["dea1","沺泛泯泙泪洟衍洶洫洽洸洙洵洳洒洌浣涓浤浚浹浙涎涕濤涅淹渕渊涵淇淦涸淆淬淞淌淨淒淅淺淙淤淕淪淮渭湮渮渙湲湟渾渣湫渫湶湍渟湃渺湎渤滿渝游溂溪溘滉溷滓溽溯滄溲滔滕溏溥滂溟潁漑灌滬滸滾漿滲漱滯漲滌"],["dfa1","漾漓滷澆潺潸澁澀潯潛濳潭澂潼潘澎澑濂潦澳澣澡澤澹濆澪濟濕濬濔濘濱濮濛瀉瀋濺瀑瀁瀏濾瀛瀚潴瀝瀘瀟瀰瀾瀲灑灣炙炒炯烱炬炸炳炮烟烋烝烙焉烽焜焙煥煕熈煦煢煌煖煬熏燻熄熕熨熬燗熹熾燒燉燔燎燠燬燧燵燼"],["e0a1","燹燿爍爐爛爨爭爬爰爲爻爼爿牀牆牋牘牴牾犂犁犇犒犖犢犧犹犲狃狆狄狎狒狢狠狡狹狷倏猗猊猜猖猝猴猯猩猥猾獎獏默獗獪獨獰獸獵獻獺珈玳珎玻珀珥珮珞璢琅瑯琥珸琲琺瑕琿瑟瑙瑁瑜瑩瑰瑣瑪瑶瑾璋璞璧瓊瓏瓔珱"],["e1a1","瓠瓣瓧瓩瓮瓲瓰瓱瓸瓷甄甃甅甌甎甍甕甓甞甦甬甼畄畍畊畉畛畆畚畩畤畧畫畭畸當疆疇畴疊疉疂疔疚疝疥疣痂疳痃疵疽疸疼疱痍痊痒痙痣痞痾痿痼瘁痰痺痲痳瘋瘍瘉瘟瘧瘠瘡瘢瘤瘴瘰瘻癇癈癆癜癘癡癢癨癩癪癧癬癰"],["e2a1","癲癶癸發皀皃皈皋皎皖皓皙皚皰皴皸皹皺盂盍盖盒盞盡盥盧盪蘯盻眈眇眄眩眤眞眥眦眛眷眸睇睚睨睫睛睥睿睾睹瞎瞋瞑瞠瞞瞰瞶瞹瞿瞼瞽瞻矇矍矗矚矜矣矮矼砌砒礦砠礪硅碎硴碆硼碚碌碣碵碪碯磑磆磋磔碾碼磅磊磬"],["e3a1","磧磚磽磴礇礒礑礙礬礫祀祠祗祟祚祕祓祺祿禊禝禧齋禪禮禳禹禺秉秕秧秬秡秣稈稍稘稙稠稟禀稱稻稾稷穃穗穉穡穢穩龝穰穹穽窈窗窕窘窖窩竈窰窶竅竄窿邃竇竊竍竏竕竓站竚竝竡竢竦竭竰笂笏笊笆笳笘笙笞笵笨笶筐"],["e4a1","筺笄筍笋筌筅筵筥筴筧筰筱筬筮箝箘箟箍箜箚箋箒箏筝箙篋篁篌篏箴篆篝篩簑簔篦篥籠簀簇簓篳篷簗簍篶簣簧簪簟簷簫簽籌籃籔籏籀籐籘籟籤籖籥籬籵粃粐粤粭粢粫粡粨粳粲粱粮粹粽糀糅糂糘糒糜糢鬻糯糲糴糶糺紆"],["e5a1","紂紜紕紊絅絋紮紲紿紵絆絳絖絎絲絨絮絏絣經綉絛綏絽綛綺綮綣綵緇綽綫總綢綯緜綸綟綰緘緝緤緞緻緲緡縅縊縣縡縒縱縟縉縋縢繆繦縻縵縹繃縷縲縺繧繝繖繞繙繚繹繪繩繼繻纃緕繽辮繿纈纉續纒纐纓纔纖纎纛纜缸缺"],["e6a1","罅罌罍罎罐网罕罔罘罟罠罨罩罧罸羂羆羃羈羇羌羔羞羝羚羣羯羲羹羮羶羸譱翅翆翊翕翔翡翦翩翳翹飜耆耄耋耒耘耙耜耡耨耿耻聊聆聒聘聚聟聢聨聳聲聰聶聹聽聿肄肆肅肛肓肚肭冐肬胛胥胙胝胄胚胖脉胯胱脛脩脣脯腋"],["e7a1","隋腆脾腓腑胼腱腮腥腦腴膃膈膊膀膂膠膕膤膣腟膓膩膰膵膾膸膽臀臂膺臉臍臑臙臘臈臚臟臠臧臺臻臾舁舂舅與舊舍舐舖舩舫舸舳艀艙艘艝艚艟艤艢艨艪艫舮艱艷艸艾芍芒芫芟芻芬苡苣苟苒苴苳苺莓范苻苹苞茆苜茉苙"],["e8a1","茵茴茖茲茱荀茹荐荅茯茫茗茘莅莚莪莟莢莖茣莎莇莊荼莵荳荵莠莉莨菴萓菫菎菽萃菘萋菁菷萇菠菲萍萢萠莽萸蔆菻葭萪萼蕚蒄葷葫蒭葮蒂葩葆萬葯葹萵蓊葢蒹蒿蒟蓙蓍蒻蓚蓐蓁蓆蓖蒡蔡蓿蓴蔗蔘蔬蔟蔕蔔蓼蕀蕣蕘蕈"],["e9a1","蕁蘂蕋蕕薀薤薈薑薊薨蕭薔薛藪薇薜蕷蕾薐藉薺藏薹藐藕藝藥藜藹蘊蘓蘋藾藺蘆蘢蘚蘰蘿虍乕虔號虧虱蚓蚣蚩蚪蚋蚌蚶蚯蛄蛆蚰蛉蠣蚫蛔蛞蛩蛬蛟蛛蛯蜒蜆蜈蜀蜃蛻蜑蜉蜍蛹蜊蜴蜿蜷蜻蜥蜩蜚蝠蝟蝸蝌蝎蝴蝗蝨蝮蝙"],["eaa1","蝓蝣蝪蠅螢螟螂螯蟋螽蟀蟐雖螫蟄螳蟇蟆螻蟯蟲蟠蠏蠍蟾蟶蟷蠎蟒蠑蠖蠕蠢蠡蠱蠶蠹蠧蠻衄衂衒衙衞衢衫袁衾袞衵衽袵衲袂袗袒袮袙袢袍袤袰袿袱裃裄裔裘裙裝裹褂裼裴裨裲褄褌褊褓襃褞褥褪褫襁襄褻褶褸襌褝襠襞"],["eba1","襦襤襭襪襯襴襷襾覃覈覊覓覘覡覩覦覬覯覲覺覽覿觀觚觜觝觧觴觸訃訖訐訌訛訝訥訶詁詛詒詆詈詼詭詬詢誅誂誄誨誡誑誥誦誚誣諄諍諂諚諫諳諧諤諱謔諠諢諷諞諛謌謇謚諡謖謐謗謠謳鞫謦謫謾謨譁譌譏譎證譖譛譚譫"],["eca1","譟譬譯譴譽讀讌讎讒讓讖讙讚谺豁谿豈豌豎豐豕豢豬豸豺貂貉貅貊貍貎貔豼貘戝貭貪貽貲貳貮貶賈賁賤賣賚賽賺賻贄贅贊贇贏贍贐齎贓賍贔贖赧赭赱赳趁趙跂趾趺跏跚跖跌跛跋跪跫跟跣跼踈踉跿踝踞踐踟蹂踵踰踴蹊"],["eda1","蹇蹉蹌蹐蹈蹙蹤蹠踪蹣蹕蹶蹲蹼躁躇躅躄躋躊躓躑躔躙躪躡躬躰軆躱躾軅軈軋軛軣軼軻軫軾輊輅輕輒輙輓輜輟輛輌輦輳輻輹轅轂輾轌轉轆轎轗轜轢轣轤辜辟辣辭辯辷迚迥迢迪迯邇迴逅迹迺逑逕逡逍逞逖逋逧逶逵逹迸"],["eea1","遏遐遑遒逎遉逾遖遘遞遨遯遶隨遲邂遽邁邀邊邉邏邨邯邱邵郢郤扈郛鄂鄒鄙鄲鄰酊酖酘酣酥酩酳酲醋醉醂醢醫醯醪醵醴醺釀釁釉釋釐釖釟釡釛釼釵釶鈞釿鈔鈬鈕鈑鉞鉗鉅鉉鉤鉈銕鈿鉋鉐銜銖銓銛鉚鋏銹銷鋩錏鋺鍄錮"],["efa1","錙錢錚錣錺錵錻鍜鍠鍼鍮鍖鎰鎬鎭鎔鎹鏖鏗鏨鏥鏘鏃鏝鏐鏈鏤鐚鐔鐓鐃鐇鐐鐶鐫鐵鐡鐺鑁鑒鑄鑛鑠鑢鑞鑪鈩鑰鑵鑷鑽鑚鑼鑾钁鑿閂閇閊閔閖閘閙閠閨閧閭閼閻閹閾闊濶闃闍闌闕闔闖關闡闥闢阡阨阮阯陂陌陏陋陷陜陞"],["f0a1","陝陟陦陲陬隍隘隕隗險隧隱隲隰隴隶隸隹雎雋雉雍襍雜霍雕雹霄霆霈霓霎霑霏霖霙霤霪霰霹霽霾靄靆靈靂靉靜靠靤靦靨勒靫靱靹鞅靼鞁靺鞆鞋鞏鞐鞜鞨鞦鞣鞳鞴韃韆韈韋韜韭齏韲竟韶韵頏頌頸頤頡頷頽顆顏顋顫顯顰"],["f1a1","顱顴顳颪颯颱颶飄飃飆飩飫餃餉餒餔餘餡餝餞餤餠餬餮餽餾饂饉饅饐饋饑饒饌饕馗馘馥馭馮馼駟駛駝駘駑駭駮駱駲駻駸騁騏騅駢騙騫騷驅驂驀驃騾驕驍驛驗驟驢驥驤驩驫驪骭骰骼髀髏髑髓體髞髟髢髣髦髯髫髮髴髱髷"],["f2a1","髻鬆鬘鬚鬟鬢鬣鬥鬧鬨鬩鬪鬮鬯鬲魄魃魏魍魎魑魘魴鮓鮃鮑鮖鮗鮟鮠鮨鮴鯀鯊鮹鯆鯏鯑鯒鯣鯢鯤鯔鯡鰺鯲鯱鯰鰕鰔鰉鰓鰌鰆鰈鰒鰊鰄鰮鰛鰥鰤鰡鰰鱇鰲鱆鰾鱚鱠鱧鱶鱸鳧鳬鳰鴉鴈鳫鴃鴆鴪鴦鶯鴣鴟鵄鴕鴒鵁鴿鴾鵆鵈"],["f3a1","鵝鵞鵤鵑鵐鵙鵲鶉鶇鶫鵯鵺鶚鶤鶩鶲鷄鷁鶻鶸鶺鷆鷏鷂鷙鷓鷸鷦鷭鷯鷽鸚鸛鸞鹵鹹鹽麁麈麋麌麒麕麑麝麥麩麸麪麭靡黌黎黏黐黔黜點黝黠黥黨黯黴黶黷黹黻黼黽鼇鼈皷鼕鼡鼬鼾齊齒齔齣齟齠齡齦齧齬齪齷齲齶龕龜龠"],["f4a1","堯槇遙瑤凜熙"],["f9a1","纊褜鍈銈蓜俉炻昱棈鋹曻彅丨仡仼伀伃伹佖侒侊侚侔俍偀倢俿倞偆偰偂傔僴僘兊兤冝冾凬刕劜劦勀勛匀匇匤卲厓厲叝﨎咜咊咩哿喆坙坥垬埈埇﨏塚增墲夋奓奛奝奣妤妺孖寀甯寘寬尞岦岺峵崧嵓﨑嵂嵭嶸嶹巐弡弴彧德"],["faa1","忞恝悅悊惞惕愠惲愑愷愰憘戓抦揵摠撝擎敎昀昕昻昉昮昞昤晥晗晙晴晳暙暠暲暿曺朎朗杦枻桒柀栁桄棏﨓楨﨔榘槢樰橫橆橳橾櫢櫤毖氿汜沆汯泚洄涇浯涖涬淏淸淲淼渹湜渧渼溿澈澵濵瀅瀇瀨炅炫焏焄煜煆煇凞燁燾犱"],["fba1","犾猤猪獷玽珉珖珣珒琇珵琦琪琩琮瑢璉璟甁畯皂皜皞皛皦益睆劯砡硎硤硺礰礼神祥禔福禛竑竧靖竫箞精絈絜綷綠緖繒罇羡羽茁荢荿菇菶葈蒴蕓蕙蕫﨟薰蘒﨡蠇裵訒訷詹誧誾諟諸諶譓譿賰賴贒赶﨣軏﨤逸遧郞都鄕鄧釚"],["fca1","釗釞釭釮釤釥鈆鈐鈊鈺鉀鈼鉎鉙鉑鈹鉧銧鉷鉸鋧鋗鋙鋐﨧鋕鋠鋓錥錡鋻﨨錞鋿錝錂鍰鍗鎤鏆鏞鏸鐱鑅鑈閒隆﨩隝隯霳霻靃靍靏靑靕顗顥飯飼餧館馞驎髙髜魵魲鮏鮱鮻鰀鵰鵫鶴鸙黑"],["fcf1","ⅰ",9,"￢￤＇＂"],["8fa2af","˘ˇ¸˙˝¯˛˚～΄΅"],["8fa2c2","¡¦¿"],["8fa2eb","ºª©®™¤№"],["8fa6e1","ΆΈΉΊΪ"],["8fa6e7","Ό"],["8fa6e9","ΎΫ"],["8fa6ec","Ώ"],["8fa6f1","άέήίϊΐόςύϋΰώ"],["8fa7c2","Ђ",10,"ЎЏ"],["8fa7f2","ђ",10,"ўџ"],["8fa9a1","ÆĐ"],["8fa9a4","Ħ"],["8fa9a6","Ĳ"],["8fa9a8","ŁĿ"],["8fa9ab","ŊØŒ"],["8fa9af","ŦÞ"],["8fa9c1","æđðħıĳĸłŀŉŋøœßŧþ"],["8faaa1","ÁÀÄÂĂǍĀĄÅÃĆĈČÇĊĎÉÈËÊĚĖĒĘ"],["8faaba","ĜĞĢĠĤÍÌÏÎǏİĪĮĨĴĶĹĽĻŃŇŅÑÓÒÖÔǑŐŌÕŔŘŖŚŜŠŞŤŢÚÙÜÛŬǓŰŪŲŮŨǗǛǙǕŴÝŸŶŹŽŻ"],["8faba1","áàäâăǎāąåãćĉčçċďéèëêěėēęǵĝğ"],["8fabbd","ġĥíìïîǐ"],["8fabc5","īįĩĵķĺľļńňņñóòöôǒőōõŕřŗśŝšşťţúùüûŭǔűūųůũǘǜǚǖŵýÿŷźžż"],["8fb0a1","丂丄丅丌丒丟丣两丨丫丮丯丰丵乀乁乄乇乑乚乜乣乨乩乴乵乹乿亍亖亗亝亯亹仃仐仚仛仠仡仢仨仯仱仳仵份仾仿伀伂伃伈伋伌伒伕伖众伙伮伱你伳伵伷伹伻伾佀佂佈佉佋佌佒佔佖佘佟佣佪佬佮佱佷佸佹佺佽佾侁侂侄"],["8fb1a1","侅侉侊侌侎侐侒侓侔侗侙侚侞侟侲侷侹侻侼侽侾俀俁俅俆俈俉俋俌俍俏俒俜俠俢俰俲俼俽俿倀倁倄倇倊倌倎倐倓倗倘倛倜倝倞倢倧倮倰倲倳倵偀偁偂偅偆偊偌偎偑偒偓偗偙偟偠偢偣偦偧偪偭偰偱倻傁傃傄傆傊傎傏傐"],["8fb2a1","傒傓傔傖傛傜傞",4,"傪傯傰傹傺傽僀僃僄僇僌僎僐僓僔僘僜僝僟僢僤僦僨僩僯僱僶僺僾儃儆儇儈儋儌儍儎僲儐儗儙儛儜儝儞儣儧儨儬儭儯儱儳儴儵儸儹兂兊兏兓兕兗兘兟兤兦兾冃冄冋冎冘冝冡冣冭冸冺冼冾冿凂"],["8fb3a1","凈减凑凒凓凕凘凞凢凥凮凲凳凴凷刁刂刅划刓刕刖刘刢刨刱刲刵刼剅剉剕剗剘剚剜剟剠剡剦剮剷剸剹劀劂劅劊劌劓劕劖劗劘劚劜劤劥劦劧劯劰劶劷劸劺劻劽勀勄勆勈勌勏勑勔勖勛勜勡勥勨勩勪勬勰勱勴勶勷匀匃匊匋"],["8fb4a1","匌匑匓匘匛匜匞匟匥匧匨匩匫匬匭匰匲匵匼匽匾卂卌卋卙卛卡卣卥卬卭卲卹卾厃厇厈厎厓厔厙厝厡厤厪厫厯厲厴厵厷厸厺厽叀叅叏叒叓叕叚叝叞叠另叧叵吂吓吚吡吧吨吪启吱吴吵呃呄呇呍呏呞呢呤呦呧呩呫呭呮呴呿"],["8fb5a1","咁咃咅咈咉咍咑咕咖咜咟咡咦咧咩咪咭咮咱咷咹咺咻咿哆哊响哎哠哪哬哯哶哼哾哿唀唁唅唈唉唌唍唎唕唪唫唲唵唶唻唼唽啁啇啉啊啍啐啑啘啚啛啞啠啡啤啦啿喁喂喆喈喎喏喑喒喓喔喗喣喤喭喲喿嗁嗃嗆嗉嗋嗌嗎嗑嗒"],["8fb6a1","嗓嗗嗘嗛嗞嗢嗩嗶嗿嘅嘈嘊嘍",5,"嘙嘬嘰嘳嘵嘷嘹嘻嘼嘽嘿噀噁噃噄噆噉噋噍噏噔噞噠噡噢噣噦噩噭噯噱噲噵嚄嚅嚈嚋嚌嚕嚙嚚嚝嚞嚟嚦嚧嚨嚩嚫嚬嚭嚱嚳嚷嚾囅囉囊囋囏囐囌囍囙囜囝囟囡囤",4,"囱囫园"],["8fb7a1","囶囷圁圂圇圊圌圑圕圚圛圝圠圢圣圤圥圩圪圬圮圯圳圴圽圾圿坅坆坌坍坒坢坥坧坨坫坭",4,"坳坴坵坷坹坺坻坼坾垁垃垌垔垗垙垚垜垝垞垟垡垕垧垨垩垬垸垽埇埈埌埏埕埝埞埤埦埧埩埭埰埵埶埸埽埾埿堃堄堈堉埡"],["8fb8a1","堌堍堛堞堟堠堦堧堭堲堹堿塉塌塍塏塐塕塟塡塤塧塨塸塼塿墀墁墇墈墉墊墌墍墏墐墔墖墝墠墡墢墦墩墱墲壄墼壂壈壍壎壐壒壔壖壚壝壡壢壩壳夅夆夋夌夒夓夔虁夝夡夣夤夨夯夰夳夵夶夿奃奆奒奓奙奛奝奞奟奡奣奫奭"],["8fb9a1","奯奲奵奶她奻奼妋妌妎妒妕妗妟妤妧妭妮妯妰妳妷妺妼姁姃姄姈姊姍姒姝姞姟姣姤姧姮姯姱姲姴姷娀娄娌娍娎娒娓娞娣娤娧娨娪娭娰婄婅婇婈婌婐婕婞婣婥婧婭婷婺婻婾媋媐媓媖媙媜媞媟媠媢媧媬媱媲媳媵媸媺媻媿"],["8fbaa1","嫄嫆嫈嫏嫚嫜嫠嫥嫪嫮嫵嫶嫽嬀嬁嬈嬗嬴嬙嬛嬝嬡嬥嬭嬸孁孋孌孒孖孞孨孮孯孼孽孾孿宁宄宆宊宎宐宑宓宔宖宨宩宬宭宯宱宲宷宺宼寀寁寍寏寖",4,"寠寯寱寴寽尌尗尞尟尣尦尩尫尬尮尰尲尵尶屙屚屜屢屣屧屨屩"],["8fbba1","屭屰屴屵屺屻屼屽岇岈岊岏岒岝岟岠岢岣岦岪岲岴岵岺峉峋峒峝峗峮峱峲峴崁崆崍崒崫崣崤崦崧崱崴崹崽崿嵂嵃嵆嵈嵕嵑嵙嵊嵟嵠嵡嵢嵤嵪嵭嵰嵹嵺嵾嵿嶁嶃嶈嶊嶒嶓嶔嶕嶙嶛嶟嶠嶧嶫嶰嶴嶸嶹巃巇巋巐巎巘巙巠巤"],["8fbca1","巩巸巹帀帇帍帒帔帕帘帟帠帮帨帲帵帾幋幐幉幑幖幘幛幜幞幨幪",4,"幰庀庋庎庢庤庥庨庪庬庱庳庽庾庿廆廌廋廎廑廒廔廕廜廞廥廫异弆弇弈弎弙弜弝弡弢弣弤弨弫弬弮弰弴弶弻弽弿彀彄彅彇彍彐彔彘彛彠彣彤彧"],["8fbda1","彯彲彴彵彸彺彽彾徉徍徏徖徜徝徢徧徫徤徬徯徰徱徸忄忇忈忉忋忐",4,"忞忡忢忨忩忪忬忭忮忯忲忳忶忺忼怇怊怍怓怔怗怘怚怟怤怭怳怵恀恇恈恉恌恑恔恖恗恝恡恧恱恾恿悂悆悈悊悎悑悓悕悘悝悞悢悤悥您悰悱悷"],["8fbea1","悻悾惂惄惈惉惊惋惎惏惔惕惙惛惝惞惢惥惲惵惸惼惽愂愇愊愌愐",4,"愖愗愙愜愞愢愪愫愰愱愵愶愷愹慁慅慆慉慞慠慬慲慸慻慼慿憀憁憃憄憋憍憒憓憗憘憜憝憟憠憥憨憪憭憸憹憼懀懁懂懎懏懕懜懝懞懟懡懢懧懩懥"],["8fbfa1","懬懭懯戁戃戄戇戓戕戜戠戢戣戧戩戫戹戽扂扃扄扆扌扐扑扒扔扖扚扜扤扭扯扳扺扽抍抎抏抐抦抨抳抶抷抺抾抿拄拎拕拖拚拪拲拴拼拽挃挄挊挋挍挐挓挖挘挩挪挭挵挶挹挼捁捂捃捄捆捊捋捎捒捓捔捘捛捥捦捬捭捱捴捵"],["8fc0a1","捸捼捽捿掂掄掇掊掐掔掕掙掚掞掤掦掭掮掯掽揁揅揈揎揑揓揔揕揜揠揥揪揬揲揳揵揸揹搉搊搐搒搔搘搞搠搢搤搥搩搪搯搰搵搽搿摋摏摑摒摓摔摚摛摜摝摟摠摡摣摭摳摴摻摽撅撇撏撐撑撘撙撛撝撟撡撣撦撨撬撳撽撾撿"],["8fc1a1","擄擉擊擋擌擎擐擑擕擗擤擥擩擪擭擰擵擷擻擿攁攄攈攉攊攏攓攔攖攙攛攞攟攢攦攩攮攱攺攼攽敃敇敉敐敒敔敟敠敧敫敺敽斁斅斊斒斕斘斝斠斣斦斮斲斳斴斿旂旈旉旎旐旔旖旘旟旰旲旴旵旹旾旿昀昄昈昉昍昑昒昕昖昝"],["8fc2a1","昞昡昢昣昤昦昩昪昫昬昮昰昱昳昹昷晀晅晆晊晌晑晎晗晘晙晛晜晠晡曻晪晫晬晾晳晵晿晷晸晹晻暀晼暋暌暍暐暒暙暚暛暜暟暠暤暭暱暲暵暻暿曀曂曃曈曌曎曏曔曛曟曨曫曬曮曺朅朇朎朓朙朜朠朢朳朾杅杇杈杌杔杕杝"],["8fc3a1","杦杬杮杴杶杻极构枎枏枑枓枖枘枙枛枰枱枲枵枻枼枽柹柀柂柃柅柈柉柒柗柙柜柡柦柰柲柶柷桒栔栙栝栟栨栧栬栭栯栰栱栳栻栿桄桅桊桌桕桗桘桛桫桮",4,"桵桹桺桻桼梂梄梆梈梖梘梚梜梡梣梥梩梪梮梲梻棅棈棌棏"],["8fc4a1","棐棑棓棖棙棜棝棥棨棪棫棬棭棰棱棵棶棻棼棽椆椉椊椐椑椓椖椗椱椳椵椸椻楂楅楉楎楗楛楣楤楥楦楨楩楬楰楱楲楺楻楿榀榍榒榖榘榡榥榦榨榫榭榯榷榸榺榼槅槈槑槖槗槢槥槮槯槱槳槵槾樀樁樃樏樑樕樚樝樠樤樨樰樲"],["8fc5a1","樴樷樻樾樿橅橆橉橊橎橐橑橒橕橖橛橤橧橪橱橳橾檁檃檆檇檉檋檑檛檝檞檟檥檫檯檰檱檴檽檾檿櫆櫉櫈櫌櫐櫔櫕櫖櫜櫝櫤櫧櫬櫰櫱櫲櫼櫽欂欃欆欇欉欏欐欑欗欛欞欤欨欫欬欯欵欶欻欿歆歊歍歒歖歘歝歠歧歫歮歰歵歽"],["8fc6a1","歾殂殅殗殛殟殠殢殣殨殩殬殭殮殰殸殹殽殾毃毄毉毌毖毚毡毣毦毧毮毱毷毹毿氂氄氅氉氍氎氐氒氙氟氦氧氨氬氮氳氵氶氺氻氿汊汋汍汏汒汔汙汛汜汫汭汯汴汶汸汹汻沅沆沇沉沔沕沗沘沜沟沰沲沴泂泆泍泏泐泑泒泔泖"],["8fc7a1","泚泜泠泧泩泫泬泮泲泴洄洇洊洎洏洑洓洚洦洧洨汧洮洯洱洹洼洿浗浞浟浡浥浧浯浰浼涂涇涑涒涔涖涗涘涪涬涴涷涹涽涿淄淈淊淎淏淖淛淝淟淠淢淥淩淯淰淴淶淼渀渄渞渢渧渲渶渹渻渼湄湅湈湉湋湏湑湒湓湔湗湜湝湞"],["8fc8a1","湢湣湨湳湻湽溍溓溙溠溧溭溮溱溳溻溿滀滁滃滇滈滊滍滎滏滫滭滮滹滻滽漄漈漊漌漍漖漘漚漛漦漩漪漯漰漳漶漻漼漭潏潑潒潓潗潙潚潝潞潡潢潨潬潽潾澃澇澈澋澌澍澐澒澓澔澖澚澟澠澥澦澧澨澮澯澰澵澶澼濅濇濈濊"],["8fc9a1","濚濞濨濩濰濵濹濼濽瀀瀅瀆瀇瀍瀗瀠瀣瀯瀴瀷瀹瀼灃灄灈灉灊灋灔灕灝灞灎灤灥灬灮灵灶灾炁炅炆炔",4,"炛炤炫炰炱炴炷烊烑烓烔烕烖烘烜烤烺焃",4,"焋焌焏焞焠焫焭焯焰焱焸煁煅煆煇煊煋煐煒煗煚煜煞煠"],["8fcaa1","煨煹熀熅熇熌熒熚熛熠熢熯熰熲熳熺熿燀燁燄燋燌燓燖燙燚燜燸燾爀爇爈爉爓爗爚爝爟爤爫爯爴爸爹牁牂牃牅牎牏牐牓牕牖牚牜牞牠牣牨牫牮牯牱牷牸牻牼牿犄犉犍犎犓犛犨犭犮犱犴犾狁狇狉狌狕狖狘狟狥狳狴狺狻"],["8fcba1","狾猂猄猅猇猋猍猒猓猘猙猞猢猤猧猨猬猱猲猵猺猻猽獃獍獐獒獖獘獝獞獟獠獦獧獩獫獬獮獯獱獷獹獼玀玁玃玅玆玎玐玓玕玗玘玜玞玟玠玢玥玦玪玫玭玵玷玹玼玽玿珅珆珉珋珌珏珒珓珖珙珝珡珣珦珧珩珴珵珷珹珺珻珽"],["8fcca1","珿琀琁琄琇琊琑琚琛琤琦琨",9,"琹瑀瑃瑄瑆瑇瑋瑍瑑瑒瑗瑝瑢瑦瑧瑨瑫瑭瑮瑱瑲璀璁璅璆璇璉璏璐璑璒璘璙璚璜璟璠璡璣璦璨璩璪璫璮璯璱璲璵璹璻璿瓈瓉瓌瓐瓓瓘瓚瓛瓞瓟瓤瓨瓪瓫瓯瓴瓺瓻瓼瓿甆"],["8fcda1","甒甖甗甠甡甤甧甩甪甯甶甹甽甾甿畀畃畇畈畎畐畒畗畞畟畡畯畱畹",5,"疁疅疐疒疓疕疙疜疢疤疴疺疿痀痁痄痆痌痎痏痗痜痟痠痡痤痧痬痮痯痱痹瘀瘂瘃瘄瘇瘈瘊瘌瘏瘒瘓瘕瘖瘙瘛瘜瘝瘞瘣瘥瘦瘩瘭瘲瘳瘵瘸瘹"],["8fcea1","瘺瘼癊癀癁癃癄癅癉癋癕癙癟癤癥癭癮癯癱癴皁皅皌皍皕皛皜皝皟皠皢",6,"皪皭皽盁盅盉盋盌盎盔盙盠盦盨盬盰盱盶盹盼眀眆眊眎眒眔眕眗眙眚眜眢眨眭眮眯眴眵眶眹眽眾睂睅睆睊睍睎睏睒睖睗睜睞睟睠睢"],["8fcfa1","睤睧睪睬睰睲睳睴睺睽瞀瞄瞌瞍瞔瞕瞖瞚瞟瞢瞧瞪瞮瞯瞱瞵瞾矃矉矑矒矕矙矞矟矠矤矦矪矬矰矱矴矸矻砅砆砉砍砎砑砝砡砢砣砭砮砰砵砷硃硄硇硈硌硎硒硜硞硠硡硣硤硨硪确硺硾碊碏碔碘碡碝碞碟碤碨碬碭碰碱碲碳"],["8fd0a1","碻碽碿磇磈磉磌磎磒磓磕磖磤磛磟磠磡磦磪磲磳礀磶磷磺磻磿礆礌礐礚礜礞礟礠礥礧礩礭礱礴礵礻礽礿祄祅祆祊祋祏祑祔祘祛祜祧祩祫祲祹祻祼祾禋禌禑禓禔禕禖禘禛禜禡禨禩禫禯禱禴禸离秂秄秇秈秊秏秔秖秚秝秞"],["8fd1a1","秠秢秥秪秫秭秱秸秼稂稃稇稉稊稌稑稕稛稞稡稧稫稭稯稰稴稵稸稹稺穄穅穇穈穌穕穖穙穜穝穟穠穥穧穪穭穵穸穾窀窂窅窆窊窋窐窑窔窞窠窣窬窳窵窹窻窼竆竉竌竎竑竛竨竩竫竬竱竴竻竽竾笇笔笟笣笧笩笪笫笭笮笯笰"],["8fd2a1","笱笴笽笿筀筁筇筎筕筠筤筦筩筪筭筯筲筳筷箄箉箎箐箑箖箛箞箠箥箬箯箰箲箵箶箺箻箼箽篂篅篈篊篔篖篗篙篚篛篨篪篲篴篵篸篹篺篼篾簁簂簃簄簆簉簋簌簎簏簙簛簠簥簦簨簬簱簳簴簶簹簺籆籊籕籑籒籓籙",5],["8fd3a1","籡籣籧籩籭籮籰籲籹籼籽粆粇粏粔粞粠粦粰粶粷粺粻粼粿糄糇糈糉糍糏糓糔糕糗糙糚糝糦糩糫糵紃紇紈紉紏紑紒紓紖紝紞紣紦紪紭紱紼紽紾絀絁絇絈絍絑絓絗絙絚絜絝絥絧絪絰絸絺絻絿綁綂綃綅綆綈綋綌綍綑綖綗綝"],["8fd4a1","綞綦綧綪綳綶綷綹緂",4,"緌緍緎緗緙縀緢緥緦緪緫緭緱緵緶緹緺縈縐縑縕縗縜縝縠縧縨縬縭縯縳縶縿繄繅繇繎繐繒繘繟繡繢繥繫繮繯繳繸繾纁纆纇纊纍纑纕纘纚纝纞缼缻缽缾缿罃罄罇罏罒罓罛罜罝罡罣罤罥罦罭"],["8fd5a1","罱罽罾罿羀羋羍羏羐羑羖羗羜羡羢羦羪羭羴羼羿翀翃翈翎翏翛翟翣翥翨翬翮翯翲翺翽翾翿耇耈耊耍耎耏耑耓耔耖耝耞耟耠耤耦耬耮耰耴耵耷耹耺耼耾聀聄聠聤聦聭聱聵肁肈肎肜肞肦肧肫肸肹胈胍胏胒胔胕胗胘胠胭胮"],["8fd6a1","胰胲胳胶胹胺胾脃脋脖脗脘脜脞脠脤脧脬脰脵脺脼腅腇腊腌腒腗腠腡腧腨腩腭腯腷膁膐膄膅膆膋膎膖膘膛膞膢膮膲膴膻臋臃臅臊臎臏臕臗臛臝臞臡臤臫臬臰臱臲臵臶臸臹臽臿舀舃舏舓舔舙舚舝舡舢舨舲舴舺艃艄艅艆"],["8fd7a1","艋艎艏艑艖艜艠艣艧艭艴艻艽艿芀芁芃芄芇芉芊芎芑芔芖芘芚芛芠芡芣芤芧芨芩芪芮芰芲芴芷芺芼芾芿苆苐苕苚苠苢苤苨苪苭苯苶苷苽苾茀茁茇茈茊茋荔茛茝茞茟茡茢茬茭茮茰茳茷茺茼茽荂荃荄荇荍荎荑荕荖荗荰荸"],["8fd8a1","荽荿莀莂莄莆莍莒莔莕莘莙莛莜莝莦莧莩莬莾莿菀菇菉菏菐菑菔菝荓菨菪菶菸菹菼萁萆萊萏萑萕萙莭萯萹葅葇葈葊葍葏葑葒葖葘葙葚葜葠葤葥葧葪葰葳葴葶葸葼葽蒁蒅蒒蒓蒕蒞蒦蒨蒩蒪蒯蒱蒴蒺蒽蒾蓀蓂蓇蓈蓌蓏蓓"],["8fd9a1","蓜蓧蓪蓯蓰蓱蓲蓷蔲蓺蓻蓽蔂蔃蔇蔌蔎蔐蔜蔞蔢蔣蔤蔥蔧蔪蔫蔯蔳蔴蔶蔿蕆蕏",4,"蕖蕙蕜",6,"蕤蕫蕯蕹蕺蕻蕽蕿薁薅薆薉薋薌薏薓薘薝薟薠薢薥薧薴薶薷薸薼薽薾薿藂藇藊藋藎薭藘藚藟藠藦藨藭藳藶藼"],["8fdaa1","藿蘀蘄蘅蘍蘎蘐蘑蘒蘘蘙蘛蘞蘡蘧蘩蘶蘸蘺蘼蘽虀虂虆虒虓虖虗虘虙虝虠",4,"虩虬虯虵虶虷虺蚍蚑蚖蚘蚚蚜蚡蚦蚧蚨蚭蚱蚳蚴蚵蚷蚸蚹蚿蛀蛁蛃蛅蛑蛒蛕蛗蛚蛜蛠蛣蛥蛧蚈蛺蛼蛽蜄蜅蜇蜋蜎蜏蜐蜓蜔蜙蜞蜟蜡蜣"],["8fdba1","蜨蜮蜯蜱蜲蜹蜺蜼蜽蜾蝀蝃蝅蝍蝘蝝蝡蝤蝥蝯蝱蝲蝻螃",6,"螋螌螐螓螕螗螘螙螞螠螣螧螬螭螮螱螵螾螿蟁蟈蟉蟊蟎蟕蟖蟙蟚蟜蟟蟢蟣蟤蟪蟫蟭蟱蟳蟸蟺蟿蠁蠃蠆蠉蠊蠋蠐蠙蠒蠓蠔蠘蠚蠛蠜蠞蠟蠨蠭蠮蠰蠲蠵"],["8fdca1","蠺蠼衁衃衅衈衉衊衋衎衑衕衖衘衚衜衟衠衤衩衱衹衻袀袘袚袛袜袟袠袨袪袺袽袾裀裊",4,"裑裒裓裛裞裧裯裰裱裵裷褁褆褍褎褏褕褖褘褙褚褜褠褦褧褨褰褱褲褵褹褺褾襀襂襅襆襉襏襒襗襚襛襜襡襢襣襫襮襰襳襵襺"],["8fdda1","襻襼襽覉覍覐覔覕覛覜覟覠覥覰覴覵覶覷覼觔",4,"觥觩觫觭觱觳觶觹觽觿訄訅訇訏訑訒訔訕訞訠訢訤訦訫訬訯訵訷訽訾詀詃詅詇詉詍詎詓詖詗詘詜詝詡詥詧詵詶詷詹詺詻詾詿誀誃誆誋誏誐誒誖誗誙誟誧誩誮誯誳"],["8fdea1","誶誷誻誾諃諆諈諉諊諑諓諔諕諗諝諟諬諰諴諵諶諼諿謅謆謋謑謜謞謟謊謭謰謷謼譂",4,"譈譒譓譔譙譍譞譣譭譶譸譹譼譾讁讄讅讋讍讏讔讕讜讞讟谸谹谽谾豅豇豉豋豏豑豓豔豗豘豛豝豙豣豤豦豨豩豭豳豵豶豻豾貆"],["8fdfa1","貇貋貐貒貓貙貛貜貤貹貺賅賆賉賋賏賖賕賙賝賡賨賬賯賰賲賵賷賸賾賿贁贃贉贒贗贛赥赩赬赮赿趂趄趈趍趐趑趕趞趟趠趦趫趬趯趲趵趷趹趻跀跅跆跇跈跊跎跑跔跕跗跙跤跥跧跬跰趼跱跲跴跽踁踄踅踆踋踑踔踖踠踡踢"],["8fe0a1","踣踦踧踱踳踶踷踸踹踽蹀蹁蹋蹍蹎蹏蹔蹛蹜蹝蹞蹡蹢蹩蹬蹭蹯蹰蹱蹹蹺蹻躂躃躉躐躒躕躚躛躝躞躢躧躩躭躮躳躵躺躻軀軁軃軄軇軏軑軔軜軨軮軰軱軷軹軺軭輀輂輇輈輏輐輖輗輘輞輠輡輣輥輧輨輬輭輮輴輵輶輷輺轀轁"],["8fe1a1","轃轇轏轑",4,"轘轝轞轥辝辠辡辤辥辦辵辶辸达迀迁迆迊迋迍运迒迓迕迠迣迤迨迮迱迵迶迻迾适逄逈逌逘逛逨逩逯逪逬逭逳逴逷逿遃遄遌遛遝遢遦遧遬遰遴遹邅邈邋邌邎邐邕邗邘邙邛邠邡邢邥邰邲邳邴邶邽郌邾郃"],["8fe2a1","郄郅郇郈郕郗郘郙郜郝郟郥郒郶郫郯郰郴郾郿鄀鄄鄅鄆鄈鄍鄐鄔鄖鄗鄘鄚鄜鄞鄠鄥鄢鄣鄧鄩鄮鄯鄱鄴鄶鄷鄹鄺鄼鄽酃酇酈酏酓酗酙酚酛酡酤酧酭酴酹酺酻醁醃醅醆醊醎醑醓醔醕醘醞醡醦醨醬醭醮醰醱醲醳醶醻醼醽醿"],["8fe3a1","釂釃釅釓釔釗釙釚釞釤釥釩釪釬",5,"釷釹釻釽鈀鈁鈄鈅鈆鈇鈉鈊鈌鈐鈒鈓鈖鈘鈜鈝鈣鈤鈥鈦鈨鈮鈯鈰鈳鈵鈶鈸鈹鈺鈼鈾鉀鉂鉃鉆鉇鉊鉍鉎鉏鉑鉘鉙鉜鉝鉠鉡鉥鉧鉨鉩鉮鉯鉰鉵",4,"鉻鉼鉽鉿銈銉銊銍銎銒銗"],["8fe4a1","銙銟銠銤銥銧銨銫銯銲銶銸銺銻銼銽銿",4,"鋅鋆鋇鋈鋋鋌鋍鋎鋐鋓鋕鋗鋘鋙鋜鋝鋟鋠鋡鋣鋥鋧鋨鋬鋮鋰鋹鋻鋿錀錂錈錍錑錔錕錜錝錞錟錡錤錥錧錩錪錳錴錶錷鍇鍈鍉鍐鍑鍒鍕鍗鍘鍚鍞鍤鍥鍧鍩鍪鍭鍯鍰鍱鍳鍴鍶"],["8fe5a1","鍺鍽鍿鎀鎁鎂鎈鎊鎋鎍鎏鎒鎕鎘鎛鎞鎡鎣鎤鎦鎨鎫鎴鎵鎶鎺鎩鏁鏄鏅鏆鏇鏉",4,"鏓鏙鏜鏞鏟鏢鏦鏧鏹鏷鏸鏺鏻鏽鐁鐂鐄鐈鐉鐍鐎鐏鐕鐖鐗鐟鐮鐯鐱鐲鐳鐴鐻鐿鐽鑃鑅鑈鑊鑌鑕鑙鑜鑟鑡鑣鑨鑫鑭鑮鑯鑱鑲钄钃镸镹"],["8fe6a1","镾閄閈閌閍閎閝閞閟閡閦閩閫閬閴閶閺閽閿闆闈闉闋闐闑闒闓闙闚闝闞闟闠闤闦阝阞阢阤阥阦阬阱阳阷阸阹阺阼阽陁陒陔陖陗陘陡陮陴陻陼陾陿隁隂隃隄隉隑隖隚隝隟隤隥隦隩隮隯隳隺雊雒嶲雘雚雝雞雟雩雯雱雺霂"],["8fe7a1","霃霅霉霚霛霝霡霢霣霨霱霳靁靃靊靎靏靕靗靘靚靛靣靧靪靮靳靶靷靸靻靽靿鞀鞉鞕鞖鞗鞙鞚鞞鞟鞢鞬鞮鞱鞲鞵鞶鞸鞹鞺鞼鞾鞿韁韄韅韇韉韊韌韍韎韐韑韔韗韘韙韝韞韠韛韡韤韯韱韴韷韸韺頇頊頙頍頎頔頖頜頞頠頣頦"],["8fe8a1","頫頮頯頰頲頳頵頥頾顄顇顊顑顒顓顖顗顙顚顢顣顥顦顪顬颫颭颮颰颴颷颸颺颻颿飂飅飈飌飡飣飥飦飧飪飳飶餂餇餈餑餕餖餗餚餛餜餟餢餦餧餫餱",4,"餹餺餻餼饀饁饆饇饈饍饎饔饘饙饛饜饞饟饠馛馝馟馦馰馱馲馵"],["8fe9a1","馹馺馽馿駃駉駓駔駙駚駜駞駧駪駫駬駰駴駵駹駽駾騂騃騄騋騌騐騑騖騞騠騢騣騤騧騭騮騳騵騶騸驇驁驄驊驋驌驎驑驔驖驝骪骬骮骯骲骴骵骶骹骻骾骿髁髃髆髈髎髐髒髕髖髗髛髜髠髤髥髧髩髬髲髳髵髹髺髽髿",4],["8feaa1","鬄鬅鬈鬉鬋鬌鬍鬎鬐鬒鬖鬙鬛鬜鬠鬦鬫鬭鬳鬴鬵鬷鬹鬺鬽魈魋魌魕魖魗魛魞魡魣魥魦魨魪",4,"魳魵魷魸魹魿鮀鮄鮅鮆鮇鮉鮊鮋鮍鮏鮐鮔鮚鮝鮞鮦鮧鮩鮬鮰鮱鮲鮷鮸鮻鮼鮾鮿鯁鯇鯈鯎鯐鯗鯘鯝鯟鯥鯧鯪鯫鯯鯳鯷鯸"],["8feba1","鯹鯺鯽鯿鰀鰂鰋鰏鰑鰖鰘鰙鰚鰜鰞鰢鰣鰦",4,"鰱鰵鰶鰷鰽鱁鱃鱄鱅鱉鱊鱎鱏鱐鱓鱔鱖鱘鱛鱝鱞鱟鱣鱩鱪鱜鱫鱨鱮鱰鱲鱵鱷鱻鳦鳲鳷鳹鴋鴂鴑鴗鴘鴜鴝鴞鴯鴰鴲鴳鴴鴺鴼鵅鴽鵂鵃鵇鵊鵓鵔鵟鵣鵢鵥鵩鵪鵫鵰鵶鵷鵻"],["8feca1","鵼鵾鶃鶄鶆鶊鶍鶎鶒鶓鶕鶖鶗鶘鶡鶪鶬鶮鶱鶵鶹鶼鶿鷃鷇鷉鷊鷔鷕鷖鷗鷚鷞鷟鷠鷥鷧鷩鷫鷮鷰鷳鷴鷾鸊鸂鸇鸎鸐鸑鸒鸕鸖鸙鸜鸝鹺鹻鹼麀麂麃麄麅麇麎麏麖麘麛麞麤麨麬麮麯麰麳麴麵黆黈黋黕黟黤黧黬黭黮黰黱黲黵"],["8feda1","黸黿鼂鼃鼉鼏鼐鼑鼒鼔鼖鼗鼙鼚鼛鼟鼢鼦鼪鼫鼯鼱鼲鼴鼷鼹鼺鼼鼽鼿齁齃",4,"齓齕齖齗齘齚齝齞齨齩齭",4,"齳齵齺齽龏龐龑龒龔龖龗龞龡龢龣龥"]],Je=[["0","\0",127,"€"],["8140","丂丄丅丆丏丒丗丟丠両丣並丩丮丯丱丳丵丷丼乀乁乂乄乆乊乑乕乗乚乛乢乣乤乥乧乨乪",5,"乲乴",9,"乿",6,"亇亊"],["8180","亐亖亗亙亜亝亞亣亪亯亰亱亴亶亷亸亹亼亽亾仈仌仏仐仒仚仛仜仠仢仦仧仩仭仮仯仱仴仸仹仺仼仾伀伂",6,"伋伌伒",4,"伜伝伡伣伨伩伬伭伮伱伳伵伷伹伻伾",4,"佄佅佇",5,"佒佔佖佡佢佦佨佪佫佭佮佱佲併佷佸佹佺佽侀侁侂侅來侇侊侌侎侐侒侓侕侖侘侙侚侜侞侟価侢"],["8240","侤侫侭侰",4,"侶",8,"俀俁係俆俇俈俉俋俌俍俒",4,"俙俛俠俢俤俥俧俫俬俰俲俴俵俶俷俹俻俼俽俿",11],["8280","個倎倐們倓倕倖倗倛倝倞倠倢倣値倧倫倯",10,"倻倽倿偀偁偂偄偅偆偉偊偋偍偐",4,"偖偗偘偙偛偝",7,"偦",5,"偭",8,"偸偹偺偼偽傁傂傃傄傆傇傉傊傋傌傎",20,"傤傦傪傫傭",4,"傳",6,"傼"],["8340","傽",17,"僐",5,"僗僘僙僛",10,"僨僩僪僫僯僰僱僲僴僶",4,"僼",9,"儈"],["8380","儉儊儌",5,"儓",13,"儢",28,"兂兇兊兌兎兏児兒兓兗兘兙兛兝",4,"兣兤兦內兩兪兯兲兺兾兿冃冄円冇冊冋冎冏冐冑冓冔冘冚冝冞冟冡冣冦",4,"冭冮冴冸冹冺冾冿凁凂凃凅凈凊凍凎凐凒",5],["8440","凘凙凚凜凞凟凢凣凥",5,"凬凮凱凲凴凷凾刄刅刉刋刌刏刐刓刔刕刜刞刟刡刢刣別刦刧刪刬刯刱刲刴刵刼刾剄",5,"剋剎剏剒剓剕剗剘"],["8480","剙剚剛剝剟剠剢剣剤剦剨剫剬剭剮剰剱剳",9,"剾劀劃",4,"劉",6,"劑劒劔",6,"劜劤劥劦劧劮劯劰労",9,"勀勁勂勄勅勆勈勊勌勍勎勏勑勓勔動勗務",5,"勠勡勢勣勥",10,"勱",7,"勻勼勽匁匂匃匄匇匉匊匋匌匎"],["8540","匑匒匓匔匘匛匜匞匟匢匤匥匧匨匩匫匬匭匯",9,"匼匽區卂卄卆卋卌卍卐協単卙卛卝卥卨卪卬卭卲卶卹卻卼卽卾厀厁厃厇厈厊厎厏"],["8580","厐",4,"厖厗厙厛厜厞厠厡厤厧厪厫厬厭厯",6,"厷厸厹厺厼厽厾叀參",4,"収叏叐叒叓叕叚叜叝叞叡叢叧叴叺叾叿吀吂吅吇吋吔吘吙吚吜吢吤吥吪吰吳吶吷吺吽吿呁呂呄呅呇呉呌呍呎呏呑呚呝",4,"呣呥呧呩",7,"呴呹呺呾呿咁咃咅咇咈咉咊咍咑咓咗咘咜咞咟咠咡"],["8640","咢咥咮咰咲咵咶咷咹咺咼咾哃哅哊哋哖哘哛哠",4,"哫哬哯哰哱哴",5,"哻哾唀唂唃唄唅唈唊",4,"唒唓唕",5,"唜唝唞唟唡唥唦"],["8680","唨唩唫唭唲唴唵唶唸唹唺唻唽啀啂啅啇啈啋",4,"啑啒啓啔啗",4,"啝啞啟啠啢啣啨啩啫啯",5,"啹啺啽啿喅喆喌喍喎喐喒喓喕喖喗喚喛喞喠",6,"喨",8,"喲喴営喸喺喼喿",4,"嗆嗇嗈嗊嗋嗎嗏嗐嗕嗗",4,"嗞嗠嗢嗧嗩嗭嗮嗰嗱嗴嗶嗸",4,"嗿嘂嘃嘄嘅"],["8740","嘆嘇嘊嘋嘍嘐",7,"嘙嘚嘜嘝嘠嘡嘢嘥嘦嘨嘩嘪嘫嘮嘯嘰嘳嘵嘷嘸嘺嘼嘽嘾噀",11,"噏",4,"噕噖噚噛噝",4],["8780","噣噥噦噧噭噮噯噰噲噳噴噵噷噸噹噺噽",7,"嚇",6,"嚐嚑嚒嚔",14,"嚤",10,"嚰",6,"嚸嚹嚺嚻嚽",12,"囋",8,"囕囖囘囙囜団囥",5,"囬囮囯囲図囶囷囸囻囼圀圁圂圅圇國",6],["8840","園",9,"圝圞圠圡圢圤圥圦圧圫圱圲圴",4,"圼圽圿坁坃坄坅坆坈坉坋坒",4,"坘坙坢坣坥坧坬坮坰坱坲坴坵坸坹坺坽坾坿垀"],["8880","垁垇垈垉垊垍",4,"垔",6,"垜垝垞垟垥垨垪垬垯垰垱垳垵垶垷垹",8,"埄",6,"埌埍埐埑埓埖埗埛埜埞埡埢埣埥",7,"埮埰埱埲埳埵埶執埻埼埾埿堁堃堄堅堈堉堊堌堎堏堐堒堓堔堖堗堘堚堛堜堝堟堢堣堥",4,"堫",4,"報堲堳場堶",7],["8940","堾",5,"塅",6,"塎塏塐塒塓塕塖塗塙",4,"塟",5,"塦",4,"塭",16,"塿墂墄墆墇墈墊墋墌"],["8980","墍",4,"墔",4,"墛墜墝墠",7,"墪",17,"墽墾墿壀壂壃壄壆",10,"壒壓壔壖",13,"壥",5,"壭壯壱売壴壵壷壸壺",7,"夃夅夆夈",4,"夎夐夑夒夓夗夘夛夝夞夠夡夢夣夦夨夬夰夲夳夵夶夻"],["8a40","夽夾夿奀奃奅奆奊奌奍奐奒奓奙奛",4,"奡奣奤奦",12,"奵奷奺奻奼奾奿妀妅妉妋妌妎妏妐妑妔妕妘妚妛妜妝妟妠妡妢妦"],["8a80","妧妬妭妰妱妳",5,"妺妼妽妿",6,"姇姈姉姌姍姎姏姕姖姙姛姞",4,"姤姦姧姩姪姫姭",11,"姺姼姽姾娀娂娊娋娍娎娏娐娒娔娕娖娗娙娚娛娝娞娡娢娤娦娧娨娪",6,"娳娵娷",4,"娽娾娿婁",4,"婇婈婋",9,"婖婗婘婙婛",5],["8b40","婡婣婤婥婦婨婩婫",8,"婸婹婻婼婽婾媀",17,"媓",6,"媜",13,"媫媬"],["8b80","媭",4,"媴媶媷媹",4,"媿嫀嫃",5,"嫊嫋嫍",4,"嫓嫕嫗嫙嫚嫛嫝嫞嫟嫢嫤嫥嫧嫨嫪嫬",4,"嫲",22,"嬊",11,"嬘",25,"嬳嬵嬶嬸",7,"孁",6],["8c40","孈",7,"孒孖孞孠孡孧孨孫孭孮孯孲孴孶孷學孹孻孼孾孿宂宆宊宍宎宐宑宒宔宖実宧宨宩宬宭宮宯宱宲宷宺宻宼寀寁寃寈寉寊寋寍寎寏"],["8c80","寑寔",8,"寠寢寣實寧審",4,"寯寱",6,"寽対尀専尃尅將專尋尌對導尐尒尓尗尙尛尞尟尠尡尣尦尨尩尪尫尭尮尯尰尲尳尵尶尷屃屄屆屇屌屍屒屓屔屖屗屘屚屛屜屝屟屢層屧",6,"屰屲",6,"屻屼屽屾岀岃",4,"岉岊岋岎岏岒岓岕岝",4,"岤",4],["8d40","岪岮岯岰岲岴岶岹岺岻岼岾峀峂峃峅",5,"峌",5,"峓",5,"峚",6,"峢峣峧峩峫峬峮峯峱",9,"峼",4],["8d80","崁崄崅崈",5,"崏",4,"崕崗崘崙崚崜崝崟",4,"崥崨崪崫崬崯",4,"崵",7,"崿",7,"嵈嵉嵍",10,"嵙嵚嵜嵞",10,"嵪嵭嵮嵰嵱嵲嵳嵵",12,"嶃",21,"嶚嶛嶜嶞嶟嶠"],["8e40","嶡",21,"嶸",12,"巆",6,"巎",12,"巜巟巠巣巤巪巬巭"],["8e80","巰巵巶巸",4,"巿帀帄帇帉帊帋帍帎帒帓帗帞",7,"帨",4,"帯帰帲",4,"帹帺帾帿幀幁幃幆",5,"幍",6,"幖",4,"幜幝幟幠幣",14,"幵幷幹幾庁庂広庅庈庉庌庍庎庒庘庛庝庡庢庣庤庨",4,"庮",4,"庴庺庻庼庽庿",6],["8f40","廆廇廈廋",5,"廔廕廗廘廙廚廜",11,"廩廫",8,"廵廸廹廻廼廽弅弆弇弉弌弍弎弐弒弔弖弙弚弜弝弞弡弢弣弤"],["8f80","弨弫弬弮弰弲",6,"弻弽弾弿彁",14,"彑彔彙彚彛彜彞彟彠彣彥彧彨彫彮彯彲彴彵彶彸彺彽彾彿徃徆徍徎徏徑従徔徖徚徛徝從徟徠徢",5,"復徫徬徯",5,"徶徸徹徺徻徾",4,"忇忈忊忋忎忓忔忕忚忛応忞忟忢忣忥忦忨忩忬忯忰忲忳忴忶忷忹忺忼怇"],["9040","怈怉怋怌怐怑怓怗怘怚怞怟怢怣怤怬怭怮怰",4,"怶",4,"怽怾恀恄",6,"恌恎恏恑恓恔恖恗恘恛恜恞恟恠恡恥恦恮恱恲恴恵恷恾悀"],["9080","悁悂悅悆悇悈悊悋悎悏悐悑悓悕悗悘悙悜悞悡悢悤悥悧悩悪悮悰悳悵悶悷悹悺悽",7,"惇惈惉惌",4,"惒惓惔惖惗惙惛惞惡",4,"惪惱惲惵惷惸惻",4,"愂愃愄愅愇愊愋愌愐",4,"愖愗愘愙愛愜愝愞愡愢愥愨愩愪愬",18,"慀",6],["9140","慇慉態慍慏慐慒慓慔慖",6,"慞慟慠慡慣慤慥慦慩",6,"慱慲慳慴慶慸",18,"憌憍憏",4,"憕"],["9180","憖",6,"憞",8,"憪憫憭",9,"憸",5,"憿懀懁懃",4,"應懌",4,"懓懕",16,"懧",13,"懶",8,"戀",5,"戇戉戓戔戙戜戝戞戠戣戦戧戨戩戫戭戯戰戱戲戵戶戸",4,"扂扄扅扆扊"],["9240","扏扐払扖扗扙扚扜",6,"扤扥扨扱扲扴扵扷扸扺扻扽抁抂抃抅抆抇抈抋",5,"抔抙抜抝択抣抦抧抩抪抭抮抯抰抲抳抴抶抷抸抺抾拀拁"],["9280","拃拋拏拑拕拝拞拠拡拤拪拫拰拲拵拸拹拺拻挀挃挄挅挆挊挋挌挍挏挐挒挓挔挕挗挘挙挜挦挧挩挬挭挮挰挱挳",5,"挻挼挾挿捀捁捄捇捈捊捑捒捓捔捖",7,"捠捤捥捦捨捪捫捬捯捰捲捳捴捵捸捹捼捽捾捿掁掃掄掅掆掋掍掑掓掔掕掗掙",6,"採掤掦掫掯掱掲掵掶掹掻掽掿揀"],["9340","揁揂揃揅揇揈揊揋揌揑揓揔揕揗",6,"揟揢揤",4,"揫揬揮揯揰揱揳揵揷揹揺揻揼揾搃搄搆",4,"損搎搑搒搕",5,"搝搟搢搣搤"],["9380","搥搧搨搩搫搮",5,"搵",4,"搻搼搾摀摂摃摉摋",6,"摓摕摖摗摙",4,"摟",7,"摨摪摫摬摮",9,"摻",6,"撃撆撈",8,"撓撔撗撘撚撛撜撝撟",4,"撥撦撧撨撪撫撯撱撲撳撴撶撹撻撽撾撿擁擃擄擆",6,"擏擑擓擔擕擖擙據"],["9440","擛擜擝擟擠擡擣擥擧",24,"攁",7,"攊",7,"攓",4,"攙",8],["9480","攢攣攤攦",4,"攬攭攰攱攲攳攷攺攼攽敀",4,"敆敇敊敋敍敎敐敒敓敔敗敘敚敜敟敠敡敤敥敧敨敩敪敭敮敯敱敳敵敶數",14,"斈斉斊斍斎斏斒斔斕斖斘斚斝斞斠斢斣斦斨斪斬斮斱",7,"斺斻斾斿旀旂旇旈旉旊旍旐旑旓旔旕旘",7,"旡旣旤旪旫"],["9540","旲旳旴旵旸旹旻",4,"昁昄昅昇昈昉昋昍昐昑昒昖昗昘昚昛昜昞昡昢昣昤昦昩昪昫昬昮昰昲昳昷",4,"昽昿晀時晄",6,"晍晎晐晑晘"],["9580","晙晛晜晝晞晠晢晣晥晧晩",4,"晱晲晳晵晸晹晻晼晽晿暀暁暃暅暆暈暉暊暋暍暎暏暐暒暓暔暕暘",4,"暞",8,"暩",4,"暯",4,"暵暶暷暸暺暻暼暽暿",25,"曚曞",7,"曧曨曪",5,"曱曵曶書曺曻曽朁朂會"],["9640","朄朅朆朇朌朎朏朑朒朓朖朘朙朚朜朞朠",5,"朧朩朮朰朲朳朶朷朸朹朻朼朾朿杁杄杅杇杊杋杍杒杔杕杗",4,"杝杢杣杤杦杧杫杬杮東杴杶"],["9680","杸杹杺杻杽枀枂枃枅枆枈枊枌枍枎枏枑枒枓枔枖枙枛枟枠枡枤枦枩枬枮枱枲枴枹",7,"柂柅",9,"柕柖柗柛柟柡柣柤柦柧柨柪柫柭柮柲柵",7,"柾栁栂栃栄栆栍栐栒栔栕栘",4,"栞栟栠栢",6,"栫",6,"栴栵栶栺栻栿桇桋桍桏桒桖",5],["9740","桜桝桞桟桪桬",7,"桵桸",8,"梂梄梇",7,"梐梑梒梔梕梖梘",9,"梣梤梥梩梪梫梬梮梱梲梴梶梷梸"],["9780","梹",6,"棁棃",5,"棊棌棎棏棐棑棓棔棖棗棙棛",4,"棡棢棤",9,"棯棲棳棴棶棷棸棻棽棾棿椀椂椃椄椆",4,"椌椏椑椓",11,"椡椢椣椥",7,"椮椯椱椲椳椵椶椷椸椺椻椼椾楀楁楃",16,"楕楖楘楙楛楜楟"],["9840","楡楢楤楥楧楨楩楪楬業楯楰楲",4,"楺楻楽楾楿榁榃榅榊榋榌榎",5,"榖榗榙榚榝",9,"榩榪榬榮榯榰榲榳榵榶榸榹榺榼榽"],["9880","榾榿槀槂",7,"構槍槏槑槒槓槕",5,"槜槝槞槡",11,"槮槯槰槱槳",9,"槾樀",9,"樋",11,"標",5,"樠樢",5,"権樫樬樭樮樰樲樳樴樶",6,"樿",4,"橅橆橈",7,"橑",6,"橚"],["9940","橜",4,"橢橣橤橦",10,"橲",6,"橺橻橽橾橿檁檂檃檅",8,"檏檒",4,"檘",7,"檡",5],["9980","檧檨檪檭",114,"欥欦欨",6],["9a40","欯欰欱欳欴欵欶欸欻欼欽欿歀歁歂歄歅歈歊歋歍",11,"歚",7,"歨歩歫",13,"歺歽歾歿殀殅殈"],["9a80","殌殎殏殐殑殔殕殗殘殙殜",4,"殢",7,"殫",7,"殶殸",6,"毀毃毄毆",4,"毌毎毐毑毘毚毜",4,"毢",7,"毬毭毮毰毱毲毴毶毷毸毺毻毼毾",6,"氈",4,"氎氒気氜氝氞氠氣氥氫氬氭氱氳氶氷氹氺氻氼氾氿汃汄汅汈汋",4,"汑汒汓汖汘"],["9b40","汙汚汢汣汥汦汧汫",4,"汱汳汵汷汸決汻汼汿沀沄沇沊沋沍沎沑沒沕沖沗沘沚沜沝沞沠沢沨沬沯沰沴沵沶沷沺泀況泂泃泆泇泈泋泍泎泏泑泒泘"],["9b80","泙泚泜泝泟泤泦泧泩泬泭泲泴泹泿洀洂洃洅洆洈洉洊洍洏洐洑洓洔洕洖洘洜洝洟",5,"洦洨洩洬洭洯洰洴洶洷洸洺洿浀浂浄浉浌浐浕浖浗浘浛浝浟浡浢浤浥浧浨浫浬浭浰浱浲浳浵浶浹浺浻浽",4,"涃涄涆涇涊涋涍涏涐涒涖",4,"涜涢涥涬涭涰涱涳涴涶涷涹",5,"淁淂淃淈淉淊"],["9c40","淍淎淏淐淒淓淔淕淗淚淛淜淟淢淣淥淧淨淩淪淭淯淰淲淴淵淶淸淺淽",7,"渆渇済渉渋渏渒渓渕渘渙減渜渞渟渢渦渧渨渪測渮渰渱渳渵"],["9c80","渶渷渹渻",7,"湅",7,"湏湐湑湒湕湗湙湚湜湝湞湠",10,"湬湭湯",14,"満溁溂溄溇溈溊",4,"溑",6,"溙溚溛溝溞溠溡溣溤溦溨溩溫溬溭溮溰溳溵溸溹溼溾溿滀滃滄滅滆滈滉滊滌滍滎滐滒滖滘滙滛滜滝滣滧滪",5],["9d40","滰滱滲滳滵滶滷滸滺",7,"漃漄漅漇漈漊",4,"漐漑漒漖",9,"漡漢漣漥漦漧漨漬漮漰漲漴漵漷",6,"漿潀潁潂"],["9d80","潃潄潅潈潉潊潌潎",9,"潙潚潛潝潟潠潡潣潤潥潧",5,"潯潰潱潳潵潶潷潹潻潽",6,"澅澆澇澊澋澏",12,"澝澞澟澠澢",4,"澨",10,"澴澵澷澸澺",5,"濁濃",5,"濊",6,"濓",10,"濟濢濣濤濥"],["9e40","濦",7,"濰",32,"瀒",7,"瀜",6,"瀤",6],["9e80","瀫",9,"瀶瀷瀸瀺",17,"灍灎灐",13,"灟",11,"灮灱灲灳灴灷灹灺灻災炁炂炃炄炆炇炈炋炌炍炏炐炑炓炗炘炚炛炞",12,"炰炲炴炵炶為炾炿烄烅烆烇烉烋",12,"烚"],["9f40","烜烝烞烠烡烢烣烥烪烮烰",6,"烸烺烻烼烾",10,"焋",4,"焑焒焔焗焛",10,"焧",7,"焲焳焴"],["9f80","焵焷",13,"煆煇煈煉煋煍煏",12,"煝煟",4,"煥煩",4,"煯煰煱煴煵煶煷煹煻煼煾",5,"熅",4,"熋熌熍熎熐熑熒熓熕熖熗熚",4,"熡",6,"熩熪熫熭",5,"熴熶熷熸熺",8,"燄",9,"燏",4],["a040","燖",9,"燡燢燣燤燦燨",5,"燯",9,"燺",11,"爇",19],["a080","爛爜爞",9,"爩爫爭爮爯爲爳爴爺爼爾牀",6,"牉牊牋牎牏牐牑牓牔牕牗牘牚牜牞牠牣牤牥牨牪牫牬牭牰牱牳牴牶牷牸牻牼牽犂犃犅",4,"犌犎犐犑犓",11,"犠",11,"犮犱犲犳犵犺",6,"狅狆狇狉狊狋狌狏狑狓狔狕狖狘狚狛"],["a1a1","　、。·ˉˇ¨〃々—～‖…‘’“”〔〕〈",7,"〖〗【】±×÷∶∧∨∑∏∪∩∈∷√⊥∥∠⌒⊙∫∮≡≌≈∽∝≠≮≯≤≥∞∵∴♂♀°′″℃＄¤￠￡‰§№☆★○●◎◇◆□■△▲※→←↑↓〓"],["a2a1","ⅰ",9],["a2b1","⒈",19,"⑴",19,"①",9],["a2e5","㈠",9],["a2f1","Ⅰ",11],["a3a1","！＂＃￥％",88,"￣"],["a4a1","ぁ",82],["a5a1","ァ",85],["a6a1","Α",16,"Σ",6],["a6c1","α",16,"σ",6],["a6e0","︵︶︹︺︿﹀︽︾﹁﹂﹃﹄"],["a6ee","︻︼︷︸︱"],["a6f4","︳︴"],["a7a1","А",5,"ЁЖ",25],["a7d1","а",5,"ёж",25],["a840","ˊˋ˙–―‥‵℅℉↖↗↘↙∕∟∣≒≦≧⊿═",35,"▁",6],["a880","█",7,"▓▔▕▼▽◢◣◤◥☉⊕〒〝〞"],["a8a1","āáǎàēéěèīíǐìōóǒòūúǔùǖǘǚǜüêɑ"],["a8bd","ńň"],["a8c0","ɡ"],["a8c5","ㄅ",36],["a940","〡",8,"㊣㎎㎏㎜㎝㎞㎡㏄㏎㏑㏒㏕︰￢￤"],["a959","℡㈱"],["a95c","‐"],["a960","ー゛゜ヽヾ〆ゝゞ﹉",9,"﹔﹕﹖﹗﹙",8],["a980","﹢",4,"﹨﹩﹪﹫"],["a996","〇"],["a9a4","─",75],["aa40","狜狝狟狢",5,"狪狫狵狶狹狽狾狿猀猂猄",5,"猋猌猍猏猐猑猒猔猘猙猚猟猠猣猤猦猧猨猭猯猰猲猳猵猶猺猻猼猽獀",8],["aa80","獉獊獋獌獎獏獑獓獔獕獖獘",7,"獡",10,"獮獰獱"],["ab40","獲",11,"獿",4,"玅玆玈玊玌玍玏玐玒玓玔玕玗玘玙玚玜玝玞玠玡玣",5,"玪玬玭玱玴玵玶玸玹玼玽玾玿珁珃",4],["ab80","珋珌珎珒",6,"珚珛珜珝珟珡珢珣珤珦珨珪珫珬珮珯珰珱珳",4],["ac40","珸",10,"琄琇琈琋琌琍琎琑",8,"琜",5,"琣琤琧琩琫琭琯琱琲琷",4,"琽琾琿瑀瑂",11],["ac80","瑎",6,"瑖瑘瑝瑠",12,"瑮瑯瑱",4,"瑸瑹瑺"],["ad40","瑻瑼瑽瑿璂璄璅璆璈璉璊璌璍璏璑",10,"璝璟",7,"璪",15,"璻",12],["ad80","瓈",9,"瓓",8,"瓝瓟瓡瓥瓧",6,"瓰瓱瓲"],["ae40","瓳瓵瓸",6,"甀甁甂甃甅",7,"甎甐甒甔甕甖甗甛甝甞甠",4,"甦甧甪甮甴甶甹甼甽甿畁畂畃畄畆畇畉畊畍畐畑畒畓畕畖畗畘"],["ae80","畝",7,"畧畨畩畫",6,"畳畵當畷畺",4,"疀疁疂疄疅疇"],["af40","疈疉疊疌疍疎疐疓疕疘疛疜疞疢疦",4,"疭疶疷疺疻疿痀痁痆痋痌痎痏痐痑痓痗痙痚痜痝痟痠痡痥痩痬痭痮痯痲痳痵痶痷痸痺痻痽痾瘂瘄瘆瘇"],["af80","瘈瘉瘋瘍瘎瘏瘑瘒瘓瘔瘖瘚瘜瘝瘞瘡瘣瘧瘨瘬瘮瘯瘱瘲瘶瘷瘹瘺瘻瘽癁療癄"],["b040","癅",6,"癎",5,"癕癗",4,"癝癟癠癡癢癤",6,"癬癭癮癰",7,"癹発發癿皀皁皃皅皉皊皌皍皏皐皒皔皕皗皘皚皛"],["b080","皜",7,"皥",8,"皯皰皳皵",9,"盀盁盃啊阿埃挨哎唉哀皑癌蔼矮艾碍爱隘鞍氨安俺按暗岸胺案肮昂盎凹敖熬翱袄傲奥懊澳芭捌扒叭吧笆八疤巴拔跋靶把耙坝霸罢爸白柏百摆佰败拜稗斑班搬扳般颁板版扮拌伴瓣半办绊邦帮梆榜膀绑棒磅蚌镑傍谤苞胞包褒剥"],["b140","盄盇盉盋盌盓盕盙盚盜盝盞盠",4,"盦",7,"盰盳盵盶盷盺盻盽盿眀眂眃眅眆眊県眎",10,"眛眜眝眞眡眣眤眥眧眪眫"],["b180","眬眮眰",4,"眹眻眽眾眿睂睄睅睆睈",7,"睒",7,"睜薄雹保堡饱宝抱报暴豹鲍爆杯碑悲卑北辈背贝钡倍狈备惫焙被奔苯本笨崩绷甭泵蹦迸逼鼻比鄙笔彼碧蓖蔽毕毙毖币庇痹闭敝弊必辟壁臂避陛鞭边编贬扁便变卞辨辩辫遍标彪膘表鳖憋别瘪彬斌濒滨宾摈兵冰柄丙秉饼炳"],["b240","睝睞睟睠睤睧睩睪睭",11,"睺睻睼瞁瞂瞃瞆",5,"瞏瞐瞓",11,"瞡瞣瞤瞦瞨瞫瞭瞮瞯瞱瞲瞴瞶",4],["b280","瞼瞾矀",12,"矎",8,"矘矙矚矝",4,"矤病并玻菠播拨钵波博勃搏铂箔伯帛舶脖膊渤泊驳捕卜哺补埠不布步簿部怖擦猜裁材才财睬踩采彩菜蔡餐参蚕残惭惨灿苍舱仓沧藏操糙槽曹草厕策侧册测层蹭插叉茬茶查碴搽察岔差诧拆柴豺搀掺蝉馋谗缠铲产阐颤昌猖"],["b340","矦矨矪矯矰矱矲矴矵矷矹矺矻矼砃",5,"砊砋砎砏砐砓砕砙砛砞砠砡砢砤砨砪砫砮砯砱砲砳砵砶砽砿硁硂硃硄硆硈硉硊硋硍硏硑硓硔硘硙硚"],["b380","硛硜硞",11,"硯",7,"硸硹硺硻硽",6,"场尝常长偿肠厂敞畅唱倡超抄钞朝嘲潮巢吵炒车扯撤掣彻澈郴臣辰尘晨忱沉陈趁衬撑称城橙成呈乘程惩澄诚承逞骋秤吃痴持匙池迟弛驰耻齿侈尺赤翅斥炽充冲虫崇宠抽酬畴踌稠愁筹仇绸瞅丑臭初出橱厨躇锄雏滁除楚"],["b440","碄碅碆碈碊碋碏碐碒碔碕碖碙碝碞碠碢碤碦碨",7,"碵碶碷碸確碻碼碽碿磀磂磃磄磆磇磈磌磍磎磏磑磒磓磖磗磘磚",9],["b480","磤磥磦磧磩磪磫磭",4,"磳磵磶磸磹磻",5,"礂礃礄礆",6,"础储矗搐触处揣川穿椽传船喘串疮窗幢床闯创吹炊捶锤垂春椿醇唇淳纯蠢戳绰疵茨磁雌辞慈瓷词此刺赐次聪葱囱匆从丛凑粗醋簇促蹿篡窜摧崔催脆瘁粹淬翠村存寸磋撮搓措挫错搭达答瘩打大呆歹傣戴带殆代贷袋待逮"],["b540","礍",5,"礔",9,"礟",4,"礥",14,"礵",4,"礽礿祂祃祄祅祇祊",8,"祔祕祘祙祡祣"],["b580","祤祦祩祪祫祬祮祰",6,"祹祻",4,"禂禃禆禇禈禉禋禌禍禎禐禑禒怠耽担丹单郸掸胆旦氮但惮淡诞弹蛋当挡党荡档刀捣蹈倒岛祷导到稻悼道盗德得的蹬灯登等瞪凳邓堤低滴迪敌笛狄涤翟嫡抵底地蒂第帝弟递缔颠掂滇碘点典靛垫电佃甸店惦奠淀殿碉叼雕凋刁掉吊钓调跌爹碟蝶迭谍叠"],["b640","禓",6,"禛",11,"禨",10,"禴",4,"禼禿秂秄秅秇秈秊秌秎秏秐秓秔秖秗秙",5,"秠秡秢秥秨秪"],["b680","秬秮秱",6,"秹秺秼秾秿稁稄稅稇稈稉稊稌稏",4,"稕稖稘稙稛稜丁盯叮钉顶鼎锭定订丢东冬董懂动栋侗恫冻洞兜抖斗陡豆逗痘都督毒犊独读堵睹赌杜镀肚度渡妒端短锻段断缎堆兑队对墩吨蹲敦顿囤钝盾遁掇哆多夺垛躲朵跺舵剁惰堕蛾峨鹅俄额讹娥恶厄扼遏鄂饿恩而儿耳尔饵洱二"],["b740","稝稟稡稢稤",14,"稴稵稶稸稺稾穀",5,"穇",9,"穒",4,"穘",16],["b780","穩",6,"穱穲穳穵穻穼穽穾窂窅窇窉窊窋窌窎窏窐窓窔窙窚窛窞窡窢贰发罚筏伐乏阀法珐藩帆番翻樊矾钒繁凡烦反返范贩犯饭泛坊芳方肪房防妨仿访纺放菲非啡飞肥匪诽吠肺废沸费芬酚吩氛分纷坟焚汾粉奋份忿愤粪丰封枫蜂峰锋风疯烽逢冯缝讽奉凤佛否夫敷肤孵扶拂辐幅氟符伏俘服"],["b840","窣窤窧窩窪窫窮",4,"窴",10,"竀",10,"竌",9,"竗竘竚竛竜竝竡竢竤竧",5,"竮竰竱竲竳"],["b880","竴",4,"竻竼竾笀笁笂笅笇笉笌笍笎笐笒笓笖笗笘笚笜笝笟笡笢笣笧笩笭浮涪福袱弗甫抚辅俯釜斧脯腑府腐赴副覆赋复傅付阜父腹负富讣附妇缚咐噶嘎该改概钙盖溉干甘杆柑竿肝赶感秆敢赣冈刚钢缸肛纲岗港杠篙皋高膏羔糕搞镐稿告哥歌搁戈鸽胳疙割革葛格蛤阁隔铬个各给根跟耕更庚羹"],["b940","笯笰笲笴笵笶笷笹笻笽笿",5,"筆筈筊筍筎筓筕筗筙筜筞筟筡筣",10,"筯筰筳筴筶筸筺筼筽筿箁箂箃箄箆",6,"箎箏"],["b980","箑箒箓箖箘箙箚箛箞箟箠箣箤箥箮箯箰箲箳箵箶箷箹",7,"篂篃範埂耿梗工攻功恭龚供躬公宫弓巩汞拱贡共钩勾沟苟狗垢构购够辜菇咕箍估沽孤姑鼓古蛊骨谷股故顾固雇刮瓜剐寡挂褂乖拐怪棺关官冠观管馆罐惯灌贯光广逛瑰规圭硅归龟闺轨鬼诡癸桂柜跪贵刽辊滚棍锅郭国果裹过哈"],["ba40","篅篈築篊篋篍篎篏篐篒篔",4,"篛篜篞篟篠篢篣篤篧篨篩篫篬篭篯篰篲",4,"篸篹篺篻篽篿",7,"簈簉簊簍簎簐",5,"簗簘簙"],["ba80","簚",4,"簠",5,"簨簩簫",12,"簹",5,"籂骸孩海氦亥害骇酣憨邯韩含涵寒函喊罕翰撼捍旱憾悍焊汗汉夯杭航壕嚎豪毫郝好耗号浩呵喝荷菏核禾和何合盒貉阂河涸赫褐鹤贺嘿黑痕很狠恨哼亨横衡恒轰哄烘虹鸿洪宏弘红喉侯猴吼厚候后呼乎忽瑚壶葫胡蝴狐糊湖"],["bb40","籃",9,"籎",36,"籵",5,"籾",9],["bb80","粈粊",6,"粓粔粖粙粚粛粠粡粣粦粧粨粩粫粬粭粯粰粴",4,"粺粻弧虎唬护互沪户花哗华猾滑画划化话槐徊怀淮坏欢环桓还缓换患唤痪豢焕涣宦幻荒慌黄磺蝗簧皇凰惶煌晃幌恍谎灰挥辉徽恢蛔回毁悔慧卉惠晦贿秽会烩汇讳诲绘荤昏婚魂浑混豁活伙火获或惑霍货祸击圾基机畸稽积箕"],["bc40","粿糀糂糃糄糆糉糋糎",6,"糘糚糛糝糞糡",6,"糩",5,"糰",7,"糹糺糼",13,"紋",5],["bc80","紑",14,"紡紣紤紥紦紨紩紪紬紭紮細",6,"肌饥迹激讥鸡姬绩缉吉极棘辑籍集及急疾汲即嫉级挤几脊己蓟技冀季伎祭剂悸济寄寂计记既忌际妓继纪嘉枷夹佳家加荚颊贾甲钾假稼价架驾嫁歼监坚尖笺间煎兼肩艰奸缄茧检柬碱硷拣捡简俭剪减荐槛鉴践贱见键箭件"],["bd40","紷",54,"絯",7],["bd80","絸",32,"健舰剑饯渐溅涧建僵姜将浆江疆蒋桨奖讲匠酱降蕉椒礁焦胶交郊浇骄娇嚼搅铰矫侥脚狡角饺缴绞剿教酵轿较叫窖揭接皆秸街阶截劫节桔杰捷睫竭洁结解姐戒藉芥界借介疥诫届巾筋斤金今津襟紧锦仅谨进靳晋禁近烬浸"],["be40","継",12,"綧",6,"綯",42],["be80","線",32,"尽劲荆兢茎睛晶鲸京惊精粳经井警景颈静境敬镜径痉靖竟竞净炯窘揪究纠玖韭久灸九酒厩救旧臼舅咎就疚鞠拘狙疽居驹菊局咀矩举沮聚拒据巨具距踞锯俱句惧炬剧捐鹃娟倦眷卷绢撅攫抉掘倔爵觉决诀绝均菌钧军君峻"],["bf40","緻",62],["bf80","縺縼",4,"繂",4,"繈",21,"俊竣浚郡骏喀咖卡咯开揩楷凯慨刊堪勘坎砍看康慷糠扛抗亢炕考拷烤靠坷苛柯棵磕颗科壳咳可渴克刻客课肯啃垦恳坑吭空恐孔控抠口扣寇枯哭窟苦酷库裤夸垮挎跨胯块筷侩快宽款匡筐狂框矿眶旷况亏盔岿窥葵奎魁傀"],["c040","繞",35,"纃",23,"纜纝纞"],["c080","纮纴纻纼绖绤绬绹缊缐缞缷缹缻",6,"罃罆",9,"罒罓馈愧溃坤昆捆困括扩廓阔垃拉喇蜡腊辣啦莱来赖蓝婪栏拦篮阑兰澜谰揽览懒缆烂滥琅榔狼廊郎朗浪捞劳牢老佬姥酪烙涝勒乐雷镭蕾磊累儡垒擂肋类泪棱楞冷厘梨犁黎篱狸离漓理李里鲤礼莉荔吏栗丽厉励砾历利傈例俐"],["c140","罖罙罛罜罝罞罠罣",4,"罫罬罭罯罰罳罵罶罷罸罺罻罼罽罿羀羂",7,"羋羍羏",4,"羕",4,"羛羜羠羢羣羥羦羨",6,"羱"],["c180","羳",4,"羺羻羾翀翂翃翄翆翇翈翉翋翍翏",4,"翖翗翙",5,"翢翣痢立粒沥隶力璃哩俩联莲连镰廉怜涟帘敛脸链恋炼练粮凉梁粱良两辆量晾亮谅撩聊僚疗燎寥辽潦了撂镣廖料列裂烈劣猎琳林磷霖临邻鳞淋凛赁吝拎玲菱零龄铃伶羚凌灵陵岭领另令溜琉榴硫馏留刘瘤流柳六龙聋咙笼窿"],["c240","翤翧翨翪翫翬翭翯翲翴",6,"翽翾翿耂耇耈耉耊耎耏耑耓耚耛耝耞耟耡耣耤耫",5,"耲耴耹耺耼耾聀聁聄聅聇聈聉聎聏聐聑聓聕聖聗"],["c280","聙聛",13,"聫",5,"聲",11,"隆垄拢陇楼娄搂篓漏陋芦卢颅庐炉掳卤虏鲁麓碌露路赂鹿潞禄录陆戮驴吕铝侣旅履屡缕虑氯律率滤绿峦挛孪滦卵乱掠略抡轮伦仑沦纶论萝螺罗逻锣箩骡裸落洛骆络妈麻玛码蚂马骂嘛吗埋买麦卖迈脉瞒馒蛮满蔓曼慢漫"],["c340","聾肁肂肅肈肊肍",5,"肔肕肗肙肞肣肦肧肨肬肰肳肵肶肸肹肻胅胇",4,"胏",6,"胘胟胠胢胣胦胮胵胷胹胻胾胿脀脁脃脄脅脇脈脋"],["c380","脌脕脗脙脛脜脝脟",12,"脭脮脰脳脴脵脷脹",4,"脿谩芒茫盲氓忙莽猫茅锚毛矛铆卯茂冒帽貌贸么玫枚梅酶霉煤没眉媒镁每美昧寐妹媚门闷们萌蒙檬盟锰猛梦孟眯醚靡糜迷谜弥米秘觅泌蜜密幂棉眠绵冕免勉娩缅面苗描瞄藐秒渺庙妙蔑灭民抿皿敏悯闽明螟鸣铭名命谬摸"],["c440","腀",5,"腇腉腍腎腏腒腖腗腘腛",4,"腡腢腣腤腦腨腪腫腬腯腲腳腵腶腷腸膁膃",4,"膉膋膌膍膎膐膒",5,"膙膚膞",4,"膤膥"],["c480","膧膩膫",7,"膴",5,"膼膽膾膿臄臅臇臈臉臋臍",6,"摹蘑模膜磨摩魔抹末莫墨默沫漠寞陌谋牟某拇牡亩姆母墓暮幕募慕木目睦牧穆拿哪呐钠那娜纳氖乃奶耐奈南男难囊挠脑恼闹淖呢馁内嫩能妮霓倪泥尼拟你匿腻逆溺蔫拈年碾撵捻念娘酿鸟尿捏聂孽啮镊镍涅您柠狞凝宁"],["c540","臔",14,"臤臥臦臨臩臫臮",4,"臵",5,"臽臿舃與",4,"舎舏舑舓舕",5,"舝舠舤舥舦舧舩舮舲舺舼舽舿"],["c580","艀艁艂艃艅艆艈艊艌艍艎艐",7,"艙艛艜艝艞艠",7,"艩拧泞牛扭钮纽脓浓农弄奴努怒女暖虐疟挪懦糯诺哦欧鸥殴藕呕偶沤啪趴爬帕怕琶拍排牌徘湃派攀潘盘磐盼畔判叛乓庞旁耪胖抛咆刨炮袍跑泡呸胚培裴赔陪配佩沛喷盆砰抨烹澎彭蓬棚硼篷膨朋鹏捧碰坯砒霹批披劈琵毗"],["c640","艪艫艬艭艱艵艶艷艸艻艼芀芁芃芅芆芇芉芌芐芓芔芕芖芚芛芞芠芢芣芧芲芵芶芺芻芼芿苀苂苃苅苆苉苐苖苙苚苝苢苧苨苩苪苬苭苮苰苲苳苵苶苸"],["c680","苺苼",4,"茊茋茍茐茒茓茖茘茙茝",9,"茩茪茮茰茲茷茻茽啤脾疲皮匹痞僻屁譬篇偏片骗飘漂瓢票撇瞥拼频贫品聘乒坪苹萍平凭瓶评屏坡泼颇婆破魄迫粕剖扑铺仆莆葡菩蒲埔朴圃普浦谱曝瀑期欺栖戚妻七凄漆柒沏其棋奇歧畦崎脐齐旗祈祁骑起岂乞企启契砌器气迄弃汽泣讫掐"],["c740","茾茿荁荂荄荅荈荊",4,"荓荕",4,"荝荢荰",6,"荹荺荾",6,"莇莈莊莋莌莍莏莐莑莔莕莖莗莙莚莝莟莡",6,"莬莭莮"],["c780","莯莵莻莾莿菂菃菄菆菈菉菋菍菎菐菑菒菓菕菗菙菚菛菞菢菣菤菦菧菨菫菬菭恰洽牵扦钎铅千迁签仟谦乾黔钱钳前潜遣浅谴堑嵌欠歉枪呛腔羌墙蔷强抢橇锹敲悄桥瞧乔侨巧鞘撬翘峭俏窍切茄且怯窃钦侵亲秦琴勤芹擒禽寝沁青轻氢倾卿清擎晴氰情顷请庆琼穷秋丘邱球求囚酋泅趋区蛆曲躯屈驱渠"],["c840","菮華菳",4,"菺菻菼菾菿萀萂萅萇萈萉萊萐萒",5,"萙萚萛萞",5,"萩",7,"萲",5,"萹萺萻萾",7,"葇葈葉"],["c880","葊",6,"葒",4,"葘葝葞葟葠葢葤",4,"葪葮葯葰葲葴葷葹葻葼取娶龋趣去圈颧权醛泉全痊拳犬券劝缺炔瘸却鹊榷确雀裙群然燃冉染瓤壤攘嚷让饶扰绕惹热壬仁人忍韧任认刃妊纫扔仍日戎茸蓉荣融熔溶容绒冗揉柔肉茹蠕儒孺如辱乳汝入褥软阮蕊瑞锐闰润若弱撒洒萨腮鳃塞赛三叁"],["c940","葽",4,"蒃蒄蒅蒆蒊蒍蒏",7,"蒘蒚蒛蒝蒞蒟蒠蒢",12,"蒰蒱蒳蒵蒶蒷蒻蒼蒾蓀蓂蓃蓅蓆蓇蓈蓋蓌蓎蓏蓒蓔蓕蓗"],["c980","蓘",4,"蓞蓡蓢蓤蓧",4,"蓭蓮蓯蓱",10,"蓽蓾蔀蔁蔂伞散桑嗓丧搔骚扫嫂瑟色涩森僧莎砂杀刹沙纱傻啥煞筛晒珊苫杉山删煽衫闪陕擅赡膳善汕扇缮墒伤商赏晌上尚裳梢捎稍烧芍勺韶少哨邵绍奢赊蛇舌舍赦摄射慑涉社设砷申呻伸身深娠绅神沈审婶甚肾慎渗声生甥牲升绳"],["ca40","蔃",8,"蔍蔎蔏蔐蔒蔔蔕蔖蔘蔙蔛蔜蔝蔞蔠蔢",8,"蔭",9,"蔾",4,"蕄蕅蕆蕇蕋",10],["ca80","蕗蕘蕚蕛蕜蕝蕟",4,"蕥蕦蕧蕩",8,"蕳蕵蕶蕷蕸蕼蕽蕿薀薁省盛剩胜圣师失狮施湿诗尸虱十石拾时什食蚀实识史矢使屎驶始式示士世柿事拭誓逝势是嗜噬适仕侍释饰氏市恃室视试收手首守寿授售受瘦兽蔬枢梳殊抒输叔舒淑疏书赎孰熟薯暑曙署蜀黍鼠属术述树束戍竖墅庶数漱"],["cb40","薂薃薆薈",6,"薐",10,"薝",6,"薥薦薧薩薫薬薭薱",5,"薸薺",6,"藂",6,"藊",4,"藑藒"],["cb80","藔藖",5,"藝",6,"藥藦藧藨藪",14,"恕刷耍摔衰甩帅栓拴霜双爽谁水睡税吮瞬顺舜说硕朔烁斯撕嘶思私司丝死肆寺嗣四伺似饲巳松耸怂颂送宋讼诵搜艘擞嗽苏酥俗素速粟僳塑溯宿诉肃酸蒜算虽隋随绥髓碎岁穗遂隧祟孙损笋蓑梭唆缩琐索锁所塌他它她塔"],["cc40","藹藺藼藽藾蘀",4,"蘆",10,"蘒蘓蘔蘕蘗",15,"蘨蘪",13,"蘹蘺蘻蘽蘾蘿虀"],["cc80","虁",11,"虒虓處",4,"虛虜虝號虠虡虣",7,"獭挞蹋踏胎苔抬台泰酞太态汰坍摊贪瘫滩坛檀痰潭谭谈坦毯袒碳探叹炭汤塘搪堂棠膛唐糖倘躺淌趟烫掏涛滔绦萄桃逃淘陶讨套特藤腾疼誊梯剔踢锑提题蹄啼体替嚏惕涕剃屉天添填田甜恬舔腆挑条迢眺跳贴铁帖厅听烃"],["cd40","虭虯虰虲",6,"蚃",6,"蚎",4,"蚔蚖",5,"蚞",4,"蚥蚦蚫蚭蚮蚲蚳蚷蚸蚹蚻",4,"蛁蛂蛃蛅蛈蛌蛍蛒蛓蛕蛖蛗蛚蛜"],["cd80","蛝蛠蛡蛢蛣蛥蛦蛧蛨蛪蛫蛬蛯蛵蛶蛷蛺蛻蛼蛽蛿蜁蜄蜅蜆蜋蜌蜎蜏蜐蜑蜔蜖汀廷停亭庭挺艇通桐酮瞳同铜彤童桶捅筒统痛偷投头透凸秃突图徒途涂屠土吐兔湍团推颓腿蜕褪退吞屯臀拖托脱鸵陀驮驼椭妥拓唾挖哇蛙洼娃瓦袜歪外豌弯湾玩顽丸烷完碗挽晚皖惋宛婉万腕汪王亡枉网往旺望忘妄威"],["ce40","蜙蜛蜝蜟蜠蜤蜦蜧蜨蜪蜫蜬蜭蜯蜰蜲蜳蜵蜶蜸蜹蜺蜼蜽蝀",6,"蝊蝋蝍蝏蝐蝑蝒蝔蝕蝖蝘蝚",5,"蝡蝢蝦",7,"蝯蝱蝲蝳蝵"],["ce80","蝷蝸蝹蝺蝿螀螁螄螆螇螉螊螌螎",4,"螔螕螖螘",6,"螠",4,"巍微危韦违桅围唯惟为潍维苇萎委伟伪尾纬未蔚味畏胃喂魏位渭谓尉慰卫瘟温蚊文闻纹吻稳紊问嗡翁瓮挝蜗涡窝我斡卧握沃巫呜钨乌污诬屋无芜梧吾吴毋武五捂午舞伍侮坞戊雾晤物勿务悟误昔熙析西硒矽晰嘻吸锡牺"],["cf40","螥螦螧螩螪螮螰螱螲螴螶螷螸螹螻螼螾螿蟁",4,"蟇蟈蟉蟌",4,"蟔",6,"蟜蟝蟞蟟蟡蟢蟣蟤蟦蟧蟨蟩蟫蟬蟭蟯",9],["cf80","蟺蟻蟼蟽蟿蠀蠁蠂蠄",5,"蠋",7,"蠔蠗蠘蠙蠚蠜",4,"蠣稀息希悉膝夕惜熄烯溪汐犀檄袭席习媳喜铣洗系隙戏细瞎虾匣霞辖暇峡侠狭下厦夏吓掀锨先仙鲜纤咸贤衔舷闲涎弦嫌显险现献县腺馅羡宪陷限线相厢镶香箱襄湘乡翔祥详想响享项巷橡像向象萧硝霄削哮嚣销消宵淆晓"],["d040","蠤",13,"蠳",5,"蠺蠻蠽蠾蠿衁衂衃衆",5,"衎",5,"衕衖衘衚",6,"衦衧衪衭衯衱衳衴衵衶衸衹衺"],["d080","衻衼袀袃袆袇袉袊袌袎袏袐袑袓袔袕袗",4,"袝",4,"袣袥",5,"小孝校肖啸笑效楔些歇蝎鞋协挟携邪斜胁谐写械卸蟹懈泄泻谢屑薪芯锌欣辛新忻心信衅星腥猩惺兴刑型形邢行醒幸杏性姓兄凶胸匈汹雄熊休修羞朽嗅锈秀袖绣墟戌需虚嘘须徐许蓄酗叙旭序畜恤絮婿绪续轩喧宣悬旋玄"],["d140","袬袮袯袰袲",4,"袸袹袺袻袽袾袿裀裃裄裇裈裊裋裌裍裏裐裑裓裖裗裚",4,"裠裡裦裧裩",6,"裲裵裶裷裺裻製裿褀褁褃",5],["d180","褉褋",4,"褑褔",4,"褜",4,"褢褣褤褦褧褨褩褬褭褮褯褱褲褳褵褷选癣眩绚靴薛学穴雪血勋熏循旬询寻驯巡殉汛训讯逊迅压押鸦鸭呀丫芽牙蚜崖衙涯雅哑亚讶焉咽阉烟淹盐严研蜒岩延言颜阎炎沿奄掩眼衍演艳堰燕厌砚雁唁彦焰宴谚验殃央鸯秧杨扬佯疡羊洋阳氧仰痒养样漾邀腰妖瑶"],["d240","褸",8,"襂襃襅",24,"襠",5,"襧",19,"襼"],["d280","襽襾覀覂覄覅覇",26,"摇尧遥窑谣姚咬舀药要耀椰噎耶爷野冶也页掖业叶曳腋夜液一壹医揖铱依伊衣颐夷遗移仪胰疑沂宜姨彝椅蚁倚已乙矣以艺抑易邑屹亿役臆逸肄疫亦裔意毅忆义益溢诣议谊译异翼翌绎茵荫因殷音阴姻吟银淫寅饮尹引隐"],["d340","覢",30,"觃觍觓觔觕觗觘觙觛觝觟觠觡觢觤觧觨觩觪觬觭觮觰觱觲觴",6],["d380","觻",4,"訁",5,"計",21,"印英樱婴鹰应缨莹萤营荧蝇迎赢盈影颖硬映哟拥佣臃痈庸雍踊蛹咏泳涌永恿勇用幽优悠忧尤由邮铀犹油游酉有友右佑釉诱又幼迂淤于盂榆虞愚舆余俞逾鱼愉渝渔隅予娱雨与屿禹宇语羽玉域芋郁吁遇喻峪御愈欲狱育誉"],["d440","訞",31,"訿",8,"詉",21],["d480","詟",25,"詺",6,"浴寓裕预豫驭鸳渊冤元垣袁原援辕园员圆猿源缘远苑愿怨院曰约越跃钥岳粤月悦阅耘云郧匀陨允运蕴酝晕韵孕匝砸杂栽哉灾宰载再在咱攒暂赞赃脏葬遭糟凿藻枣早澡蚤躁噪造皂灶燥责择则泽贼怎增憎曾赠扎喳渣札轧"],["d540","誁",7,"誋",7,"誔",46],["d580","諃",32,"铡闸眨栅榨咋乍炸诈摘斋宅窄债寨瞻毡詹粘沾盏斩辗崭展蘸栈占战站湛绽樟章彰漳张掌涨杖丈帐账仗胀瘴障招昭找沼赵照罩兆肇召遮折哲蛰辙者锗蔗这浙珍斟真甄砧臻贞针侦枕疹诊震振镇阵蒸挣睁征狰争怔整拯正政"],["d640","諤",34,"謈",27],["d680","謤謥謧",30,"帧症郑证芝枝支吱蜘知肢脂汁之织职直植殖执值侄址指止趾只旨纸志挚掷至致置帜峙制智秩稚质炙痔滞治窒中盅忠钟衷终种肿重仲众舟周州洲诌粥轴肘帚咒皱宙昼骤珠株蛛朱猪诸诛逐竹烛煮拄瞩嘱主著柱助蛀贮铸筑"],["d740","譆",31,"譧",4,"譭",25],["d780","讇",24,"讬讱讻诇诐诪谉谞住注祝驻抓爪拽专砖转撰赚篆桩庄装妆撞壮状椎锥追赘坠缀谆准捉拙卓桌琢茁酌啄着灼浊兹咨资姿滋淄孜紫仔籽滓子自渍字鬃棕踪宗综总纵邹走奏揍租足卒族祖诅阻组钻纂嘴醉最罪尊遵昨左佐柞做作坐座"],["d840","谸",8,"豂豃豄豅豈豊豋豍",7,"豖豗豘豙豛",5,"豣",6,"豬",6,"豴豵豶豷豻",6,"貃貄貆貇"],["d880","貈貋貍",6,"貕貖貗貙",20,"亍丌兀丐廿卅丕亘丞鬲孬噩丨禺丿匕乇夭爻卮氐囟胤馗毓睾鼗丶亟鼐乜乩亓芈孛啬嘏仄厍厝厣厥厮靥赝匚叵匦匮匾赜卦卣刂刈刎刭刳刿剀剌剞剡剜蒯剽劂劁劐劓冂罔亻仃仉仂仨仡仫仞伛仳伢佤仵伥伧伉伫佞佧攸佚佝"],["d940","貮",62],["d980","賭",32,"佟佗伲伽佶佴侑侉侃侏佾佻侪佼侬侔俦俨俪俅俚俣俜俑俟俸倩偌俳倬倏倮倭俾倜倌倥倨偾偃偕偈偎偬偻傥傧傩傺僖儆僭僬僦僮儇儋仝氽佘佥俎龠汆籴兮巽黉馘冁夔勹匍訇匐凫夙兕亠兖亳衮袤亵脔裒禀嬴蠃羸冫冱冽冼"],["da40","贎",14,"贠赑赒赗赟赥赨赩赪赬赮赯赱赲赸",8,"趂趃趆趇趈趉趌",4,"趒趓趕",9,"趠趡"],["da80","趢趤",12,"趲趶趷趹趻趽跀跁跂跅跇跈跉跊跍跐跒跓跔凇冖冢冥讠讦讧讪讴讵讷诂诃诋诏诎诒诓诔诖诘诙诜诟诠诤诨诩诮诰诳诶诹诼诿谀谂谄谇谌谏谑谒谔谕谖谙谛谘谝谟谠谡谥谧谪谫谮谯谲谳谵谶卩卺阝阢阡阱阪阽阼陂陉陔陟陧陬陲陴隈隍隗隰邗邛邝邙邬邡邴邳邶邺"],["db40","跕跘跙跜跠跡跢跥跦跧跩跭跮跰跱跲跴跶跼跾",6,"踆踇踈踋踍踎踐踑踒踓踕",7,"踠踡踤",4,"踫踭踰踲踳踴踶踷踸踻踼踾"],["db80","踿蹃蹅蹆蹌",4,"蹓",5,"蹚",11,"蹧蹨蹪蹫蹮蹱邸邰郏郅邾郐郄郇郓郦郢郜郗郛郫郯郾鄄鄢鄞鄣鄱鄯鄹酃酆刍奂劢劬劭劾哿勐勖勰叟燮矍廴凵凼鬯厶弁畚巯坌垩垡塾墼壅壑圩圬圪圳圹圮圯坜圻坂坩垅坫垆坼坻坨坭坶坳垭垤垌垲埏垧垴垓垠埕埘埚埙埒垸埴埯埸埤埝"],["dc40","蹳蹵蹷",4,"蹽蹾躀躂躃躄躆躈",6,"躑躒躓躕",6,"躝躟",11,"躭躮躰躱躳",6,"躻",7],["dc80","軃",10,"軏",21,"堋堍埽埭堀堞堙塄堠塥塬墁墉墚墀馨鼙懿艹艽艿芏芊芨芄芎芑芗芙芫芸芾芰苈苊苣芘芷芮苋苌苁芩芴芡芪芟苄苎芤苡茉苷苤茏茇苜苴苒苘茌苻苓茑茚茆茔茕苠苕茜荑荛荜茈莒茼茴茱莛荞茯荏荇荃荟荀茗荠茭茺茳荦荥"],["dd40","軥",62],["dd80","輤",32,"荨茛荩荬荪荭荮莰荸莳莴莠莪莓莜莅荼莶莩荽莸荻莘莞莨莺莼菁萁菥菘堇萘萋菝菽菖萜萸萑萆菔菟萏萃菸菹菪菅菀萦菰菡葜葑葚葙葳蒇蒈葺蒉葸萼葆葩葶蒌蒎萱葭蓁蓍蓐蓦蒽蓓蓊蒿蒺蓠蒡蒹蒴蒗蓥蓣蔌甍蔸蓰蔹蔟蔺"],["de40","轅",32,"轪辀辌辒辝辠辡辢辤辥辦辧辪辬辭辮辯農辳辴辵辷辸辺辻込辿迀迃迆"],["de80","迉",4,"迏迒迖迗迚迠迡迣迧迬迯迱迲迴迵迶迺迻迼迾迿逇逈逌逎逓逕逘蕖蔻蓿蓼蕙蕈蕨蕤蕞蕺瞢蕃蕲蕻薤薨薇薏蕹薮薜薅薹薷薰藓藁藜藿蘧蘅蘩蘖蘼廾弈夼奁耷奕奚奘匏尢尥尬尴扌扪抟抻拊拚拗拮挢拶挹捋捃掭揶捱捺掎掴捭掬掊捩掮掼揲揸揠揿揄揞揎摒揆掾摅摁搋搛搠搌搦搡摞撄摭撖"],["df40","這逜連逤逥逧",5,"逰",4,"逷逹逺逽逿遀遃遅遆遈",4,"過達違遖遙遚遜",5,"遤遦遧適遪遫遬遯",4,"遶",6,"遾邁"],["df80","還邅邆邇邉邊邌",4,"邒邔邖邘邚邜邞邟邠邤邥邧邨邩邫邭邲邷邼邽邿郀摺撷撸撙撺擀擐擗擤擢攉攥攮弋忒甙弑卟叱叽叩叨叻吒吖吆呋呒呓呔呖呃吡呗呙吣吲咂咔呷呱呤咚咛咄呶呦咝哐咭哂咴哒咧咦哓哔呲咣哕咻咿哌哙哚哜咩咪咤哝哏哞唛哧唠哽唔哳唢唣唏唑唧唪啧喏喵啉啭啁啕唿啐唼"],["e040","郂郃郆郈郉郋郌郍郒郔郕郖郘郙郚郞郟郠郣郤郥郩郪郬郮郰郱郲郳郵郶郷郹郺郻郼郿鄀鄁鄃鄅",19,"鄚鄛鄜"],["e080","鄝鄟鄠鄡鄤",10,"鄰鄲",6,"鄺",8,"酄唷啖啵啶啷唳唰啜喋嗒喃喱喹喈喁喟啾嗖喑啻嗟喽喾喔喙嗪嗷嗉嘟嗑嗫嗬嗔嗦嗝嗄嗯嗥嗲嗳嗌嗍嗨嗵嗤辔嘞嘈嘌嘁嘤嘣嗾嘀嘧嘭噘嘹噗嘬噍噢噙噜噌噔嚆噤噱噫噻噼嚅嚓嚯囔囗囝囡囵囫囹囿圄圊圉圜帏帙帔帑帱帻帼"],["e140","酅酇酈酑酓酔酕酖酘酙酛酜酟酠酦酧酨酫酭酳酺酻酼醀",4,"醆醈醊醎醏醓",6,"醜",5,"醤",5,"醫醬醰醱醲醳醶醷醸醹醻"],["e180","醼",10,"釈釋釐釒",9,"針",8,"帷幄幔幛幞幡岌屺岍岐岖岈岘岙岑岚岜岵岢岽岬岫岱岣峁岷峄峒峤峋峥崂崃崧崦崮崤崞崆崛嵘崾崴崽嵬嵛嵯嵝嵫嵋嵊嵩嵴嶂嶙嶝豳嶷巅彳彷徂徇徉後徕徙徜徨徭徵徼衢彡犭犰犴犷犸狃狁狎狍狒狨狯狩狲狴狷猁狳猃狺"],["e240","釦",62],["e280","鈥",32,"狻猗猓猡猊猞猝猕猢猹猥猬猸猱獐獍獗獠獬獯獾舛夥飧夤夂饣饧",5,"饴饷饽馀馄馇馊馍馐馑馓馔馕庀庑庋庖庥庠庹庵庾庳赓廒廑廛廨廪膺忄忉忖忏怃忮怄忡忤忾怅怆忪忭忸怙怵怦怛怏怍怩怫怊怿怡恸恹恻恺恂"],["e340","鉆",45,"鉵",16],["e380","銆",7,"銏",24,"恪恽悖悚悭悝悃悒悌悛惬悻悱惝惘惆惚悴愠愦愕愣惴愀愎愫慊慵憬憔憧憷懔懵忝隳闩闫闱闳闵闶闼闾阃阄阆阈阊阋阌阍阏阒阕阖阗阙阚丬爿戕氵汔汜汊沣沅沐沔沌汨汩汴汶沆沩泐泔沭泷泸泱泗沲泠泖泺泫泮沱泓泯泾"],["e440","銨",5,"銯",24,"鋉",31],["e480","鋩",32,"洹洧洌浃浈洇洄洙洎洫浍洮洵洚浏浒浔洳涑浯涞涠浞涓涔浜浠浼浣渚淇淅淞渎涿淠渑淦淝淙渖涫渌涮渫湮湎湫溲湟溆湓湔渲渥湄滟溱溘滠漭滢溥溧溽溻溷滗溴滏溏滂溟潢潆潇漤漕滹漯漶潋潴漪漉漩澉澍澌潸潲潼潺濑"],["e540","錊",51,"錿",10],["e580","鍊",31,"鍫濉澧澹澶濂濡濮濞濠濯瀚瀣瀛瀹瀵灏灞宀宄宕宓宥宸甯骞搴寤寮褰寰蹇謇辶迓迕迥迮迤迩迦迳迨逅逄逋逦逑逍逖逡逵逶逭逯遄遑遒遐遨遘遢遛暹遴遽邂邈邃邋彐彗彖彘尻咫屐屙孱屣屦羼弪弩弭艴弼鬻屮妁妃妍妩妪妣"],["e640","鍬",34,"鎐",27],["e680","鎬",29,"鏋鏌鏍妗姊妫妞妤姒妲妯姗妾娅娆姝娈姣姘姹娌娉娲娴娑娣娓婀婧婊婕娼婢婵胬媪媛婷婺媾嫫媲嫒嫔媸嫠嫣嫱嫖嫦嫘嫜嬉嬗嬖嬲嬷孀尕尜孚孥孳孑孓孢驵驷驸驺驿驽骀骁骅骈骊骐骒骓骖骘骛骜骝骟骠骢骣骥骧纟纡纣纥纨纩"],["e740","鏎",7,"鏗",54],["e780","鐎",32,"纭纰纾绀绁绂绉绋绌绐绔绗绛绠绡绨绫绮绯绱绲缍绶绺绻绾缁缂缃缇缈缋缌缏缑缒缗缙缜缛缟缡",6,"缪缫缬缭缯",4,"缵幺畿巛甾邕玎玑玮玢玟珏珂珑玷玳珀珉珈珥珙顼琊珩珧珞玺珲琏琪瑛琦琥琨琰琮琬"],["e840","鐯",14,"鐿",43,"鑬鑭鑮鑯"],["e880","鑰",20,"钑钖钘铇铏铓铔铚铦铻锜锠琛琚瑁瑜瑗瑕瑙瑷瑭瑾璜璎璀璁璇璋璞璨璩璐璧瓒璺韪韫韬杌杓杞杈杩枥枇杪杳枘枧杵枨枞枭枋杷杼柰栉柘栊柩枰栌柙枵柚枳柝栀柃枸柢栎柁柽栲栳桠桡桎桢桄桤梃栝桕桦桁桧桀栾桊桉栩梵梏桴桷梓桫棂楮棼椟椠棹"],["e940","锧锳锽镃镈镋镕镚镠镮镴镵長",7,"門",42],["e980","閫",32,"椤棰椋椁楗棣椐楱椹楠楂楝榄楫榀榘楸椴槌榇榈槎榉楦楣楹榛榧榻榫榭槔榱槁槊槟榕槠榍槿樯槭樗樘橥槲橄樾檠橐橛樵檎橹樽樨橘橼檑檐檩檗檫猷獒殁殂殇殄殒殓殍殚殛殡殪轫轭轱轲轳轵轶轸轷轹轺轼轾辁辂辄辇辋"],["ea40","闌",27,"闬闿阇阓阘阛阞阠阣",6,"阫阬阭阯阰阷阸阹阺阾陁陃陊陎陏陑陒陓陖陗"],["ea80","陘陙陚陜陝陞陠陣陥陦陫陭",4,"陳陸",12,"隇隉隊辍辎辏辘辚軎戋戗戛戟戢戡戥戤戬臧瓯瓴瓿甏甑甓攴旮旯旰昊昙杲昃昕昀炅曷昝昴昱昶昵耆晟晔晁晏晖晡晗晷暄暌暧暝暾曛曜曦曩贲贳贶贻贽赀赅赆赈赉赇赍赕赙觇觊觋觌觎觏觐觑牮犟牝牦牯牾牿犄犋犍犏犒挈挲掰"],["eb40","隌階隑隒隓隕隖隚際隝",9,"隨",7,"隱隲隴隵隷隸隺隻隿雂雃雈雊雋雐雑雓雔雖",9,"雡",6,"雫"],["eb80","雬雭雮雰雱雲雴雵雸雺電雼雽雿霂霃霅霊霋霌霐霑霒霔霕霗",4,"霝霟霠搿擘耄毪毳毽毵毹氅氇氆氍氕氘氙氚氡氩氤氪氲攵敕敫牍牒牖爰虢刖肟肜肓肼朊肽肱肫肭肴肷胧胨胩胪胛胂胄胙胍胗朐胝胫胱胴胭脍脎胲胼朕脒豚脶脞脬脘脲腈腌腓腴腙腚腱腠腩腼腽腭腧塍媵膈膂膑滕膣膪臌朦臊膻"],["ec40","霡",8,"霫霬霮霯霱霳",4,"霺霻霼霽霿",18,"靔靕靗靘靚靜靝靟靣靤靦靧靨靪",7],["ec80","靲靵靷",4,"靽",7,"鞆",4,"鞌鞎鞏鞐鞓鞕鞖鞗鞙",4,"臁膦欤欷欹歃歆歙飑飒飓飕飙飚殳彀毂觳斐齑斓於旆旄旃旌旎旒旖炀炜炖炝炻烀炷炫炱烨烊焐焓焖焯焱煳煜煨煅煲煊煸煺熘熳熵熨熠燠燔燧燹爝爨灬焘煦熹戾戽扃扈扉礻祀祆祉祛祜祓祚祢祗祠祯祧祺禅禊禚禧禳忑忐"],["ed40","鞞鞟鞡鞢鞤",6,"鞬鞮鞰鞱鞳鞵",46],["ed80","韤韥韨韮",4,"韴韷",23,"怼恝恚恧恁恙恣悫愆愍慝憩憝懋懑戆肀聿沓泶淼矶矸砀砉砗砘砑斫砭砜砝砹砺砻砟砼砥砬砣砩硎硭硖硗砦硐硇硌硪碛碓碚碇碜碡碣碲碹碥磔磙磉磬磲礅磴礓礤礞礴龛黹黻黼盱眄眍盹眇眈眚眢眙眭眦眵眸睐睑睇睃睚睨"],["ee40","頏",62],["ee80","顎",32,"睢睥睿瞍睽瞀瞌瞑瞟瞠瞰瞵瞽町畀畎畋畈畛畲畹疃罘罡罟詈罨罴罱罹羁罾盍盥蠲钅钆钇钋钊钌钍钏钐钔钗钕钚钛钜钣钤钫钪钭钬钯钰钲钴钶",4,"钼钽钿铄铈",6,"铐铑铒铕铖铗铙铘铛铞铟铠铢铤铥铧铨铪"],["ef40","顯",5,"颋颎颒颕颙颣風",37,"飏飐飔飖飗飛飜飝飠",4],["ef80","飥飦飩",30,"铩铫铮铯铳铴铵铷铹铼铽铿锃锂锆锇锉锊锍锎锏锒",4,"锘锛锝锞锟锢锪锫锩锬锱锲锴锶锷锸锼锾锿镂锵镄镅镆镉镌镎镏镒镓镔镖镗镘镙镛镞镟镝镡镢镤",8,"镯镱镲镳锺矧矬雉秕秭秣秫稆嵇稃稂稞稔"],["f040","餈",4,"餎餏餑",28,"餯",26],["f080","饊",9,"饖",12,"饤饦饳饸饹饻饾馂馃馉稹稷穑黏馥穰皈皎皓皙皤瓞瓠甬鸠鸢鸨",4,"鸲鸱鸶鸸鸷鸹鸺鸾鹁鹂鹄鹆鹇鹈鹉鹋鹌鹎鹑鹕鹗鹚鹛鹜鹞鹣鹦",6,"鹱鹭鹳疒疔疖疠疝疬疣疳疴疸痄疱疰痃痂痖痍痣痨痦痤痫痧瘃痱痼痿瘐瘀瘅瘌瘗瘊瘥瘘瘕瘙"],["f140","馌馎馚",10,"馦馧馩",47],["f180","駙",32,"瘛瘼瘢瘠癀瘭瘰瘿瘵癃瘾瘳癍癞癔癜癖癫癯翊竦穸穹窀窆窈窕窦窠窬窨窭窳衤衩衲衽衿袂袢裆袷袼裉裢裎裣裥裱褚裼裨裾裰褡褙褓褛褊褴褫褶襁襦襻疋胥皲皴矜耒耔耖耜耠耢耥耦耧耩耨耱耋耵聃聆聍聒聩聱覃顸颀颃"],["f240","駺",62],["f280","騹",32,"颉颌颍颏颔颚颛颞颟颡颢颥颦虍虔虬虮虿虺虼虻蚨蚍蚋蚬蚝蚧蚣蚪蚓蚩蚶蛄蚵蛎蚰蚺蚱蚯蛉蛏蚴蛩蛱蛲蛭蛳蛐蜓蛞蛴蛟蛘蛑蜃蜇蛸蜈蜊蜍蜉蜣蜻蜞蜥蜮蜚蜾蝈蜴蜱蜩蜷蜿螂蜢蝽蝾蝻蝠蝰蝌蝮螋蝓蝣蝼蝤蝙蝥螓螯螨蟒"],["f340","驚",17,"驲骃骉骍骎骔骕骙骦骩",6,"骲骳骴骵骹骻骽骾骿髃髄髆",4,"髍髎髏髐髒體髕髖髗髙髚髛髜"],["f380","髝髞髠髢髣髤髥髧髨髩髪髬髮髰",8,"髺髼",6,"鬄鬅鬆蟆螈螅螭螗螃螫蟥螬螵螳蟋蟓螽蟑蟀蟊蟛蟪蟠蟮蠖蠓蟾蠊蠛蠡蠹蠼缶罂罄罅舐竺竽笈笃笄笕笊笫笏筇笸笪笙笮笱笠笥笤笳笾笞筘筚筅筵筌筝筠筮筻筢筲筱箐箦箧箸箬箝箨箅箪箜箢箫箴篑篁篌篝篚篥篦篪簌篾篼簏簖簋"],["f440","鬇鬉",5,"鬐鬑鬒鬔",10,"鬠鬡鬢鬤",10,"鬰鬱鬳",7,"鬽鬾鬿魀魆魊魋魌魎魐魒魓魕",5],["f480","魛",32,"簟簪簦簸籁籀臾舁舂舄臬衄舡舢舣舭舯舨舫舸舻舳舴舾艄艉艋艏艚艟艨衾袅袈裘裟襞羝羟羧羯羰羲籼敉粑粝粜粞粢粲粼粽糁糇糌糍糈糅糗糨艮暨羿翎翕翥翡翦翩翮翳糸絷綦綮繇纛麸麴赳趄趔趑趱赧赭豇豉酊酐酎酏酤"],["f540","魼",62],["f580","鮻",32,"酢酡酰酩酯酽酾酲酴酹醌醅醐醍醑醢醣醪醭醮醯醵醴醺豕鹾趸跫踅蹙蹩趵趿趼趺跄跖跗跚跞跎跏跛跆跬跷跸跣跹跻跤踉跽踔踝踟踬踮踣踯踺蹀踹踵踽踱蹉蹁蹂蹑蹒蹊蹰蹶蹼蹯蹴躅躏躔躐躜躞豸貂貊貅貘貔斛觖觞觚觜"],["f640","鯜",62],["f680","鰛",32,"觥觫觯訾謦靓雩雳雯霆霁霈霏霎霪霭霰霾龀龃龅",5,"龌黾鼋鼍隹隼隽雎雒瞿雠銎銮鋈錾鍪鏊鎏鐾鑫鱿鲂鲅鲆鲇鲈稣鲋鲎鲐鲑鲒鲔鲕鲚鲛鲞",5,"鲥",4,"鲫鲭鲮鲰",7,"鲺鲻鲼鲽鳄鳅鳆鳇鳊鳋"],["f740","鰼",62],["f780","鱻鱽鱾鲀鲃鲄鲉鲊鲌鲏鲓鲖鲗鲘鲙鲝鲪鲬鲯鲹鲾",4,"鳈鳉鳑鳒鳚鳛鳠鳡鳌",4,"鳓鳔鳕鳗鳘鳙鳜鳝鳟鳢靼鞅鞑鞒鞔鞯鞫鞣鞲鞴骱骰骷鹘骶骺骼髁髀髅髂髋髌髑魅魃魇魉魈魍魑飨餍餮饕饔髟髡髦髯髫髻髭髹鬈鬏鬓鬟鬣麽麾縻麂麇麈麋麒鏖麝麟黛黜黝黠黟黢黩黧黥黪黯鼢鼬鼯鼹鼷鼽鼾齄"],["f840","鳣",62],["f880","鴢",32],["f940","鵃",62],["f980","鶂",32],["fa40","鶣",62],["fa80","鷢",32],["fb40","鸃",27,"鸤鸧鸮鸰鸴鸻鸼鹀鹍鹐鹒鹓鹔鹖鹙鹝鹟鹠鹡鹢鹥鹮鹯鹲鹴",9,"麀"],["fb80","麁麃麄麅麆麉麊麌",5,"麔",8,"麞麠",5,"麧麨麩麪"],["fc40","麫",8,"麵麶麷麹麺麼麿",4,"黅黆黇黈黊黋黌黐黒黓黕黖黗黙黚點黡黣黤黦黨黫黬黭黮黰",8,"黺黽黿",6],["fc80","鼆",4,"鼌鼏鼑鼒鼔鼕鼖鼘鼚",5,"鼡鼣",8,"鼭鼮鼰鼱"],["fd40","鼲",4,"鼸鼺鼼鼿",4,"齅",10,"齒",38],["fd80","齹",5,"龁龂龍",11,"龜龝龞龡",4,"郎凉秊裏隣"],["fe40","兀嗀﨎﨏﨑﨓﨔礼﨟蘒﨡﨣﨤﨧﨨﨩"]],vr=[["a140","",62],["a180","",32],["a240","",62],["a280","",32],["a2ab","",5],["a2e3","€"],["a2ef",""],["a2fd",""],["a340","",62],["a380","",31,"　"],["a440","",62],["a480","",32],["a4f4","",10],["a540","",62],["a580","",32],["a5f7","",7],["a640","",62],["a680","",32],["a6b9","",7],["a6d9","",6],["a6ec",""],["a6f3",""],["a6f6","",8],["a740","",62],["a780","",32],["a7c2","",14],["a7f2","",12],["a896","",10],["a8bc","ḿ"],["a8bf","ǹ"],["a8c1",""],["a8ea","",20],["a958",""],["a95b",""],["a95d",""],["a989","〾⿰",11],["a997","",12],["a9f0","",14],["aaa1","",93],["aba1","",93],["aca1","",93],["ada1","",93],["aea1","",93],["afa1","",93],["d7fa","",4],["f8a1","",93],["f9a1","",93],["faa1","",93],["fba1","",93],["fca1","",93],["fda1","",93],["fe50","⺁⺄㑳㑇⺈⺋㖞㘚㘎⺌⺗㥮㤘㧏㧟㩳㧐㭎㱮㳠⺧⺪䁖䅟⺮䌷⺳⺶⺷䎱䎬⺻䏝䓖䙡䙌"],["fe80","䜣䜩䝼䞍⻊䥇䥺䥽䦂䦃䦅䦆䦟䦛䦷䦶䲣䲟䲠䲡䱷䲢䴓",6,"䶮",93],["8135f437",""]],_i=[128,165,169,178,184,216,226,235,238,244,248,251,253,258,276,284,300,325,329,334,364,463,465,467,469,471,473,475,477,506,594,610,712,716,730,930,938,962,970,1026,1104,1106,8209,8215,8218,8222,8231,8241,8244,8246,8252,8365,8452,8454,8458,8471,8482,8556,8570,8596,8602,8713,8720,8722,8726,8731,8737,8740,8742,8748,8751,8760,8766,8777,8781,8787,8802,8808,8816,8854,8858,8870,8896,8979,9322,9372,9548,9588,9616,9622,9634,9652,9662,9672,9676,9680,9702,9735,9738,9793,9795,11906,11909,11913,11917,11928,11944,11947,11951,11956,11960,11964,11979,12284,12292,12312,12319,12330,12351,12436,12447,12535,12543,12586,12842,12850,12964,13200,13215,13218,13253,13263,13267,13270,13384,13428,13727,13839,13851,14617,14703,14801,14816,14964,15183,15471,15585,16471,16736,17208,17325,17330,17374,17623,17997,18018,18212,18218,18301,18318,18760,18811,18814,18820,18823,18844,18848,18872,19576,19620,19738,19887,40870,59244,59336,59367,59413,59417,59423,59431,59437,59443,59452,59460,59478,59493,63789,63866,63894,63976,63986,64016,64018,64021,64025,64034,64037,64042,65074,65093,65107,65112,65127,65132,65375,65510,65536],vi=[0,36,38,45,50,81,89,95,96,100,103,104,105,109,126,133,148,172,175,179,208,306,307,308,309,310,311,312,313,341,428,443,544,545,558,741,742,749,750,805,819,820,7922,7924,7925,7927,7934,7943,7944,7945,7950,8062,8148,8149,8152,8164,8174,8236,8240,8262,8264,8374,8380,8381,8384,8388,8390,8392,8393,8394,8396,8401,8406,8416,8419,8424,8437,8439,8445,8482,8485,8496,8521,8603,8936,8946,9046,9050,9063,9066,9076,9092,9100,9108,9111,9113,9131,9162,9164,9218,9219,11329,11331,11334,11336,11346,11361,11363,11366,11370,11372,11375,11389,11682,11686,11687,11692,11694,11714,11716,11723,11725,11730,11736,11982,11989,12102,12336,12348,12350,12384,12393,12395,12397,12510,12553,12851,12962,12973,13738,13823,13919,13933,14080,14298,14585,14698,15583,15847,16318,16434,16438,16481,16729,17102,17122,17315,17320,17402,17418,17859,17909,17911,17915,17916,17936,17939,17961,18664,18703,18814,18962,19043,33469,33470,33471,33484,33485,33490,33497,33501,33505,33513,33520,33536,33550,37845,37921,37948,38029,38038,38064,38065,38066,38069,38075,38076,38078,39108,39109,39113,39114,39115,39116,39265,39394,189e3],Ci={uChars:_i,gbChars:vi},Ii=[["0","\0",127],["8141","갂갃갅갆갋",4,"갘갞갟갡갢갣갥",6,"갮갲갳갴"],["8161","갵갶갷갺갻갽갾갿걁",9,"걌걎",5,"걕"],["8181","걖걗걙걚걛걝",18,"걲걳걵걶걹걻",4,"겂겇겈겍겎겏겑겒겓겕",6,"겞겢",5,"겫겭겮겱",6,"겺겾겿곀곂곃곅곆곇곉곊곋곍",7,"곖곘",7,"곢곣곥곦곩곫곭곮곲곴곷",4,"곾곿괁괂괃괅괇",4,"괎괐괒괓"],["8241","괔괕괖괗괙괚괛괝괞괟괡",7,"괪괫괮",5],["8261","괶괷괹괺괻괽",6,"굆굈굊",5,"굑굒굓굕굖굗"],["8281","굙",7,"굢굤",7,"굮굯굱굲굷굸굹굺굾궀궃",4,"궊궋궍궎궏궑",10,"궞",5,"궥",17,"궸",7,"귂귃귅귆귇귉",6,"귒귔",7,"귝귞귟귡귢귣귥",18],["8341","귺귻귽귾긂",5,"긊긌긎",5,"긕",7],["8361","긝",18,"긲긳긵긶긹긻긼"],["8381","긽긾긿깂깄깇깈깉깋깏깑깒깓깕깗",4,"깞깢깣깤깦깧깪깫깭깮깯깱",6,"깺깾",5,"꺆",5,"꺍",46,"꺿껁껂껃껅",6,"껎껒",5,"껚껛껝",8],["8441","껦껧껩껪껬껮",5,"껵껶껷껹껺껻껽",8],["8461","꼆꼉꼊꼋꼌꼎꼏꼑",18],["8481","꼤",7,"꼮꼯꼱꼳꼵",6,"꼾꽀꽄꽅꽆꽇꽊",5,"꽑",10,"꽞",5,"꽦",18,"꽺",5,"꾁꾂꾃꾅꾆꾇꾉",6,"꾒꾓꾔꾖",5,"꾝",26,"꾺꾻꾽꾾"],["8541","꾿꿁",5,"꿊꿌꿏",4,"꿕",6,"꿝",4],["8561","꿢",5,"꿪",5,"꿲꿳꿵꿶꿷꿹",6,"뀂뀃"],["8581","뀅",6,"뀍뀎뀏뀑뀒뀓뀕",6,"뀞",9,"뀩",26,"끆끇끉끋끍끏끐끑끒끖끘끚끛끜끞",29,"끾끿낁낂낃낅",6,"낎낐낒",5,"낛낝낞낣낤"],["8641","낥낦낧낪낰낲낶낷낹낺낻낽",6,"냆냊",5,"냒"],["8661","냓냕냖냗냙",6,"냡냢냣냤냦",10],["8681","냱",22,"넊넍넎넏넑넔넕넖넗넚넞",4,"넦넧넩넪넫넭",6,"넶넺",5,"녂녃녅녆녇녉",6,"녒녓녖녗녙녚녛녝녞녟녡",22,"녺녻녽녾녿놁놃",4,"놊놌놎놏놐놑놕놖놗놙놚놛놝"],["8741","놞",9,"놩",15],["8761","놹",18,"뇍뇎뇏뇑뇒뇓뇕"],["8781","뇖",5,"뇞뇠",7,"뇪뇫뇭뇮뇯뇱",7,"뇺뇼뇾",5,"눆눇눉눊눍",6,"눖눘눚",5,"눡",18,"눵",6,"눽",26,"뉙뉚뉛뉝뉞뉟뉡",6,"뉪",4],["8841","뉯",4,"뉶",5,"뉽",6,"늆늇늈늊",4],["8861","늏늒늓늕늖늗늛",4,"늢늤늧늨늩늫늭늮늯늱늲늳늵늶늷"],["8881","늸",15,"닊닋닍닎닏닑닓",4,"닚닜닞닟닠닡닣닧닩닪닰닱닲닶닼닽닾댂댃댅댆댇댉",6,"댒댖",5,"댝",54,"덗덙덚덝덠덡덢덣"],["8941","덦덨덪덬덭덯덲덳덵덶덷덹",6,"뎂뎆",5,"뎍"],["8961","뎎뎏뎑뎒뎓뎕",10,"뎢",5,"뎩뎪뎫뎭"],["8981","뎮",21,"돆돇돉돊돍돏돑돒돓돖돘돚돜돞돟돡돢돣돥돦돧돩",18,"돽",18,"됑",6,"됙됚됛됝됞됟됡",6,"됪됬",7,"됵",15],["8a41","둅",10,"둒둓둕둖둗둙",6,"둢둤둦"],["8a61","둧",4,"둭",18,"뒁뒂"],["8a81","뒃",4,"뒉",19,"뒞",5,"뒥뒦뒧뒩뒪뒫뒭",7,"뒶뒸뒺",5,"듁듂듃듅듆듇듉",6,"듑듒듓듔듖",5,"듞듟듡듢듥듧",4,"듮듰듲",5,"듹",26,"딖딗딙딚딝"],["8b41","딞",5,"딦딫",4,"딲딳딵딶딷딹",6,"땂땆"],["8b61","땇땈땉땊땎땏땑땒땓땕",6,"땞땢",8],["8b81","땫",52,"떢떣떥떦떧떩떬떭떮떯떲떶",4,"떾떿뗁뗂뗃뗅",6,"뗎뗒",5,"뗙",18,"뗭",18],["8c41","똀",15,"똒똓똕똖똗똙",4],["8c61","똞",6,"똦",5,"똭",6,"똵",5],["8c81","똻",12,"뙉",26,"뙥뙦뙧뙩",50,"뚞뚟뚡뚢뚣뚥",5,"뚭뚮뚯뚰뚲",16],["8d41","뛃",16,"뛕",8],["8d61","뛞",17,"뛱뛲뛳뛵뛶뛷뛹뛺"],["8d81","뛻",4,"뜂뜃뜄뜆",33,"뜪뜫뜭뜮뜱",6,"뜺뜼",7,"띅띆띇띉띊띋띍",6,"띖",9,"띡띢띣띥띦띧띩",6,"띲띴띶",5,"띾띿랁랂랃랅",6,"랎랓랔랕랚랛랝랞"],["8e41","랟랡",6,"랪랮",5,"랶랷랹",8],["8e61","럂",4,"럈럊",19],["8e81","럞",13,"럮럯럱럲럳럵",6,"럾렂",4,"렊렋렍렎렏렑",6,"렚렜렞",5,"렦렧렩렪렫렭",6,"렶렺",5,"롁롂롃롅",11,"롒롔",7,"롞롟롡롢롣롥",6,"롮롰롲",5,"롹롺롻롽",7],["8f41","뢅",7,"뢎",17],["8f61","뢠",7,"뢩",6,"뢱뢲뢳뢵뢶뢷뢹",4],["8f81","뢾뢿룂룄룆",5,"룍룎룏룑룒룓룕",7,"룞룠룢",5,"룪룫룭룮룯룱",6,"룺룼룾",5,"뤅",18,"뤙",6,"뤡",26,"뤾뤿륁륂륃륅",6,"륍륎륐륒",5],["9041","륚륛륝륞륟륡",6,"륪륬륮",5,"륶륷륹륺륻륽"],["9061","륾",5,"릆릈릋릌릏",15],["9081","릟",12,"릮릯릱릲릳릵",6,"릾맀맂",5,"맊맋맍맓",4,"맚맜맟맠맢맦맧맩맪맫맭",6,"맶맻",4,"먂",5,"먉",11,"먖",33,"먺먻먽먾먿멁멃멄멅멆"],["9141","멇멊멌멏멐멑멒멖멗멙멚멛멝",6,"멦멪",5],["9161","멲멳멵멶멷멹",9,"몆몈몉몊몋몍",5],["9181","몓",20,"몪몭몮몯몱몳",4,"몺몼몾",5,"뫅뫆뫇뫉",14,"뫚",33,"뫽뫾뫿묁묂묃묅",7,"묎묐묒",5,"묙묚묛묝묞묟묡",6],["9241","묨묪묬",7,"묷묹묺묿",4,"뭆뭈뭊뭋뭌뭎뭑뭒"],["9261","뭓뭕뭖뭗뭙",7,"뭢뭤",7,"뭭",4],["9281","뭲",21,"뮉뮊뮋뮍뮎뮏뮑",18,"뮥뮦뮧뮩뮪뮫뮭",6,"뮵뮶뮸",7,"믁믂믃믅믆믇믉",6,"믑믒믔",35,"믺믻믽믾밁"],["9341","밃",4,"밊밎밐밒밓밙밚밠밡밢밣밦밨밪밫밬밮밯밲밳밵"],["9361","밶밷밹",6,"뱂뱆뱇뱈뱊뱋뱎뱏뱑",8],["9381","뱚뱛뱜뱞",37,"벆벇벉벊벍벏",4,"벖벘벛",4,"벢벣벥벦벩",6,"벲벶",5,"벾벿볁볂볃볅",7,"볎볒볓볔볖볗볙볚볛볝",22,"볷볹볺볻볽"],["9441","볾",5,"봆봈봊",5,"봑봒봓봕",8],["9461","봞",5,"봥",6,"봭",12],["9481","봺",5,"뵁",6,"뵊뵋뵍뵎뵏뵑",6,"뵚",9,"뵥뵦뵧뵩",22,"붂붃붅붆붋",4,"붒붔붖붗붘붛붝",6,"붥",10,"붱",6,"붹",24],["9541","뷒뷓뷖뷗뷙뷚뷛뷝",11,"뷪",5,"뷱"],["9561","뷲뷳뷵뷶뷷뷹",6,"븁븂븄븆",5,"븎븏븑븒븓"],["9581","븕",6,"븞븠",35,"빆빇빉빊빋빍빏",4,"빖빘빜빝빞빟빢빣빥빦빧빩빫",4,"빲빶",4,"빾빿뺁뺂뺃뺅",6,"뺎뺒",5,"뺚",13,"뺩",14],["9641","뺸",23,"뻒뻓"],["9661","뻕뻖뻙",6,"뻡뻢뻦",5,"뻭",8],["9681","뻶",10,"뼂",5,"뼊",13,"뼚뼞",33,"뽂뽃뽅뽆뽇뽉",6,"뽒뽓뽔뽖",44],["9741","뾃",16,"뾕",8],["9761","뾞",17,"뾱",7],["9781","뾹",11,"뿆",5,"뿎뿏뿑뿒뿓뿕",6,"뿝뿞뿠뿢",89,"쀽쀾쀿"],["9841","쁀",16,"쁒",5,"쁙쁚쁛"],["9861","쁝쁞쁟쁡",6,"쁪",15],["9881","쁺",21,"삒삓삕삖삗삙",6,"삢삤삦",5,"삮삱삲삷",4,"삾샂샃샄샆샇샊샋샍샎샏샑",6,"샚샞",5,"샦샧샩샪샫샭",6,"샶샸샺",5,"섁섂섃섅섆섇섉",6,"섑섒섓섔섖",5,"섡섢섥섨섩섪섫섮"],["9941","섲섳섴섵섷섺섻섽섾섿셁",6,"셊셎",5,"셖셗"],["9961","셙셚셛셝",6,"셦셪",5,"셱셲셳셵셶셷셹셺셻"],["9981","셼",8,"솆",5,"솏솑솒솓솕솗",4,"솞솠솢솣솤솦솧솪솫솭솮솯솱",11,"솾",5,"쇅쇆쇇쇉쇊쇋쇍",6,"쇕쇖쇙",6,"쇡쇢쇣쇥쇦쇧쇩",6,"쇲쇴",7,"쇾쇿숁숂숃숅",6,"숎숐숒",5,"숚숛숝숞숡숢숣"],["9a41","숤숥숦숧숪숬숮숰숳숵",16],["9a61","쉆쉇쉉",6,"쉒쉓쉕쉖쉗쉙",6,"쉡쉢쉣쉤쉦"],["9a81","쉧",4,"쉮쉯쉱쉲쉳쉵",6,"쉾슀슂",5,"슊",5,"슑",6,"슙슚슜슞",5,"슦슧슩슪슫슮",5,"슶슸슺",33,"싞싟싡싢싥",5,"싮싰싲싳싴싵싷싺싽싾싿쌁",6,"쌊쌋쌎쌏"],["9b41","쌐쌑쌒쌖쌗쌙쌚쌛쌝",6,"쌦쌧쌪",8],["9b61","쌳",17,"썆",7],["9b81","썎",25,"썪썫썭썮썯썱썳",4,"썺썻썾",5,"쎅쎆쎇쎉쎊쎋쎍",50,"쏁",22,"쏚"],["9c41","쏛쏝쏞쏡쏣",4,"쏪쏫쏬쏮",5,"쏶쏷쏹",5],["9c61","쏿",8,"쐉",6,"쐑",9],["9c81","쐛",8,"쐥",6,"쐭쐮쐯쐱쐲쐳쐵",6,"쐾",9,"쑉",26,"쑦쑧쑩쑪쑫쑭",6,"쑶쑷쑸쑺",5,"쒁",18,"쒕",6,"쒝",12],["9d41","쒪",13,"쒹쒺쒻쒽",8],["9d61","쓆",25],["9d81","쓠",8,"쓪",5,"쓲쓳쓵쓶쓷쓹쓻쓼쓽쓾씂",9,"씍씎씏씑씒씓씕",6,"씝",10,"씪씫씭씮씯씱",6,"씺씼씾",5,"앆앇앋앏앐앑앒앖앚앛앜앟앢앣앥앦앧앩",6,"앲앶",5,"앾앿얁얂얃얅얆얈얉얊얋얎얐얒얓얔"],["9e41","얖얙얚얛얝얞얟얡",7,"얪",9,"얶"],["9e61","얷얺얿",4,"엋엍엏엒엓엕엖엗엙",6,"엢엤엦엧"],["9e81","엨엩엪엫엯엱엲엳엵엸엹엺엻옂옃옄옉옊옋옍옎옏옑",6,"옚옝",6,"옦옧옩옪옫옯옱옲옶옸옺옼옽옾옿왂왃왅왆왇왉",6,"왒왖",5,"왞왟왡",10,"왭왮왰왲",5,"왺왻왽왾왿욁",6,"욊욌욎",5,"욖욗욙욚욛욝",6,"욦"],["9f41","욨욪",5,"욲욳욵욶욷욻",4,"웂웄웆",5,"웎"],["9f61","웏웑웒웓웕",6,"웞웟웢",5,"웪웫웭웮웯웱웲"],["9f81","웳",4,"웺웻웼웾",5,"윆윇윉윊윋윍",6,"윖윘윚",5,"윢윣윥윦윧윩",6,"윲윴윶윸윹윺윻윾윿읁읂읃읅",4,"읋읎읐읙읚읛읝읞읟읡",6,"읩읪읬",7,"읶읷읹읺읻읿잀잁잂잆잋잌잍잏잒잓잕잙잛",4,"잢잧",4,"잮잯잱잲잳잵잶잷"],["a041","잸잹잺잻잾쟂",5,"쟊쟋쟍쟏쟑",6,"쟙쟚쟛쟜"],["a061","쟞",5,"쟥쟦쟧쟩쟪쟫쟭",13],["a081","쟻",4,"젂젃젅젆젇젉젋",4,"젒젔젗",4,"젞젟젡젢젣젥",6,"젮젰젲",5,"젹젺젻젽젾젿졁",6,"졊졋졎",5,"졕",26,"졲졳졵졶졷졹졻",4,"좂좄좈좉좊좎",5,"좕",7,"좞좠좢좣좤"],["a141","좥좦좧좩",18,"좾좿죀죁"],["a161","죂죃죅죆죇죉죊죋죍",6,"죖죘죚",5,"죢죣죥"],["a181","죦",14,"죶",5,"죾죿줁줂줃줇",4,"줎　、。·‥…¨〃­―∥＼∼‘’“”〔〕〈",9,"±×÷≠≤≥∞∴°′″℃Å￠￡￥♂♀∠⊥⌒∂∇≡≒§※☆★○●◎◇◆□■△▲▽▼→←↑↓↔〓≪≫√∽∝∵∫∬∈∋⊆⊇⊂⊃∪∩∧∨￢"],["a241","줐줒",5,"줙",18],["a261","줭",6,"줵",18],["a281","쥈",7,"쥒쥓쥕쥖쥗쥙",6,"쥢쥤",7,"쥭쥮쥯⇒⇔∀∃´～ˇ˘˝˚˙¸˛¡¿ː∮∑∏¤℉‰◁◀▷▶♤♠♡♥♧♣⊙◈▣◐◑▒▤▥▨▧▦▩♨☏☎☜☞¶†‡↕↗↙↖↘♭♩♪♬㉿㈜№㏇™㏂㏘℡€®"],["a341","쥱쥲쥳쥵",6,"쥽",10,"즊즋즍즎즏"],["a361","즑",6,"즚즜즞",16],["a381","즯",16,"짂짃짅짆짉짋",4,"짒짔짗짘짛！",58,"￦］",32,"￣"],["a441","짞짟짡짣짥짦짨짩짪짫짮짲",5,"짺짻짽짾짿쨁쨂쨃쨄"],["a461","쨅쨆쨇쨊쨎",5,"쨕쨖쨗쨙",12],["a481","쨦쨧쨨쨪",28,"ㄱ",93],["a541","쩇",4,"쩎쩏쩑쩒쩓쩕",6,"쩞쩢",5,"쩩쩪"],["a561","쩫",17,"쩾",5,"쪅쪆"],["a581","쪇",16,"쪙",14,"ⅰ",9],["a5b0","Ⅰ",9],["a5c1","Α",16,"Σ",6],["a5e1","α",16,"σ",6],["a641","쪨",19,"쪾쪿쫁쫂쫃쫅"],["a661","쫆",5,"쫎쫐쫒쫔쫕쫖쫗쫚",5,"쫡",6],["a681","쫨쫩쫪쫫쫭",6,"쫵",18,"쬉쬊─│┌┐┘└├┬┤┴┼━┃┏┓┛┗┣┳┫┻╋┠┯┨┷┿┝┰┥┸╂┒┑┚┙┖┕┎┍┞┟┡┢┦┧┩┪┭┮┱┲┵┶┹┺┽┾╀╁╃",7],["a741","쬋",4,"쬑쬒쬓쬕쬖쬗쬙",6,"쬢",7],["a761","쬪",22,"쭂쭃쭄"],["a781","쭅쭆쭇쭊쭋쭍쭎쭏쭑",6,"쭚쭛쭜쭞",5,"쭥",7,"㎕㎖㎗ℓ㎘㏄㎣㎤㎥㎦㎙",9,"㏊㎍㎎㎏㏏㎈㎉㏈㎧㎨㎰",9,"㎀",4,"㎺",5,"㎐",4,"Ω㏀㏁㎊㎋㎌㏖㏅㎭㎮㎯㏛㎩㎪㎫㎬㏝㏐㏓㏃㏉㏜㏆"],["a841","쭭",10,"쭺",14],["a861","쮉",18,"쮝",6],["a881","쮤",19,"쮹",11,"ÆÐªĦ"],["a8a6","Ĳ"],["a8a8","ĿŁØŒºÞŦŊ"],["a8b1","㉠",27,"ⓐ",25,"①",14,"½⅓⅔¼¾⅛⅜⅝⅞"],["a941","쯅",14,"쯕",10],["a961","쯠쯡쯢쯣쯥쯦쯨쯪",18],["a981","쯽",14,"찎찏찑찒찓찕",6,"찞찟찠찣찤æđðħıĳĸŀłøœßþŧŋŉ㈀",27,"⒜",25,"⑴",14,"¹²³⁴ⁿ₁₂₃₄"],["aa41","찥찦찪찫찭찯찱",6,"찺찿",4,"챆챇챉챊챋챍챎"],["aa61","챏",4,"챖챚",5,"챡챢챣챥챧챩",6,"챱챲"],["aa81","챳챴챶",29,"ぁ",82],["ab41","첔첕첖첗첚첛첝첞첟첡",6,"첪첮",5,"첶첷첹"],["ab61","첺첻첽",6,"쳆쳈쳊",5,"쳑쳒쳓쳕",5],["ab81","쳛",8,"쳥",6,"쳭쳮쳯쳱",12,"ァ",85],["ac41","쳾쳿촀촂",5,"촊촋촍촎촏촑",6,"촚촜촞촟촠"],["ac61","촡촢촣촥촦촧촩촪촫촭",11,"촺",4],["ac81","촿",28,"쵝쵞쵟А",5,"ЁЖ",25],["acd1","а",5,"ёж",25],["ad41","쵡쵢쵣쵥",6,"쵮쵰쵲",5,"쵹",7],["ad61","춁",6,"춉",10,"춖춗춙춚춛춝춞춟"],["ad81","춠춡춢춣춦춨춪",5,"춱",18,"췅"],["ae41","췆",5,"췍췎췏췑",16],["ae61","췢",5,"췩췪췫췭췮췯췱",6,"췺췼췾",4],["ae81","츃츅츆츇츉츊츋츍",6,"츕츖츗츘츚",5,"츢츣츥츦츧츩츪츫"],["af41","츬츭츮츯츲츴츶",19],["af61","칊",13,"칚칛칝칞칢",5,"칪칬"],["af81","칮",5,"칶칷칹칺칻칽",6,"캆캈캊",5,"캒캓캕캖캗캙"],["b041","캚",5,"캢캦",5,"캮",12],["b061","캻",5,"컂",19],["b081","컖",13,"컦컧컩컪컭",6,"컶컺",5,"가각간갇갈갉갊감",7,"같",4,"갠갤갬갭갯갰갱갸갹갼걀걋걍걔걘걜거걱건걷걸걺검겁것겄겅겆겉겊겋게겐겔겜겝겟겠겡겨격겪견겯결겸겹겻겼경곁계곈곌곕곗고곡곤곧골곪곬곯곰곱곳공곶과곽관괄괆"],["b141","켂켃켅켆켇켉",6,"켒켔켖",5,"켝켞켟켡켢켣"],["b161","켥",6,"켮켲",5,"켹",11],["b181","콅",14,"콖콗콙콚콛콝",6,"콦콨콪콫콬괌괍괏광괘괜괠괩괬괭괴괵괸괼굄굅굇굉교굔굘굡굣구국군굳굴굵굶굻굼굽굿궁궂궈궉권궐궜궝궤궷귀귁귄귈귐귑귓규균귤그극근귿글긁금급긋긍긔기긱긴긷길긺김깁깃깅깆깊까깍깎깐깔깖깜깝깟깠깡깥깨깩깬깰깸"],["b241","콭콮콯콲콳콵콶콷콹",6,"쾁쾂쾃쾄쾆",5,"쾍"],["b261","쾎",18,"쾢",5,"쾩"],["b281","쾪",5,"쾱",18,"쿅",6,"깹깻깼깽꺄꺅꺌꺼꺽꺾껀껄껌껍껏껐껑께껙껜껨껫껭껴껸껼꼇꼈꼍꼐꼬꼭꼰꼲꼴꼼꼽꼿꽁꽂꽃꽈꽉꽐꽜꽝꽤꽥꽹꾀꾄꾈꾐꾑꾕꾜꾸꾹꾼꿀꿇꿈꿉꿋꿍꿎꿔꿜꿨꿩꿰꿱꿴꿸뀀뀁뀄뀌뀐뀔뀜뀝뀨끄끅끈끊끌끎끓끔끕끗끙"],["b341","쿌",19,"쿢쿣쿥쿦쿧쿩"],["b361","쿪",5,"쿲쿴쿶",5,"쿽쿾쿿퀁퀂퀃퀅",5],["b381","퀋",5,"퀒",5,"퀙",19,"끝끼끽낀낄낌낍낏낑나낙낚난낟날낡낢남납낫",4,"낱낳내낵낸낼냄냅냇냈냉냐냑냔냘냠냥너넉넋넌널넒넓넘넙넛넜넝넣네넥넨넬넴넵넷넸넹녀녁년녈념녑녔녕녘녜녠노녹논놀놂놈놉놋농높놓놔놘놜놨뇌뇐뇔뇜뇝"],["b441","퀮",5,"퀶퀷퀹퀺퀻퀽",6,"큆큈큊",5],["b461","큑큒큓큕큖큗큙",6,"큡",10,"큮큯"],["b481","큱큲큳큵",6,"큾큿킀킂",18,"뇟뇨뇩뇬뇰뇹뇻뇽누눅눈눋눌눔눕눗눙눠눴눼뉘뉜뉠뉨뉩뉴뉵뉼늄늅늉느늑는늘늙늚늠늡늣능늦늪늬늰늴니닉닌닐닒님닙닛닝닢다닥닦단닫",4,"닳담답닷",4,"닿대댁댄댈댐댑댓댔댕댜더덕덖던덛덜덞덟덤덥"],["b541","킕",14,"킦킧킩킪킫킭",5],["b561","킳킶킸킺",5,"탂탃탅탆탇탊",5,"탒탖",4],["b581","탛탞탟탡탢탣탥",6,"탮탲",5,"탹",11,"덧덩덫덮데덱덴델뎀뎁뎃뎄뎅뎌뎐뎔뎠뎡뎨뎬도독돈돋돌돎돐돔돕돗동돛돝돠돤돨돼됐되된될됨됩됫됴두둑둔둘둠둡둣둥둬뒀뒈뒝뒤뒨뒬뒵뒷뒹듀듄듈듐듕드득든듣들듦듬듭듯등듸디딕딘딛딜딤딥딧딨딩딪따딱딴딸"],["b641","턅",7,"턎",17],["b661","턠",15,"턲턳턵턶턷턹턻턼턽턾"],["b681","턿텂텆",5,"텎텏텑텒텓텕",6,"텞텠텢",5,"텩텪텫텭땀땁땃땄땅땋때땍땐땔땜땝땟땠땡떠떡떤떨떪떫떰떱떳떴떵떻떼떽뗀뗄뗌뗍뗏뗐뗑뗘뗬또똑똔똘똥똬똴뙈뙤뙨뚜뚝뚠뚤뚫뚬뚱뛔뛰뛴뛸뜀뜁뜅뜨뜩뜬뜯뜰뜸뜹뜻띄띈띌띔띕띠띤띨띰띱띳띵라락란랄람랍랏랐랑랒랖랗"],["b741","텮",13,"텽",6,"톅톆톇톉톊"],["b761","톋",20,"톢톣톥톦톧"],["b781","톩",6,"톲톴톶톷톸톹톻톽톾톿퇁",14,"래랙랜랠램랩랫랬랭랴략랸럇량러럭런럴럼럽럿렀렁렇레렉렌렐렘렙렛렝려력련렬렴렵렷렸령례롄롑롓로록론롤롬롭롯롱롸롼뢍뢨뢰뢴뢸룀룁룃룅료룐룔룝룟룡루룩룬룰룸룹룻룽뤄뤘뤠뤼뤽륀륄륌륏륑류륙륜률륨륩"],["b841","퇐",7,"퇙",17],["b861","퇫",8,"퇵퇶퇷퇹",13],["b881","툈툊",5,"툑",24,"륫륭르륵른를름릅릇릉릊릍릎리릭린릴림립릿링마막만많",4,"맘맙맛망맞맡맣매맥맨맬맴맵맷맸맹맺먀먁먈먕머먹먼멀멂멈멉멋멍멎멓메멕멘멜멤멥멧멨멩며멱면멸몃몄명몇몌모목몫몬몰몲몸몹못몽뫄뫈뫘뫙뫼"],["b941","툪툫툮툯툱툲툳툵",6,"툾퉀퉂",5,"퉉퉊퉋퉌"],["b961","퉍",14,"퉝",6,"퉥퉦퉧퉨"],["b981","퉩",22,"튂튃튅튆튇튉튊튋튌묀묄묍묏묑묘묜묠묩묫무묵묶문묻물묽묾뭄뭅뭇뭉뭍뭏뭐뭔뭘뭡뭣뭬뮈뮌뮐뮤뮨뮬뮴뮷므믄믈믐믓미믹민믿밀밂밈밉밋밌밍및밑바",4,"받",4,"밤밥밧방밭배백밴밸뱀뱁뱃뱄뱅뱉뱌뱍뱐뱝버벅번벋벌벎범법벗"],["ba41","튍튎튏튒튓튔튖",5,"튝튞튟튡튢튣튥",6,"튭"],["ba61","튮튯튰튲",5,"튺튻튽튾틁틃",4,"틊틌",5],["ba81","틒틓틕틖틗틙틚틛틝",6,"틦",9,"틲틳틵틶틷틹틺벙벚베벡벤벧벨벰벱벳벴벵벼벽변별볍볏볐병볕볘볜보복볶본볼봄봅봇봉봐봔봤봬뵀뵈뵉뵌뵐뵘뵙뵤뵨부북분붇불붉붊붐붑붓붕붙붚붜붤붰붸뷔뷕뷘뷜뷩뷰뷴뷸븀븃븅브븍븐블븜븝븟비빅빈빌빎빔빕빗빙빚빛빠빡빤"],["bb41","틻",4,"팂팄팆",5,"팏팑팒팓팕팗",4,"팞팢팣"],["bb61","팤팦팧팪팫팭팮팯팱",6,"팺팾",5,"퍆퍇퍈퍉"],["bb81","퍊",31,"빨빪빰빱빳빴빵빻빼빽뺀뺄뺌뺍뺏뺐뺑뺘뺙뺨뻐뻑뻔뻗뻘뻠뻣뻤뻥뻬뼁뼈뼉뼘뼙뼛뼜뼝뽀뽁뽄뽈뽐뽑뽕뾔뾰뿅뿌뿍뿐뿔뿜뿟뿡쀼쁑쁘쁜쁠쁨쁩삐삑삔삘삠삡삣삥사삭삯산삳살삵삶삼삽삿샀상샅새색샌샐샘샙샛샜생샤"],["bc41","퍪",17,"퍾퍿펁펂펃펅펆펇"],["bc61","펈펉펊펋펎펒",5,"펚펛펝펞펟펡",6,"펪펬펮"],["bc81","펯",4,"펵펶펷펹펺펻펽",6,"폆폇폊",5,"폑",5,"샥샨샬샴샵샷샹섀섄섈섐섕서",4,"섣설섦섧섬섭섯섰성섶세섹센셀셈셉셋셌셍셔셕션셜셤셥셧셨셩셰셴셸솅소속솎손솔솖솜솝솟송솥솨솩솬솰솽쇄쇈쇌쇔쇗쇘쇠쇤쇨쇰쇱쇳쇼쇽숀숄숌숍숏숑수숙순숟술숨숩숫숭"],["bd41","폗폙",7,"폢폤",7,"폮폯폱폲폳폵폶폷"],["bd61","폸폹폺폻폾퐀퐂",5,"퐉",13],["bd81","퐗",5,"퐞",25,"숯숱숲숴쉈쉐쉑쉔쉘쉠쉥쉬쉭쉰쉴쉼쉽쉿슁슈슉슐슘슛슝스슥슨슬슭슴습슷승시식신싣실싫심십싯싱싶싸싹싻싼쌀쌈쌉쌌쌍쌓쌔쌕쌘쌜쌤쌥쌨쌩썅써썩썬썰썲썸썹썼썽쎄쎈쎌쏀쏘쏙쏜쏟쏠쏢쏨쏩쏭쏴쏵쏸쐈쐐쐤쐬쐰"],["be41","퐸",7,"푁푂푃푅",14],["be61","푔",7,"푝푞푟푡푢푣푥",7,"푮푰푱푲"],["be81","푳",4,"푺푻푽푾풁풃",4,"풊풌풎",5,"풕",8,"쐴쐼쐽쑈쑤쑥쑨쑬쑴쑵쑹쒀쒔쒜쒸쒼쓩쓰쓱쓴쓸쓺쓿씀씁씌씐씔씜씨씩씬씰씸씹씻씽아악안앉않알앍앎앓암압앗았앙앝앞애액앤앨앰앱앳앴앵야약얀얄얇얌얍얏양얕얗얘얜얠얩어억언얹얻얼얽얾엄",6,"엌엎"],["bf41","풞",10,"풪",14],["bf61","풹",18,"퓍퓎퓏퓑퓒퓓퓕"],["bf81","퓖",5,"퓝퓞퓠",7,"퓩퓪퓫퓭퓮퓯퓱",6,"퓹퓺퓼에엑엔엘엠엡엣엥여역엮연열엶엷염",5,"옅옆옇예옌옐옘옙옛옜오옥온올옭옮옰옳옴옵옷옹옻와왁완왈왐왑왓왔왕왜왝왠왬왯왱외왹왼욀욈욉욋욍요욕욘욜욤욥욧용우욱운울욹욺움웁웃웅워웍원월웜웝웠웡웨"],["c041","퓾",5,"픅픆픇픉픊픋픍",6,"픖픘",5],["c061","픞",25],["c081","픸픹픺픻픾픿핁핂핃핅",6,"핎핐핒",5,"핚핛핝핞핟핡핢핣웩웬웰웸웹웽위윅윈윌윔윕윗윙유육윤율윰윱윳융윷으윽은을읊음읍읏응",7,"읜읠읨읫이익인일읽읾잃임입잇있잉잊잎자작잔잖잗잘잚잠잡잣잤장잦재잭잰잴잼잽잿쟀쟁쟈쟉쟌쟎쟐쟘쟝쟤쟨쟬저적전절젊"],["c141","핤핦핧핪핬핮",5,"핶핷핹핺핻핽",6,"햆햊햋"],["c161","햌햍햎햏햑",19,"햦햧"],["c181","햨",31,"점접젓정젖제젝젠젤젬젭젯젱져젼졀졈졉졌졍졔조족존졸졺좀좁좃종좆좇좋좌좍좔좝좟좡좨좼좽죄죈죌죔죕죗죙죠죡죤죵주죽준줄줅줆줌줍줏중줘줬줴쥐쥑쥔쥘쥠쥡쥣쥬쥰쥴쥼즈즉즌즐즘즙즛증지직진짇질짊짐집짓"],["c241","헊헋헍헎헏헑헓",4,"헚헜헞",5,"헦헧헩헪헫헭헮"],["c261","헯",4,"헶헸헺",5,"혂혃혅혆혇혉",6,"혒"],["c281","혖",5,"혝혞혟혡혢혣혥",7,"혮",9,"혺혻징짖짙짚짜짝짠짢짤짧짬짭짯짰짱째짹짼쨀쨈쨉쨋쨌쨍쨔쨘쨩쩌쩍쩐쩔쩜쩝쩟쩠쩡쩨쩽쪄쪘쪼쪽쫀쫄쫌쫍쫏쫑쫓쫘쫙쫠쫬쫴쬈쬐쬔쬘쬠쬡쭁쭈쭉쭌쭐쭘쭙쭝쭤쭸쭹쮜쮸쯔쯤쯧쯩찌찍찐찔찜찝찡찢찧차착찬찮찰참찹찻"],["c341","혽혾혿홁홂홃홄홆홇홊홌홎홏홐홒홓홖홗홙홚홛홝",4],["c361","홢",4,"홨홪",5,"홲홳홵",11],["c381","횁횂횄횆",5,"횎횏횑횒횓횕",7,"횞횠횢",5,"횩횪찼창찾채책챈챌챔챕챗챘챙챠챤챦챨챰챵처척천철첨첩첫첬청체첵첸첼쳄쳅쳇쳉쳐쳔쳤쳬쳰촁초촉촌촐촘촙촛총촤촨촬촹최쵠쵤쵬쵭쵯쵱쵸춈추축춘출춤춥춧충춰췄췌췐취췬췰췸췹췻췽츄츈츌츔츙츠측츤츨츰츱츳층"],["c441","횫횭횮횯횱",7,"횺횼",7,"훆훇훉훊훋"],["c461","훍훎훏훐훒훓훕훖훘훚",5,"훡훢훣훥훦훧훩",4],["c481","훮훯훱훲훳훴훶",5,"훾훿휁휂휃휅",11,"휒휓휔치칙친칟칠칡침칩칫칭카칵칸칼캄캅캇캉캐캑캔캘캠캡캣캤캥캬캭컁커컥컨컫컬컴컵컷컸컹케켁켄켈켐켑켓켕켜켠켤켬켭켯켰켱켸코콕콘콜콤콥콧콩콰콱콴콸쾀쾅쾌쾡쾨쾰쿄쿠쿡쿤쿨쿰쿱쿳쿵쿼퀀퀄퀑퀘퀭퀴퀵퀸퀼"],["c541","휕휖휗휚휛휝휞휟휡",6,"휪휬휮",5,"휶휷휹"],["c561","휺휻휽",6,"흅흆흈흊",5,"흒흓흕흚",4],["c581","흟흢흤흦흧흨흪흫흭흮흯흱흲흳흵",6,"흾흿힀힂",5,"힊힋큄큅큇큉큐큔큘큠크큭큰클큼큽킁키킥킨킬킴킵킷킹타탁탄탈탉탐탑탓탔탕태택탠탤탬탭탯탰탱탸턍터턱턴털턺텀텁텃텄텅테텍텐텔템텝텟텡텨텬텼톄톈토톡톤톨톰톱톳통톺톼퇀퇘퇴퇸툇툉툐투툭툰툴툼툽툿퉁퉈퉜"],["c641","힍힎힏힑",6,"힚힜힞",5],["c6a1","퉤튀튁튄튈튐튑튕튜튠튤튬튱트특튼튿틀틂틈틉틋틔틘틜틤틥티틱틴틸팀팁팃팅파팍팎판팔팖팜팝팟팠팡팥패팩팬팰팸팹팻팼팽퍄퍅퍼퍽펀펄펌펍펏펐펑페펙펜펠펨펩펫펭펴편펼폄폅폈평폐폘폡폣포폭폰폴폼폽폿퐁"],["c7a1","퐈퐝푀푄표푠푤푭푯푸푹푼푿풀풂품풉풋풍풔풩퓌퓐퓔퓜퓟퓨퓬퓰퓸퓻퓽프픈플픔픕픗피픽핀필핌핍핏핑하학한할핥함합핫항해핵핸핼햄햅햇했행햐향허헉헌헐헒험헙헛헝헤헥헨헬헴헵헷헹혀혁현혈혐협혓혔형혜혠"],["c8a1","혤혭호혹혼홀홅홈홉홋홍홑화확환활홧황홰홱홴횃횅회획횐횔횝횟횡효횬횰횹횻후훅훈훌훑훔훗훙훠훤훨훰훵훼훽휀휄휑휘휙휜휠휨휩휫휭휴휵휸휼흄흇흉흐흑흔흖흗흘흙흠흡흣흥흩희흰흴흼흽힁히힉힌힐힘힙힛힝"],["caa1","伽佳假價加可呵哥嘉嫁家暇架枷柯歌珂痂稼苛茄街袈訶賈跏軻迦駕刻却各恪慤殼珏脚覺角閣侃刊墾奸姦干幹懇揀杆柬桿澗癎看磵稈竿簡肝艮艱諫間乫喝曷渴碣竭葛褐蝎鞨勘坎堪嵌感憾戡敢柑橄減甘疳監瞰紺邯鑑鑒龕"],["cba1","匣岬甲胛鉀閘剛堈姜岡崗康强彊慷江畺疆糠絳綱羌腔舡薑襁講鋼降鱇介价個凱塏愷愾慨改槪漑疥皆盖箇芥蓋豈鎧開喀客坑更粳羹醵倨去居巨拒据據擧渠炬祛距踞車遽鉅鋸乾件健巾建愆楗腱虔蹇鍵騫乞傑杰桀儉劍劒檢"],["cca1","瞼鈐黔劫怯迲偈憩揭擊格檄激膈覡隔堅牽犬甄絹繭肩見譴遣鵑抉決潔結缺訣兼慊箝謙鉗鎌京俓倞傾儆勁勍卿坰境庚徑慶憬擎敬景暻更梗涇炅烱璟璥瓊痙硬磬竟競絅經耕耿脛莖警輕逕鏡頃頸驚鯨係啓堺契季屆悸戒桂械"],["cda1","棨溪界癸磎稽系繫繼計誡谿階鷄古叩告呱固姑孤尻庫拷攷故敲暠枯槁沽痼皐睾稿羔考股膏苦苽菰藁蠱袴誥賈辜錮雇顧高鼓哭斛曲梏穀谷鵠困坤崑昆梱棍滾琨袞鯤汨滑骨供公共功孔工恐恭拱控攻珙空蚣貢鞏串寡戈果瓜"],["cea1","科菓誇課跨過鍋顆廓槨藿郭串冠官寬慣棺款灌琯瓘管罐菅觀貫關館刮恝括适侊光匡壙廣曠洸炚狂珖筐胱鑛卦掛罫乖傀塊壞怪愧拐槐魁宏紘肱轟交僑咬喬嬌嶠巧攪敎校橋狡皎矯絞翹膠蕎蛟較轎郊餃驕鮫丘久九仇俱具勾"],["cfa1","區口句咎嘔坵垢寇嶇廐懼拘救枸柩構歐毆毬求溝灸狗玖球瞿矩究絿耉臼舅舊苟衢謳購軀逑邱鉤銶駒驅鳩鷗龜國局菊鞠鞫麴君窘群裙軍郡堀屈掘窟宮弓穹窮芎躬倦券勸卷圈拳捲權淃眷厥獗蕨蹶闕机櫃潰詭軌饋句晷歸貴"],["d0a1","鬼龜叫圭奎揆槻珪硅窺竅糾葵規赳逵閨勻均畇筠菌鈞龜橘克剋劇戟棘極隙僅劤勤懃斤根槿瑾筋芹菫覲謹近饉契今妗擒昑檎琴禁禽芩衾衿襟金錦伋及急扱汲級給亘兢矜肯企伎其冀嗜器圻基埼夔奇妓寄岐崎己幾忌技旗旣"],["d1a1","朞期杞棋棄機欺氣汽沂淇玘琦琪璂璣畸畿碁磯祁祇祈祺箕紀綺羈耆耭肌記譏豈起錡錤飢饑騎騏驥麒緊佶吉拮桔金喫儺喇奈娜懦懶拏拿癩",5,"那樂",4,"諾酪駱亂卵暖欄煖爛蘭難鸞捏捺南嵐枏楠湳濫男藍襤拉"],["d2a1","納臘蠟衲囊娘廊",4,"乃來內奈柰耐冷女年撚秊念恬拈捻寧寗努勞奴弩怒擄櫓爐瑙盧",5,"駑魯",10,"濃籠聾膿農惱牢磊腦賂雷尿壘",7,"嫩訥杻紐勒",5,"能菱陵尼泥匿溺多茶"],["d3a1","丹亶但單團壇彖斷旦檀段湍短端簞緞蛋袒鄲鍛撻澾獺疸達啖坍憺擔曇淡湛潭澹痰聃膽蕁覃談譚錟沓畓答踏遝唐堂塘幢戇撞棠當糖螳黨代垈坮大對岱帶待戴擡玳臺袋貸隊黛宅德悳倒刀到圖堵塗導屠島嶋度徒悼挑掉搗桃"],["d4a1","棹櫂淘渡滔濤燾盜睹禱稻萄覩賭跳蹈逃途道都鍍陶韜毒瀆牘犢獨督禿篤纛讀墩惇敦旽暾沌焞燉豚頓乭突仝冬凍動同憧東桐棟洞潼疼瞳童胴董銅兜斗杜枓痘竇荳讀豆逗頭屯臀芚遁遯鈍得嶝橙燈登等藤謄鄧騰喇懶拏癩羅"],["d5a1","蘿螺裸邏樂洛烙珞絡落諾酪駱丹亂卵欄欒瀾爛蘭鸞剌辣嵐擥攬欖濫籃纜藍襤覽拉臘蠟廊朗浪狼琅瑯螂郞來崍徠萊冷掠略亮倆兩凉梁樑粮粱糧良諒輛量侶儷勵呂廬慮戾旅櫚濾礪藜蠣閭驢驪麗黎力曆歷瀝礫轢靂憐戀攣漣"],["d6a1","煉璉練聯蓮輦連鍊冽列劣洌烈裂廉斂殮濂簾獵令伶囹寧岺嶺怜玲笭羚翎聆逞鈴零靈領齡例澧禮醴隷勞怒撈擄櫓潞瀘爐盧老蘆虜路輅露魯鷺鹵碌祿綠菉錄鹿麓論壟弄朧瀧瓏籠聾儡瀨牢磊賂賚賴雷了僚寮廖料燎療瞭聊蓼"],["d7a1","遼鬧龍壘婁屢樓淚漏瘻累縷蔞褸鏤陋劉旒柳榴流溜瀏琉瑠留瘤硫謬類六戮陸侖倫崙淪綸輪律慄栗率隆勒肋凜凌楞稜綾菱陵俚利厘吏唎履悧李梨浬犁狸理璃異痢籬罹羸莉裏裡里釐離鯉吝潾燐璘藺躪隣鱗麟林淋琳臨霖砬"],["d8a1","立笠粒摩瑪痲碼磨馬魔麻寞幕漠膜莫邈万卍娩巒彎慢挽晩曼滿漫灣瞞萬蔓蠻輓饅鰻唜抹末沫茉襪靺亡妄忘忙望網罔芒茫莽輞邙埋妹媒寐昧枚梅每煤罵買賣邁魅脈貊陌驀麥孟氓猛盲盟萌冪覓免冕勉棉沔眄眠綿緬面麵滅"],["d9a1","蔑冥名命明暝椧溟皿瞑茗蓂螟酩銘鳴袂侮冒募姆帽慕摸摹暮某模母毛牟牡瑁眸矛耗芼茅謀謨貌木沐牧目睦穆鶩歿沒夢朦蒙卯墓妙廟描昴杳渺猫竗苗錨務巫憮懋戊拇撫无楙武毋無珷畝繆舞茂蕪誣貿霧鵡墨默們刎吻問文"],["daa1","汶紊紋聞蚊門雯勿沕物味媚尾嵋彌微未梶楣渼湄眉米美薇謎迷靡黴岷悶愍憫敏旻旼民泯玟珉緡閔密蜜謐剝博拍搏撲朴樸泊珀璞箔粕縛膊舶薄迫雹駁伴半反叛拌搬攀斑槃泮潘班畔瘢盤盼磐磻礬絆般蟠返頒飯勃拔撥渤潑"],["dba1","發跋醱鉢髮魃倣傍坊妨尨幇彷房放方旁昉枋榜滂磅紡肪膀舫芳蒡蚌訪謗邦防龐倍俳北培徘拜排杯湃焙盃背胚裴裵褙賠輩配陪伯佰帛柏栢白百魄幡樊煩燔番磻繁蕃藩飜伐筏罰閥凡帆梵氾汎泛犯範范法琺僻劈壁擘檗璧癖"],["dca1","碧蘗闢霹便卞弁變辨辯邊別瞥鱉鼈丙倂兵屛幷昞昺柄棅炳甁病秉竝輧餠騈保堡報寶普步洑湺潽珤甫菩補褓譜輔伏僕匐卜宓復服福腹茯蔔複覆輹輻馥鰒本乶俸奉封峯峰捧棒烽熢琫縫蓬蜂逢鋒鳳不付俯傅剖副否咐埠夫婦"],["dda1","孚孵富府復扶敷斧浮溥父符簿缶腐腑膚艀芙莩訃負賦賻赴趺部釜阜附駙鳧北分吩噴墳奔奮忿憤扮昐汾焚盆粉糞紛芬賁雰不佛弗彿拂崩朋棚硼繃鵬丕備匕匪卑妃婢庇悲憊扉批斐枇榧比毖毗毘沸泌琵痺砒碑秕秘粃緋翡肥"],["dea1","脾臂菲蜚裨誹譬費鄙非飛鼻嚬嬪彬斌檳殯浜濱瀕牝玭貧賓頻憑氷聘騁乍事些仕伺似使俟僿史司唆嗣四士奢娑寫寺射巳師徙思捨斜斯柶査梭死沙泗渣瀉獅砂社祀祠私篩紗絲肆舍莎蓑蛇裟詐詞謝賜赦辭邪飼駟麝削數朔索"],["dfa1","傘刪山散汕珊産疝算蒜酸霰乷撒殺煞薩三參杉森渗芟蔘衫揷澁鈒颯上傷像償商喪嘗孀尙峠常床庠廂想桑橡湘爽牀狀相祥箱翔裳觴詳象賞霜塞璽賽嗇塞穡索色牲生甥省笙墅壻嶼序庶徐恕抒捿敍暑曙書栖棲犀瑞筮絮緖署"],["e0a1","胥舒薯西誓逝鋤黍鼠夕奭席惜昔晳析汐淅潟石碩蓆釋錫仙僊先善嬋宣扇敾旋渲煽琁瑄璇璿癬禪線繕羨腺膳船蘚蟬詵跣選銑鐥饍鮮卨屑楔泄洩渫舌薛褻設說雪齧剡暹殲纖蟾贍閃陝攝涉燮葉城姓宬性惺成星晟猩珹盛省筬"],["e1a1","聖聲腥誠醒世勢歲洗稅笹細說貰召嘯塑宵小少巢所掃搔昭梳沼消溯瀟炤燒甦疏疎瘙笑篠簫素紹蔬蕭蘇訴逍遡邵銷韶騷俗屬束涑粟續謖贖速孫巽損蓀遜飡率宋悚松淞訟誦送頌刷殺灑碎鎖衰釗修受嗽囚垂壽嫂守岫峀帥愁"],["e2a1","戍手授搜收數樹殊水洙漱燧狩獸琇璲瘦睡秀穗竪粹綏綬繡羞脩茱蒐蓚藪袖誰讐輸遂邃酬銖銹隋隧隨雖需須首髓鬚叔塾夙孰宿淑潚熟琡璹肅菽巡徇循恂旬栒楯橓殉洵淳珣盾瞬筍純脣舜荀蓴蕣詢諄醇錞順馴戌術述鉥崇崧"],["e3a1","嵩瑟膝蝨濕拾習褶襲丞乘僧勝升承昇繩蠅陞侍匙嘶始媤尸屎屍市弑恃施是時枾柴猜矢示翅蒔蓍視試詩諡豕豺埴寔式息拭植殖湜熄篒蝕識軾食飾伸侁信呻娠宸愼新晨燼申神紳腎臣莘薪藎蜃訊身辛辰迅失室實悉審尋心沁"],["e4a1","沈深瀋甚芯諶什十拾雙氏亞俄兒啞娥峨我牙芽莪蛾衙訝阿雅餓鴉鵝堊岳嶽幄惡愕握樂渥鄂鍔顎鰐齷安岸按晏案眼雁鞍顔鮟斡謁軋閼唵岩巖庵暗癌菴闇壓押狎鴨仰央怏昻殃秧鴦厓哀埃崖愛曖涯碍艾隘靄厄扼掖液縊腋額"],["e5a1","櫻罌鶯鸚也倻冶夜惹揶椰爺耶若野弱掠略約若葯蒻藥躍亮佯兩凉壤孃恙揚攘敭暘梁楊樣洋瀁煬痒瘍禳穰糧羊良襄諒讓釀陽量養圄御於漁瘀禦語馭魚齬億憶抑檍臆偃堰彦焉言諺孼蘖俺儼嚴奄掩淹嶪業円予余勵呂女如廬"],["e6a1","旅歟汝濾璵礖礪與艅茹輿轝閭餘驪麗黎亦力域役易曆歷疫繹譯轢逆驛嚥堧姸娟宴年延憐戀捐挻撚椽沇沿涎涓淵演漣烟然煙煉燃燕璉硏硯秊筵緣練縯聯衍軟輦蓮連鉛鍊鳶列劣咽悅涅烈熱裂說閱厭廉念捻染殮炎焰琰艶苒"],["e7a1","簾閻髥鹽曄獵燁葉令囹塋寧嶺嶸影怜映暎楹榮永泳渶潁濚瀛瀯煐營獰玲瑛瑩瓔盈穎纓羚聆英詠迎鈴鍈零霙靈領乂倪例刈叡曳汭濊猊睿穢芮藝蘂禮裔詣譽豫醴銳隸霓預五伍俉傲午吾吳嗚塢墺奧娛寤悟惡懊敖旿晤梧汚澳"],["e8a1","烏熬獒筽蜈誤鰲鼇屋沃獄玉鈺溫瑥瘟穩縕蘊兀壅擁瓮甕癰翁邕雍饔渦瓦窩窪臥蛙蝸訛婉完宛梡椀浣玩琓琬碗緩翫脘腕莞豌阮頑曰往旺枉汪王倭娃歪矮外嵬巍猥畏了僚僥凹堯夭妖姚寥寮尿嶢拗搖撓擾料曜樂橈燎燿瑤療"],["e9a1","窈窯繇繞耀腰蓼蟯要謠遙遼邀饒慾欲浴縟褥辱俑傭冗勇埇墉容庸慂榕涌湧溶熔瑢用甬聳茸蓉踊鎔鏞龍于佑偶優又友右宇寓尤愚憂旴牛玗瑀盂祐禑禹紆羽芋藕虞迂遇郵釪隅雨雩勖彧旭昱栯煜稶郁頊云暈橒殞澐熉耘芸蕓"],["eaa1","運隕雲韻蔚鬱亐熊雄元原員圓園垣媛嫄寃怨愿援沅洹湲源爰猿瑗苑袁轅遠阮院願鴛月越鉞位偉僞危圍委威尉慰暐渭爲瑋緯胃萎葦蔿蝟衛褘謂違韋魏乳侑儒兪劉唯喩孺宥幼幽庾悠惟愈愉揄攸有杻柔柚柳楡楢油洧流游溜"],["eba1","濡猶猷琉瑜由留癒硫紐維臾萸裕誘諛諭踰蹂遊逾遺酉釉鍮類六堉戮毓肉育陸倫允奫尹崙淪潤玧胤贇輪鈗閏律慄栗率聿戎瀜絨融隆垠恩慇殷誾銀隱乙吟淫蔭陰音飮揖泣邑凝應膺鷹依倚儀宜意懿擬椅毅疑矣義艤薏蟻衣誼"],["eca1","議醫二以伊利吏夷姨履已弛彛怡易李梨泥爾珥理異痍痢移罹而耳肄苡荑裏裡貽貳邇里離飴餌匿溺瀷益翊翌翼謚人仁刃印吝咽因姻寅引忍湮燐璘絪茵藺蚓認隣靭靷鱗麟一佚佾壹日溢逸鎰馹任壬妊姙恁林淋稔臨荏賃入卄"],["eda1","立笠粒仍剩孕芿仔刺咨姉姿子字孜恣慈滋炙煮玆瓷疵磁紫者自茨蔗藉諮資雌作勺嚼斫昨灼炸爵綽芍酌雀鵲孱棧殘潺盞岑暫潛箴簪蠶雜丈仗匠場墻壯奬將帳庄張掌暲杖樟檣欌漿牆狀獐璋章粧腸臟臧莊葬蔣薔藏裝贓醬長"],["eea1","障再哉在宰才材栽梓渽滓災縡裁財載齋齎爭箏諍錚佇低儲咀姐底抵杵楮樗沮渚狙猪疽箸紵苧菹著藷詛貯躇這邸雎齟勣吊嫡寂摘敵滴狄炙的積笛籍績翟荻謫賊赤跡蹟迪迹適鏑佃佺傳全典前剪塡塼奠專展廛悛戰栓殿氈澱"],["efa1","煎琠田甸畑癲筌箋箭篆纏詮輾轉鈿銓錢鐫電顚顫餞切截折浙癤竊節絶占岾店漸点粘霑鮎點接摺蝶丁井亭停偵呈姃定幀庭廷征情挺政整旌晶晸柾楨檉正汀淀淨渟湞瀞炡玎珽町睛碇禎程穽精綎艇訂諪貞鄭酊釘鉦鋌錠霆靖"],["f0a1","靜頂鼎制劑啼堤帝弟悌提梯濟祭第臍薺製諸蹄醍除際霽題齊俎兆凋助嘲弔彫措操早晁曺曹朝條棗槽漕潮照燥爪璪眺祖祚租稠窕粗糟組繰肇藻蚤詔調趙躁造遭釣阻雕鳥族簇足鏃存尊卒拙猝倧宗從悰慫棕淙琮種終綜縱腫"],["f1a1","踪踵鍾鐘佐坐左座挫罪主住侏做姝胄呪周嗾奏宙州廚晝朱柱株注洲湊澍炷珠疇籌紂紬綢舟蛛註誅走躊輳週酎酒鑄駐竹粥俊儁准埈寯峻晙樽浚準濬焌畯竣蠢逡遵雋駿茁中仲衆重卽櫛楫汁葺增憎曾拯烝甑症繒蒸證贈之只"],["f2a1","咫地址志持指摯支旨智枝枳止池沚漬知砥祉祗紙肢脂至芝芷蜘誌識贄趾遲直稙稷織職唇嗔塵振搢晉晋桭榛殄津溱珍瑨璡畛疹盡眞瞋秦縉縝臻蔯袗診賑軫辰進鎭陣陳震侄叱姪嫉帙桎瓆疾秩窒膣蛭質跌迭斟朕什執潗緝輯"],["f3a1","鏶集徵懲澄且侘借叉嗟嵯差次此磋箚茶蹉車遮捉搾着窄錯鑿齪撰澯燦璨瓚竄簒纂粲纘讚贊鑽餐饌刹察擦札紮僭參塹慘慙懺斬站讒讖倉倡創唱娼廠彰愴敞昌昶暢槍滄漲猖瘡窓脹艙菖蒼債埰寀寨彩採砦綵菜蔡采釵冊柵策"],["f4a1","責凄妻悽處倜刺剔尺慽戚拓擲斥滌瘠脊蹠陟隻仟千喘天川擅泉淺玔穿舛薦賤踐遷釧闡阡韆凸哲喆徹撤澈綴輟轍鐵僉尖沾添甛瞻簽籤詹諂堞妾帖捷牒疊睫諜貼輒廳晴淸聽菁請靑鯖切剃替涕滯締諦逮遞體初剿哨憔抄招梢"],["f5a1","椒楚樵炒焦硝礁礎秒稍肖艸苕草蕉貂超酢醋醮促囑燭矗蜀觸寸忖村邨叢塚寵悤憁摠總聰蔥銃撮催崔最墜抽推椎楸樞湫皺秋芻萩諏趨追鄒酋醜錐錘鎚雛騶鰍丑畜祝竺筑築縮蓄蹙蹴軸逐春椿瑃出朮黜充忠沖蟲衝衷悴膵萃"],["f6a1","贅取吹嘴娶就炊翠聚脆臭趣醉驟鷲側仄厠惻測層侈値嗤峙幟恥梔治淄熾痔痴癡稚穉緇緻置致蚩輜雉馳齒則勅飭親七柒漆侵寢枕沈浸琛砧針鍼蟄秤稱快他咤唾墮妥惰打拖朶楕舵陀馱駝倬卓啄坼度托拓擢晫柝濁濯琢琸託"],["f7a1","鐸呑嘆坦彈憚歎灘炭綻誕奪脫探眈耽貪塔搭榻宕帑湯糖蕩兌台太怠態殆汰泰笞胎苔跆邰颱宅擇澤撑攄兎吐土討慟桶洞痛筒統通堆槌腿褪退頹偸套妬投透鬪慝特闖坡婆巴把播擺杷波派爬琶破罷芭跛頗判坂板版瓣販辦鈑"],["f8a1","阪八叭捌佩唄悖敗沛浿牌狽稗覇貝彭澎烹膨愎便偏扁片篇編翩遍鞭騙貶坪平枰萍評吠嬖幣廢弊斃肺蔽閉陛佈包匍匏咆哺圃布怖抛抱捕暴泡浦疱砲胞脯苞葡蒲袍褒逋鋪飽鮑幅暴曝瀑爆輻俵剽彪慓杓標漂瓢票表豹飇飄驃"],["f9a1","品稟楓諷豊風馮彼披疲皮被避陂匹弼必泌珌畢疋筆苾馝乏逼下何厦夏廈昰河瑕荷蝦賀遐霞鰕壑學虐謔鶴寒恨悍旱汗漢澣瀚罕翰閑閒限韓割轄函含咸啣喊檻涵緘艦銜陷鹹合哈盒蛤閤闔陜亢伉姮嫦巷恒抗杭桁沆港缸肛航"],["faa1","行降項亥偕咳垓奚孩害懈楷海瀣蟹解該諧邂駭骸劾核倖幸杏荇行享向嚮珦鄕響餉饗香噓墟虛許憲櫶獻軒歇險驗奕爀赫革俔峴弦懸晛泫炫玄玹現眩睍絃絢縣舷衒見賢鉉顯孑穴血頁嫌俠協夾峽挾浹狹脅脇莢鋏頰亨兄刑型"],["fba1","形泂滎瀅灐炯熒珩瑩荊螢衡逈邢鎣馨兮彗惠慧暳蕙蹊醯鞋乎互呼壕壺好岵弧戶扈昊晧毫浩淏湖滸澔濠濩灝狐琥瑚瓠皓祜糊縞胡芦葫蒿虎號蝴護豪鎬頀顥惑或酷婚昏混渾琿魂忽惚笏哄弘汞泓洪烘紅虹訌鴻化和嬅樺火畵"],["fca1","禍禾花華話譁貨靴廓擴攫確碻穫丸喚奐宦幻患換歡晥桓渙煥環紈還驩鰥活滑猾豁闊凰幌徨恍惶愰慌晃晄榥況湟滉潢煌璜皇篁簧荒蝗遑隍黃匯回廻徊恢悔懷晦會檜淮澮灰獪繪膾茴蛔誨賄劃獲宖橫鐄哮嚆孝效斅曉梟涍淆"],["fda1","爻肴酵驍侯候厚后吼喉嗅帿後朽煦珝逅勛勳塤壎焄熏燻薰訓暈薨喧暄煊萱卉喙毁彙徽揮暉煇諱輝麾休携烋畦虧恤譎鷸兇凶匈洶胸黑昕欣炘痕吃屹紇訖欠欽歆吸恰洽翕興僖凞喜噫囍姬嬉希憙憘戱晞曦熙熹熺犧禧稀羲詰"]],Cr=[["0","\0",127],["a140","　，、。．‧；：？！︰…‥﹐﹑﹒·﹔﹕﹖﹗｜–︱—︳╴︴﹏（）︵︶｛｝︷︸〔〕︹︺【】︻︼《》︽︾〈〉︿﹀「」﹁﹂『』﹃﹄﹙﹚"],["a1a1","﹛﹜﹝﹞‘’“”〝〞‵′＃＆＊※§〃○●△▲◎☆★◇◆□■▽▼㊣℅¯￣＿ˍ﹉﹊﹍﹎﹋﹌﹟﹠﹡＋－×÷±√＜＞＝≦≧≠∞≒≡﹢",4,"～∩∪⊥∠∟⊿㏒㏑∫∮∵∴♀♂⊕⊙↑↓←→↖↗↙↘∥∣／"],["a240","＼∕﹨＄￥〒￠￡％＠℃℉﹩﹪﹫㏕㎜㎝㎞㏎㎡㎎㎏㏄°兙兛兞兝兡兣嗧瓩糎▁",7,"▏▎▍▌▋▊▉┼┴┬┤├▔─│▕┌┐└┘╭"],["a2a1","╮╰╯═╞╪╡◢◣◥◤╱╲╳０",9,"Ⅰ",9,"〡",8,"十卄卅Ａ",25,"ａ",21],["a340","ｗｘｙｚΑ",16,"Σ",6,"α",16,"σ",6,"ㄅ",10],["a3a1","ㄐ",25,"˙ˉˊˇˋ"],["a3e1","€"],["a440","一乙丁七乃九了二人儿入八几刀刁力匕十卜又三下丈上丫丸凡久么也乞于亡兀刃勺千叉口土士夕大女子孑孓寸小尢尸山川工己已巳巾干廾弋弓才"],["a4a1","丑丐不中丰丹之尹予云井互五亢仁什仃仆仇仍今介仄元允內六兮公冗凶分切刈勻勾勿化匹午升卅卞厄友及反壬天夫太夭孔少尤尺屯巴幻廿弔引心戈戶手扎支文斗斤方日曰月木欠止歹毋比毛氏水火爪父爻片牙牛犬王丙"],["a540","世丕且丘主乍乏乎以付仔仕他仗代令仙仞充兄冉冊冬凹出凸刊加功包匆北匝仟半卉卡占卯卮去可古右召叮叩叨叼司叵叫另只史叱台句叭叻四囚外"],["a5a1","央失奴奶孕它尼巨巧左市布平幼弁弘弗必戊打扔扒扑斥旦朮本未末札正母民氐永汁汀氾犯玄玉瓜瓦甘生用甩田由甲申疋白皮皿目矛矢石示禾穴立丞丟乒乓乩亙交亦亥仿伉伙伊伕伍伐休伏仲件任仰仳份企伋光兇兆先全"],["a640","共再冰列刑划刎刖劣匈匡匠印危吉吏同吊吐吁吋各向名合吃后吆吒因回囝圳地在圭圬圯圩夙多夷夸妄奸妃好她如妁字存宇守宅安寺尖屹州帆并年"],["a6a1","式弛忙忖戎戌戍成扣扛托收早旨旬旭曲曳有朽朴朱朵次此死氖汝汗汙江池汐汕污汛汍汎灰牟牝百竹米糸缶羊羽老考而耒耳聿肉肋肌臣自至臼舌舛舟艮色艾虫血行衣西阡串亨位住佇佗佞伴佛何估佐佑伽伺伸佃佔似但佣"],["a740","作你伯低伶余佝佈佚兌克免兵冶冷別判利刪刨劫助努劬匣即卵吝吭吞吾否呎吧呆呃吳呈呂君吩告吹吻吸吮吵吶吠吼呀吱含吟听囪困囤囫坊坑址坍"],["a7a1","均坎圾坐坏圻壯夾妝妒妨妞妣妙妖妍妤妓妊妥孝孜孚孛完宋宏尬局屁尿尾岐岑岔岌巫希序庇床廷弄弟彤形彷役忘忌志忍忱快忸忪戒我抄抗抖技扶抉扭把扼找批扳抒扯折扮投抓抑抆改攻攸旱更束李杏材村杜杖杞杉杆杠"],["a840","杓杗步每求汞沙沁沈沉沅沛汪決沐汰沌汨沖沒汽沃汲汾汴沆汶沍沔沘沂灶灼災灸牢牡牠狄狂玖甬甫男甸皂盯矣私秀禿究系罕肖肓肝肘肛肚育良芒"],["a8a1","芋芍見角言谷豆豕貝赤走足身車辛辰迂迆迅迄巡邑邢邪邦那酉釆里防阮阱阪阬並乖乳事些亞享京佯依侍佳使佬供例來侃佰併侈佩佻侖佾侏侑佺兔兒兕兩具其典冽函刻券刷刺到刮制剁劾劻卒協卓卑卦卷卸卹取叔受味呵"],["a940","咖呸咕咀呻呷咄咒咆呼咐呱呶和咚呢周咋命咎固垃坷坪坩坡坦坤坼夜奉奇奈奄奔妾妻委妹妮姑姆姐姍始姓姊妯妳姒姅孟孤季宗定官宜宙宛尚屈居"],["a9a1","屆岷岡岸岩岫岱岳帘帚帖帕帛帑幸庚店府底庖延弦弧弩往征彿彼忝忠忽念忿怏怔怯怵怖怪怕怡性怩怫怛或戕房戾所承拉拌拄抿拂抹拒招披拓拔拋拈抨抽押拐拙拇拍抵拚抱拘拖拗拆抬拎放斧於旺昔易昌昆昂明昀昏昕昊"],["aa40","昇服朋杭枋枕東果杳杷枇枝林杯杰板枉松析杵枚枓杼杪杲欣武歧歿氓氛泣注泳沱泌泥河沽沾沼波沫法泓沸泄油況沮泗泅泱沿治泡泛泊沬泯泜泖泠"],["aaa1","炕炎炒炊炙爬爭爸版牧物狀狎狙狗狐玩玨玟玫玥甽疝疙疚的盂盲直知矽社祀祁秉秈空穹竺糾罔羌羋者肺肥肢肱股肫肩肴肪肯臥臾舍芳芝芙芭芽芟芹花芬芥芯芸芣芰芾芷虎虱初表軋迎返近邵邸邱邶采金長門阜陀阿阻附"],["ab40","陂隹雨青非亟亭亮信侵侯便俠俑俏保促侶俘俟俊俗侮俐俄係俚俎俞侷兗冒冑冠剎剃削前剌剋則勇勉勃勁匍南卻厚叛咬哀咨哎哉咸咦咳哇哂咽咪品"],["aba1","哄哈咯咫咱咻咩咧咿囿垂型垠垣垢城垮垓奕契奏奎奐姜姘姿姣姨娃姥姪姚姦威姻孩宣宦室客宥封屎屏屍屋峙峒巷帝帥帟幽庠度建弈弭彥很待徊律徇後徉怒思怠急怎怨恍恰恨恢恆恃恬恫恪恤扁拜挖按拼拭持拮拽指拱拷"],["ac40","拯括拾拴挑挂政故斫施既春昭映昧是星昨昱昤曷柿染柱柔某柬架枯柵柩柯柄柑枴柚查枸柏柞柳枰柙柢柝柒歪殃殆段毒毗氟泉洋洲洪流津洌洱洞洗"],["aca1","活洽派洶洛泵洹洧洸洩洮洵洎洫炫為炳炬炯炭炸炮炤爰牲牯牴狩狠狡玷珊玻玲珍珀玳甚甭畏界畎畋疫疤疥疢疣癸皆皇皈盈盆盃盅省盹相眉看盾盼眇矜砂研砌砍祆祉祈祇禹禺科秒秋穿突竿竽籽紂紅紀紉紇約紆缸美羿耄"],["ad40","耐耍耑耶胖胥胚胃胄背胡胛胎胞胤胝致舢苧范茅苣苛苦茄若茂茉苒苗英茁苜苔苑苞苓苟苯茆虐虹虻虺衍衫要觔計訂訃貞負赴赳趴軍軌述迦迢迪迥"],["ada1","迭迫迤迨郊郎郁郃酋酊重閂限陋陌降面革韋韭音頁風飛食首香乘亳倌倍倣俯倦倥俸倩倖倆值借倚倒們俺倀倔倨俱倡個候倘俳修倭倪俾倫倉兼冤冥冢凍凌准凋剖剜剔剛剝匪卿原厝叟哨唐唁唷哼哥哲唆哺唔哩哭員唉哮哪"],["ae40","哦唧唇哽唏圃圄埂埔埋埃堉夏套奘奚娑娘娜娟娛娓姬娠娣娩娥娌娉孫屘宰害家宴宮宵容宸射屑展屐峭峽峻峪峨峰島崁峴差席師庫庭座弱徒徑徐恙"],["aea1","恣恥恐恕恭恩息悄悟悚悍悔悌悅悖扇拳挈拿捎挾振捕捂捆捏捉挺捐挽挪挫挨捍捌效敉料旁旅時晉晏晃晒晌晅晁書朔朕朗校核案框桓根桂桔栩梳栗桌桑栽柴桐桀格桃株桅栓栘桁殊殉殷氣氧氨氦氤泰浪涕消涇浦浸海浙涓"],["af40","浬涉浮浚浴浩涌涊浹涅浥涔烊烘烤烙烈烏爹特狼狹狽狸狷玆班琉珮珠珪珞畔畝畜畚留疾病症疲疳疽疼疹痂疸皋皰益盍盎眩真眠眨矩砰砧砸砝破砷"],["afa1","砥砭砠砟砲祕祐祠祟祖神祝祗祚秤秣秧租秦秩秘窄窈站笆笑粉紡紗紋紊素索純紐紕級紜納紙紛缺罟羔翅翁耆耘耕耙耗耽耿胱脂胰脅胭胴脆胸胳脈能脊胼胯臭臬舀舐航舫舨般芻茫荒荔荊茸荐草茵茴荏茲茹茶茗荀茱茨荃"],["b040","虔蚊蚪蚓蚤蚩蚌蚣蚜衰衷袁袂衽衹記訐討訌訕訊託訓訖訏訑豈豺豹財貢起躬軒軔軏辱送逆迷退迺迴逃追逅迸邕郡郝郢酒配酌釘針釗釜釙閃院陣陡"],["b0a1","陛陝除陘陞隻飢馬骨高鬥鬲鬼乾偺偽停假偃偌做偉健偶偎偕偵側偷偏倏偯偭兜冕凰剪副勒務勘動匐匏匙匿區匾參曼商啪啦啄啞啡啃啊唱啖問啕唯啤唸售啜唬啣唳啁啗圈國圉域堅堊堆埠埤基堂堵執培夠奢娶婁婉婦婪婀"],["b140","娼婢婚婆婊孰寇寅寄寂宿密尉專將屠屜屝崇崆崎崛崖崢崑崩崔崙崤崧崗巢常帶帳帷康庸庶庵庾張強彗彬彩彫得徙從徘御徠徜恿患悉悠您惋悴惦悽"],["b1a1","情悻悵惜悼惘惕惆惟悸惚惇戚戛扈掠控捲掖探接捷捧掘措捱掩掉掃掛捫推掄授掙採掬排掏掀捻捩捨捺敝敖救教敗啟敏敘敕敔斜斛斬族旋旌旎晝晚晤晨晦晞曹勗望梁梯梢梓梵桿桶梱梧梗械梃棄梭梆梅梔條梨梟梡梂欲殺"],["b240","毫毬氫涎涼淳淙液淡淌淤添淺清淇淋涯淑涮淞淹涸混淵淅淒渚涵淚淫淘淪深淮淨淆淄涪淬涿淦烹焉焊烽烯爽牽犁猜猛猖猓猙率琅琊球理現琍瓠瓶"],["b2a1","瓷甜產略畦畢異疏痔痕疵痊痍皎盔盒盛眷眾眼眶眸眺硫硃硎祥票祭移窒窕笠笨笛第符笙笞笮粒粗粕絆絃統紮紹紼絀細紳組累終紲紱缽羞羚翌翎習耜聊聆脯脖脣脫脩脰脤舂舵舷舶船莎莞莘荸莢莖莽莫莒莊莓莉莠荷荻荼"],["b340","莆莧處彪蛇蛀蚶蛄蚵蛆蛋蚱蚯蛉術袞袈被袒袖袍袋覓規訪訝訣訥許設訟訛訢豉豚販責貫貨貪貧赧赦趾趺軛軟這逍通逗連速逝逐逕逞造透逢逖逛途"],["b3a1","部郭都酗野釵釦釣釧釭釩閉陪陵陳陸陰陴陶陷陬雀雪雩章竟頂頃魚鳥鹵鹿麥麻傢傍傅備傑傀傖傘傚最凱割剴創剩勞勝勛博厥啻喀喧啼喊喝喘喂喜喪喔喇喋喃喳單喟唾喲喚喻喬喱啾喉喫喙圍堯堪場堤堰報堡堝堠壹壺奠"],["b440","婷媚婿媒媛媧孳孱寒富寓寐尊尋就嵌嵐崴嵇巽幅帽幀幃幾廊廁廂廄弼彭復循徨惑惡悲悶惠愜愣惺愕惰惻惴慨惱愎惶愉愀愒戟扉掣掌描揀揩揉揆揍"],["b4a1","插揣提握揖揭揮捶援揪換摒揚揹敞敦敢散斑斐斯普晰晴晶景暑智晾晷曾替期朝棺棕棠棘棗椅棟棵森棧棹棒棲棣棋棍植椒椎棉棚楮棻款欺欽殘殖殼毯氮氯氬港游湔渡渲湧湊渠渥渣減湛湘渤湖湮渭渦湯渴湍渺測湃渝渾滋"],["b540","溉渙湎湣湄湲湩湟焙焚焦焰無然煮焜牌犄犀猶猥猴猩琺琪琳琢琥琵琶琴琯琛琦琨甥甦畫番痢痛痣痙痘痞痠登發皖皓皴盜睏短硝硬硯稍稈程稅稀窘"],["b5a1","窗窖童竣等策筆筐筒答筍筋筏筑粟粥絞結絨絕紫絮絲絡給絢絰絳善翔翕耋聒肅腕腔腋腑腎脹腆脾腌腓腴舒舜菩萃菸萍菠菅萋菁華菱菴著萊菰萌菌菽菲菊萸萎萄菜萇菔菟虛蛟蛙蛭蛔蛛蛤蛐蛞街裁裂袱覃視註詠評詞証詁"],["b640","詔詛詐詆訴診訶詖象貂貯貼貳貽賁費賀貴買貶貿貸越超趁跎距跋跚跑跌跛跆軻軸軼辜逮逵週逸進逶鄂郵鄉郾酣酥量鈔鈕鈣鈉鈞鈍鈐鈇鈑閔閏開閑"],["b6a1","間閒閎隊階隋陽隅隆隍陲隄雁雅雄集雇雯雲韌項順須飧飪飯飩飲飭馮馭黃黍黑亂傭債傲傳僅傾催傷傻傯僇剿剷剽募勦勤勢勣匯嗟嗨嗓嗦嗎嗜嗇嗑嗣嗤嗯嗚嗡嗅嗆嗥嗉園圓塞塑塘塗塚塔填塌塭塊塢塒塋奧嫁嫉嫌媾媽媼"],["b740","媳嫂媲嵩嵯幌幹廉廈弒彙徬微愚意慈感想愛惹愁愈慎慌慄慍愾愴愧愍愆愷戡戢搓搾搞搪搭搽搬搏搜搔損搶搖搗搆敬斟新暗暉暇暈暖暄暘暍會榔業"],["b7a1","楚楷楠楔極椰概楊楨楫楞楓楹榆楝楣楛歇歲毀殿毓毽溢溯滓溶滂源溝滇滅溥溘溼溺溫滑準溜滄滔溪溧溴煎煙煩煤煉照煜煬煦煌煥煞煆煨煖爺牒猷獅猿猾瑯瑚瑕瑟瑞瑁琿瑙瑛瑜當畸瘀痰瘁痲痱痺痿痴痳盞盟睛睫睦睞督"],["b840","睹睪睬睜睥睨睢矮碎碰碗碘碌碉硼碑碓硿祺祿禁萬禽稜稚稠稔稟稞窟窠筷節筠筮筧粱粳粵經絹綑綁綏絛置罩罪署義羨群聖聘肆肄腱腰腸腥腮腳腫"],["b8a1","腹腺腦舅艇蒂葷落萱葵葦葫葉葬葛萼萵葡董葩葭葆虞虜號蛹蜓蜈蜇蜀蛾蛻蜂蜃蜆蜊衙裟裔裙補裘裝裡裊裕裒覜解詫該詳試詩詰誇詼詣誠話誅詭詢詮詬詹詻訾詨豢貊貉賊資賈賄貲賃賂賅跡跟跨路跳跺跪跤跦躲較載軾輊"],["b940","辟農運遊道遂達逼違遐遇遏過遍遑逾遁鄒鄗酬酪酩釉鈷鉗鈸鈽鉀鈾鉛鉋鉤鉑鈴鉉鉍鉅鈹鈿鉚閘隘隔隕雍雋雉雊雷電雹零靖靴靶預頑頓頊頒頌飼飴"],["b9a1","飽飾馳馱馴髡鳩麂鼎鼓鼠僧僮僥僖僭僚僕像僑僱僎僩兢凳劃劂匱厭嗾嘀嘛嘗嗽嘔嘆嘉嘍嘎嗷嘖嘟嘈嘐嗶團圖塵塾境墓墊塹墅塽壽夥夢夤奪奩嫡嫦嫩嫗嫖嫘嫣孵寞寧寡寥實寨寢寤察對屢嶄嶇幛幣幕幗幔廓廖弊彆彰徹慇"],["ba40","愿態慷慢慣慟慚慘慵截撇摘摔撤摸摟摺摑摧搴摭摻敲斡旗旖暢暨暝榜榨榕槁榮槓構榛榷榻榫榴槐槍榭槌榦槃榣歉歌氳漳演滾漓滴漩漾漠漬漏漂漢"],["baa1","滿滯漆漱漸漲漣漕漫漯澈漪滬漁滲滌滷熔熙煽熊熄熒爾犒犖獄獐瑤瑣瑪瑰瑭甄疑瘧瘍瘋瘉瘓盡監瞄睽睿睡磁碟碧碳碩碣禎福禍種稱窪窩竭端管箕箋筵算箝箔箏箸箇箄粹粽精綻綰綜綽綾綠緊綴網綱綺綢綿綵綸維緒緇綬"],["bb40","罰翠翡翟聞聚肇腐膀膏膈膊腿膂臧臺與舔舞艋蓉蒿蓆蓄蒙蒞蒲蒜蓋蒸蓀蓓蒐蒼蓑蓊蜿蜜蜻蜢蜥蜴蜘蝕蜷蜩裳褂裴裹裸製裨褚裯誦誌語誣認誡誓誤"],["bba1","說誥誨誘誑誚誧豪貍貌賓賑賒赫趙趕跼輔輒輕輓辣遠遘遜遣遙遞遢遝遛鄙鄘鄞酵酸酷酴鉸銀銅銘銖鉻銓銜銨鉼銑閡閨閩閣閥閤隙障際雌雒需靼鞅韶頗領颯颱餃餅餌餉駁骯骰髦魁魂鳴鳶鳳麼鼻齊億儀僻僵價儂儈儉儅凜"],["bc40","劇劈劉劍劊勰厲嘮嘻嘹嘲嘿嘴嘩噓噎噗噴嘶嘯嘰墀墟增墳墜墮墩墦奭嬉嫻嬋嫵嬌嬈寮寬審寫層履嶝嶔幢幟幡廢廚廟廝廣廠彈影德徵慶慧慮慝慕憂"],["bca1","慼慰慫慾憧憐憫憎憬憚憤憔憮戮摩摯摹撞撲撈撐撰撥撓撕撩撒撮播撫撚撬撙撢撳敵敷數暮暫暴暱樣樟槨樁樞標槽模樓樊槳樂樅槭樑歐歎殤毅毆漿潼澄潑潦潔澆潭潛潸潮澎潺潰潤澗潘滕潯潠潟熟熬熱熨牖犛獎獗瑩璋璃"],["bd40","瑾璀畿瘠瘩瘟瘤瘦瘡瘢皚皺盤瞎瞇瞌瞑瞋磋磅確磊碾磕碼磐稿稼穀稽稷稻窯窮箭箱範箴篆篇篁箠篌糊締練緯緻緘緬緝編緣線緞緩綞緙緲緹罵罷羯"],["bda1","翩耦膛膜膝膠膚膘蔗蔽蔚蓮蔬蔭蔓蔑蔣蔡蔔蓬蔥蓿蔆螂蝴蝶蝠蝦蝸蝨蝙蝗蝌蝓衛衝褐複褒褓褕褊誼諒談諄誕請諸課諉諂調誰論諍誶誹諛豌豎豬賠賞賦賤賬賭賢賣賜質賡赭趟趣踫踐踝踢踏踩踟踡踞躺輝輛輟輩輦輪輜輞"],["be40","輥適遮遨遭遷鄰鄭鄧鄱醇醉醋醃鋅銻銷鋪銬鋤鋁銳銼鋒鋇鋰銲閭閱霄霆震霉靠鞍鞋鞏頡頫頜颳養餓餒餘駝駐駟駛駑駕駒駙骷髮髯鬧魅魄魷魯鴆鴉"],["bea1","鴃麩麾黎墨齒儒儘儔儐儕冀冪凝劑劓勳噙噫噹噩噤噸噪器噥噱噯噬噢噶壁墾壇壅奮嬝嬴學寰導彊憲憑憩憊懍憶憾懊懈戰擅擁擋撻撼據擄擇擂操撿擒擔撾整曆曉暹曄曇暸樽樸樺橙橫橘樹橄橢橡橋橇樵機橈歙歷氅濂澱澡"],["bf40","濃澤濁澧澳激澹澶澦澠澴熾燉燐燒燈燕熹燎燙燜燃燄獨璜璣璘璟璞瓢甌甍瘴瘸瘺盧盥瞠瞞瞟瞥磨磚磬磧禦積穎穆穌穋窺篙簑築篤篛篡篩篦糕糖縊"],["bfa1","縑縈縛縣縞縝縉縐罹羲翰翱翮耨膳膩膨臻興艘艙蕊蕙蕈蕨蕩蕃蕉蕭蕪蕞螃螟螞螢融衡褪褲褥褫褡親覦諦諺諫諱謀諜諧諮諾謁謂諷諭諳諶諼豫豭貓賴蹄踱踴蹂踹踵輻輯輸輳辨辦遵遴選遲遼遺鄴醒錠錶鋸錳錯錢鋼錫錄錚"],["c040","錐錦錡錕錮錙閻隧隨險雕霎霑霖霍霓霏靛靜靦鞘頰頸頻頷頭頹頤餐館餞餛餡餚駭駢駱骸骼髻髭鬨鮑鴕鴣鴦鴨鴒鴛默黔龍龜優償儡儲勵嚎嚀嚐嚅嚇"],["c0a1","嚏壕壓壑壎嬰嬪嬤孺尷屨嶼嶺嶽嶸幫彌徽應懂懇懦懋戲戴擎擊擘擠擰擦擬擱擢擭斂斃曙曖檀檔檄檢檜櫛檣橾檗檐檠歜殮毚氈濘濱濟濠濛濤濫濯澀濬濡濩濕濮濰燧營燮燦燥燭燬燴燠爵牆獰獲璩環璦璨癆療癌盪瞳瞪瞰瞬"],["c140","瞧瞭矯磷磺磴磯礁禧禪穗窿簇簍篾篷簌篠糠糜糞糢糟糙糝縮績繆縷縲繃縫總縱繅繁縴縹繈縵縿縯罄翳翼聱聲聰聯聳臆臃膺臂臀膿膽臉膾臨舉艱薪"],["c1a1","薄蕾薜薑薔薯薛薇薨薊虧蟀蟑螳蟒蟆螫螻螺蟈蟋褻褶襄褸褽覬謎謗謙講謊謠謝謄謐豁谿豳賺賽購賸賻趨蹉蹋蹈蹊轄輾轂轅輿避遽還邁邂邀鄹醣醞醜鍍鎂錨鍵鍊鍥鍋錘鍾鍬鍛鍰鍚鍔闊闋闌闈闆隱隸雖霜霞鞠韓顆颶餵騁"],["c240","駿鮮鮫鮪鮭鴻鴿麋黏點黜黝黛鼾齋叢嚕嚮壙壘嬸彝懣戳擴擲擾攆擺擻擷斷曜朦檳檬櫃檻檸櫂檮檯歟歸殯瀉瀋濾瀆濺瀑瀏燻燼燾燸獷獵璧璿甕癖癘"],["c2a1","癒瞽瞿瞻瞼礎禮穡穢穠竄竅簫簧簪簞簣簡糧織繕繞繚繡繒繙罈翹翻職聶臍臏舊藏薩藍藐藉薰薺薹薦蟯蟬蟲蟠覆覲觴謨謹謬謫豐贅蹙蹣蹦蹤蹟蹕軀轉轍邇邃邈醫醬釐鎔鎊鎖鎢鎳鎮鎬鎰鎘鎚鎗闔闖闐闕離雜雙雛雞霤鞣鞦"],["c340","鞭韹額顏題顎顓颺餾餿餽餮馥騎髁鬃鬆魏魎魍鯊鯉鯽鯈鯀鵑鵝鵠黠鼕鼬儳嚥壞壟壢寵龐廬懲懷懶懵攀攏曠曝櫥櫝櫚櫓瀛瀟瀨瀚瀝瀕瀘爆爍牘犢獸"],["c3a1","獺璽瓊瓣疇疆癟癡矇礙禱穫穩簾簿簸簽簷籀繫繭繹繩繪羅繳羶羹羸臘藩藝藪藕藤藥藷蟻蠅蠍蟹蟾襠襟襖襞譁譜識證譚譎譏譆譙贈贊蹼蹲躇蹶蹬蹺蹴轔轎辭邊邋醱醮鏡鏑鏟鏃鏈鏜鏝鏖鏢鏍鏘鏤鏗鏨關隴難霪霧靡韜韻類"],["c440","願顛颼饅饉騖騙鬍鯨鯧鯖鯛鶉鵡鵲鵪鵬麒麗麓麴勸嚨嚷嚶嚴嚼壤孀孃孽寶巉懸懺攘攔攙曦朧櫬瀾瀰瀲爐獻瓏癢癥礦礪礬礫竇競籌籃籍糯糰辮繽繼"],["c4a1","纂罌耀臚艦藻藹蘑藺蘆蘋蘇蘊蠔蠕襤覺觸議譬警譯譟譫贏贍躉躁躅躂醴釋鐘鐃鏽闡霰飄饒饑馨騫騰騷騵鰓鰍鹹麵黨鼯齟齣齡儷儸囁囀囂夔屬巍懼懾攝攜斕曩櫻欄櫺殲灌爛犧瓖瓔癩矓籐纏續羼蘗蘭蘚蠣蠢蠡蠟襪襬覽譴"],["c540","護譽贓躊躍躋轟辯醺鐮鐳鐵鐺鐸鐲鐫闢霸霹露響顧顥饗驅驃驀騾髏魔魑鰭鰥鶯鶴鷂鶸麝黯鼙齜齦齧儼儻囈囊囉孿巔巒彎懿攤權歡灑灘玀瓤疊癮癬"],["c5a1","禳籠籟聾聽臟襲襯觼讀贖贗躑躓轡酈鑄鑑鑒霽霾韃韁顫饕驕驍髒鬚鱉鰱鰾鰻鷓鷗鼴齬齪龔囌巖戀攣攫攪曬欐瓚竊籤籣籥纓纖纔臢蘸蘿蠱變邐邏鑣鑠鑤靨顯饜驚驛驗髓體髑鱔鱗鱖鷥麟黴囑壩攬灞癱癲矗罐羈蠶蠹衢讓讒"],["c640","讖艷贛釀鑪靂靈靄韆顰驟鬢魘鱟鷹鷺鹼鹽鼇齷齲廳欖灣籬籮蠻觀躡釁鑲鑰顱饞髖鬣黌灤矚讚鑷韉驢驥纜讜躪釅鑽鑾鑼鱷鱸黷豔鑿鸚爨驪鬱鸛鸞籲"],["c940","乂乜凵匚厂万丌乇亍囗兀屮彳丏冇与丮亓仂仉仈冘勼卬厹圠夃夬尐巿旡殳毌气爿丱丼仨仜仩仡仝仚刌匜卌圢圣夗夯宁宄尒尻屴屳帄庀庂忉戉扐氕"],["c9a1","氶汃氿氻犮犰玊禸肊阞伎优伬仵伔仱伀价伈伝伂伅伢伓伄仴伒冱刓刉刐劦匢匟卍厊吇囡囟圮圪圴夼妀奼妅奻奾奷奿孖尕尥屼屺屻屾巟幵庄异弚彴忕忔忏扜扞扤扡扦扢扙扠扚扥旯旮朾朹朸朻机朿朼朳氘汆汒汜汏汊汔汋"],["ca40","汌灱牞犴犵玎甪癿穵网艸艼芀艽艿虍襾邙邗邘邛邔阢阤阠阣佖伻佢佉体佤伾佧佒佟佁佘伭伳伿佡冏冹刜刞刡劭劮匉卣卲厎厏吰吷吪呔呅吙吜吥吘"],["caa1","吽呏呁吨吤呇囮囧囥坁坅坌坉坋坒夆奀妦妘妠妗妎妢妐妏妧妡宎宒尨尪岍岏岈岋岉岒岊岆岓岕巠帊帎庋庉庌庈庍弅弝彸彶忒忑忐忭忨忮忳忡忤忣忺忯忷忻怀忴戺抃抌抎抏抔抇扱扻扺扰抁抈扷扽扲扴攷旰旴旳旲旵杅杇"],["cb40","杙杕杌杈杝杍杚杋毐氙氚汸汧汫沄沋沏汱汯汩沚汭沇沕沜汦汳汥汻沎灴灺牣犿犽狃狆狁犺狅玕玗玓玔玒町甹疔疕皁礽耴肕肙肐肒肜芐芏芅芎芑芓"],["cba1","芊芃芄豸迉辿邟邡邥邞邧邠阰阨阯阭丳侘佼侅佽侀侇佶佴侉侄佷佌侗佪侚佹侁佸侐侜侔侞侒侂侕佫佮冞冼冾刵刲刳剆刱劼匊匋匼厒厔咇呿咁咑咂咈呫呺呾呥呬呴呦咍呯呡呠咘呣呧呤囷囹坯坲坭坫坱坰坶垀坵坻坳坴坢"],["cc40","坨坽夌奅妵妺姏姎妲姌姁妶妼姃姖妱妽姀姈妴姇孢孥宓宕屄屇岮岤岠岵岯岨岬岟岣岭岢岪岧岝岥岶岰岦帗帔帙弨弢弣弤彔徂彾彽忞忥怭怦怙怲怋"],["cca1","怴怊怗怳怚怞怬怢怍怐怮怓怑怌怉怜戔戽抭抴拑抾抪抶拊抮抳抯抻抩抰抸攽斨斻昉旼昄昒昈旻昃昋昍昅旽昑昐曶朊枅杬枎枒杶杻枘枆构杴枍枌杺枟枑枙枃杽极杸杹枔欥殀歾毞氝沓泬泫泮泙沶泔沭泧沷泐泂沺泃泆泭泲"],["cd40","泒泝沴沊沝沀泞泀洰泍泇沰泹泏泩泑炔炘炅炓炆炄炑炖炂炚炃牪狖狋狘狉狜狒狔狚狌狑玤玡玭玦玢玠玬玝瓝瓨甿畀甾疌疘皯盳盱盰盵矸矼矹矻矺"],["cda1","矷祂礿秅穸穻竻籵糽耵肏肮肣肸肵肭舠芠苀芫芚芘芛芵芧芮芼芞芺芴芨芡芩苂芤苃芶芢虰虯虭虮豖迒迋迓迍迖迕迗邲邴邯邳邰阹阽阼阺陃俍俅俓侲俉俋俁俔俜俙侻侳俛俇俖侺俀侹俬剄剉勀勂匽卼厗厖厙厘咺咡咭咥哏"],["ce40","哃茍咷咮哖咶哅哆咠呰咼咢咾呲哞咰垵垞垟垤垌垗垝垛垔垘垏垙垥垚垕壴复奓姡姞姮娀姱姝姺姽姼姶姤姲姷姛姩姳姵姠姾姴姭宨屌峐峘峌峗峋峛"],["cea1","峞峚峉峇峊峖峓峔峏峈峆峎峟峸巹帡帢帣帠帤庰庤庢庛庣庥弇弮彖徆怷怹恔恲恞恅恓恇恉恛恌恀恂恟怤恄恘恦恮扂扃拏挍挋拵挎挃拫拹挏挌拸拶挀挓挔拺挕拻拰敁敃斪斿昶昡昲昵昜昦昢昳昫昺昝昴昹昮朏朐柁柲柈枺"],["cf40","柜枻柸柘柀枷柅柫柤柟枵柍枳柷柶柮柣柂枹柎柧柰枲柼柆柭柌枮柦柛柺柉柊柃柪柋欨殂殄殶毖毘毠氠氡洨洴洭洟洼洿洒洊泚洳洄洙洺洚洑洀洝浂"],["cfa1","洁洘洷洃洏浀洇洠洬洈洢洉洐炷炟炾炱炰炡炴炵炩牁牉牊牬牰牳牮狊狤狨狫狟狪狦狣玅珌珂珈珅玹玶玵玴珫玿珇玾珃珆玸珋瓬瓮甮畇畈疧疪癹盄眈眃眄眅眊盷盻盺矧矨砆砑砒砅砐砏砎砉砃砓祊祌祋祅祄秕种秏秖秎窀"],["d040","穾竑笀笁籺籸籹籿粀粁紃紈紁罘羑羍羾耇耎耏耔耷胘胇胠胑胈胂胐胅胣胙胜胊胕胉胏胗胦胍臿舡芔苙苾苹茇苨茀苕茺苫苖苴苬苡苲苵茌苻苶苰苪"],["d0a1","苤苠苺苳苭虷虴虼虳衁衎衧衪衩觓訄訇赲迣迡迮迠郱邽邿郕郅邾郇郋郈釔釓陔陏陑陓陊陎倞倅倇倓倢倰倛俵俴倳倷倬俶俷倗倜倠倧倵倯倱倎党冔冓凊凄凅凈凎剡剚剒剞剟剕剢勍匎厞唦哢唗唒哧哳哤唚哿唄唈哫唑唅哱"],["d140","唊哻哷哸哠唎唃唋圁圂埌堲埕埒垺埆垽垼垸垶垿埇埐垹埁夎奊娙娖娭娮娕娏娗娊娞娳孬宧宭宬尃屖屔峬峿峮峱峷崀峹帩帨庨庮庪庬弳弰彧恝恚恧"],["d1a1","恁悢悈悀悒悁悝悃悕悛悗悇悜悎戙扆拲挐捖挬捄捅挶捃揤挹捋捊挼挩捁挴捘捔捙挭捇挳捚捑挸捗捀捈敊敆旆旃旄旂晊晟晇晑朒朓栟栚桉栲栳栻桋桏栖栱栜栵栫栭栯桎桄栴栝栒栔栦栨栮桍栺栥栠欬欯欭欱欴歭肂殈毦毤"],["d240","毨毣毢毧氥浺浣浤浶洍浡涒浘浢浭浯涑涍淯浿涆浞浧浠涗浰浼浟涂涘洯浨涋浾涀涄洖涃浻浽浵涐烜烓烑烝烋缹烢烗烒烞烠烔烍烅烆烇烚烎烡牂牸"],["d2a1","牷牶猀狺狴狾狶狳狻猁珓珙珥珖玼珧珣珩珜珒珛珔珝珚珗珘珨瓞瓟瓴瓵甡畛畟疰痁疻痄痀疿疶疺皊盉眝眛眐眓眒眣眑眕眙眚眢眧砣砬砢砵砯砨砮砫砡砩砳砪砱祔祛祏祜祓祒祑秫秬秠秮秭秪秜秞秝窆窉窅窋窌窊窇竘笐"],["d340","笄笓笅笏笈笊笎笉笒粄粑粊粌粈粍粅紞紝紑紎紘紖紓紟紒紏紌罜罡罞罠罝罛羖羒翃翂翀耖耾耹胺胲胹胵脁胻脀舁舯舥茳茭荄茙荑茥荖茿荁茦茜茢"],["d3a1","荂荎茛茪茈茼荍茖茤茠茷茯茩荇荅荌荓茞茬荋茧荈虓虒蚢蚨蚖蚍蚑蚞蚇蚗蚆蚋蚚蚅蚥蚙蚡蚧蚕蚘蚎蚝蚐蚔衃衄衭衵衶衲袀衱衿衯袃衾衴衼訒豇豗豻貤貣赶赸趵趷趶軑軓迾迵适迿迻逄迼迶郖郠郙郚郣郟郥郘郛郗郜郤酐"],["d440","酎酏釕釢釚陜陟隼飣髟鬯乿偰偪偡偞偠偓偋偝偲偈偍偁偛偊偢倕偅偟偩偫偣偤偆偀偮偳偗偑凐剫剭剬剮勖勓匭厜啵啶唼啍啐唴唪啑啢唶唵唰啒啅"],["d4a1","唌唲啥啎唹啈唭唻啀啋圊圇埻堔埢埶埜埴堀埭埽堈埸堋埳埏堇埮埣埲埥埬埡堎埼堐埧堁堌埱埩埰堍堄奜婠婘婕婧婞娸娵婭婐婟婥婬婓婤婗婃婝婒婄婛婈媎娾婍娹婌婰婩婇婑婖婂婜孲孮寁寀屙崞崋崝崚崠崌崨崍崦崥崏"],["d540","崰崒崣崟崮帾帴庱庴庹庲庳弶弸徛徖徟悊悐悆悾悰悺惓惔惏惤惙惝惈悱惛悷惊悿惃惍惀挲捥掊掂捽掽掞掭掝掗掫掎捯掇掐据掯捵掜捭掮捼掤挻掟"],["d5a1","捸掅掁掑掍捰敓旍晥晡晛晙晜晢朘桹梇梐梜桭桮梮梫楖桯梣梬梩桵桴梲梏桷梒桼桫桲梪梀桱桾梛梖梋梠梉梤桸桻梑梌梊桽欶欳欷欸殑殏殍殎殌氪淀涫涴涳湴涬淩淢涷淶淔渀淈淠淟淖涾淥淜淝淛淴淊涽淭淰涺淕淂淏淉"],["d640","淐淲淓淽淗淍淣涻烺焍烷焗烴焌烰焄烳焐烼烿焆焓焀烸烶焋焂焎牾牻牼牿猝猗猇猑猘猊猈狿猏猞玈珶珸珵琄琁珽琇琀珺珼珿琌琋珴琈畤畣痎痒痏"],["d6a1","痋痌痑痐皏皉盓眹眯眭眱眲眴眳眽眥眻眵硈硒硉硍硊硌砦硅硐祤祧祩祪祣祫祡离秺秸秶秷窏窔窐笵筇笴笥笰笢笤笳笘笪笝笱笫笭笯笲笸笚笣粔粘粖粣紵紽紸紶紺絅紬紩絁絇紾紿絊紻紨罣羕羜羝羛翊翋翍翐翑翇翏翉耟"],["d740","耞耛聇聃聈脘脥脙脛脭脟脬脞脡脕脧脝脢舑舸舳舺舴舲艴莐莣莨莍荺荳莤荴莏莁莕莙荵莔莩荽莃莌莝莛莪莋荾莥莯莈莗莰荿莦莇莮荶莚虙虖蚿蚷"],["d7a1","蛂蛁蛅蚺蚰蛈蚹蚳蚸蛌蚴蚻蚼蛃蚽蚾衒袉袕袨袢袪袚袑袡袟袘袧袙袛袗袤袬袌袓袎覂觖觙觕訰訧訬訞谹谻豜豝豽貥赽赻赹趼跂趹趿跁軘軞軝軜軗軠軡逤逋逑逜逌逡郯郪郰郴郲郳郔郫郬郩酖酘酚酓酕釬釴釱釳釸釤釹釪"],["d840","釫釷釨釮镺閆閈陼陭陫陱陯隿靪頄飥馗傛傕傔傞傋傣傃傌傎傝偨傜傒傂傇兟凔匒匑厤厧喑喨喥喭啷噅喢喓喈喏喵喁喣喒喤啽喌喦啿喕喡喎圌堩堷"],["d8a1","堙堞堧堣堨埵塈堥堜堛堳堿堶堮堹堸堭堬堻奡媯媔媟婺媢媞婸媦婼媥媬媕媮娷媄媊媗媃媋媩婻婽媌媜媏媓媝寪寍寋寔寑寊寎尌尰崷嵃嵫嵁嵋崿崵嵑嵎嵕崳崺嵒崽崱嵙嵂崹嵉崸崼崲崶嵀嵅幄幁彘徦徥徫惉悹惌惢惎惄愔"],["d940","惲愊愖愅惵愓惸惼惾惁愃愘愝愐惿愄愋扊掔掱掰揎揥揨揯揃撝揳揊揠揶揕揲揵摡揟掾揝揜揄揘揓揂揇揌揋揈揰揗揙攲敧敪敤敜敨敥斌斝斞斮旐旒"],["d9a1","晼晬晻暀晱晹晪晲朁椌棓椄棜椪棬棪棱椏棖棷棫棤棶椓椐棳棡椇棌椈楰梴椑棯棆椔棸棐棽棼棨椋椊椗棎棈棝棞棦棴棑椆棔棩椕椥棇欹欻欿欼殔殗殙殕殽毰毲毳氰淼湆湇渟湉溈渼渽湅湢渫渿湁湝湳渜渳湋湀湑渻渃渮湞"],["da40","湨湜湡渱渨湠湱湫渹渢渰湓湥渧湸湤湷湕湹湒湦渵渶湚焠焞焯烻焮焱焣焥焢焲焟焨焺焛牋牚犈犉犆犅犋猒猋猰猢猱猳猧猲猭猦猣猵猌琮琬琰琫琖"],["daa1","琚琡琭琱琤琣琝琩琠琲瓻甯畯畬痧痚痡痦痝痟痤痗皕皒盚睆睇睄睍睅睊睎睋睌矞矬硠硤硥硜硭硱硪确硰硩硨硞硢祴祳祲祰稂稊稃稌稄窙竦竤筊笻筄筈筌筎筀筘筅粢粞粨粡絘絯絣絓絖絧絪絏絭絜絫絒絔絩絑絟絎缾缿罥"],["db40","罦羢羠羡翗聑聏聐胾胔腃腊腒腏腇脽腍脺臦臮臷臸臹舄舼舽舿艵茻菏菹萣菀菨萒菧菤菼菶萐菆菈菫菣莿萁菝菥菘菿菡菋菎菖菵菉萉萏菞萑萆菂菳"],["dba1","菕菺菇菑菪萓菃菬菮菄菻菗菢萛菛菾蛘蛢蛦蛓蛣蛚蛪蛝蛫蛜蛬蛩蛗蛨蛑衈衖衕袺裗袹袸裀袾袶袼袷袽袲褁裉覕覘覗觝觚觛詎詍訹詙詀詗詘詄詅詒詈詑詊詌詏豟貁貀貺貾貰貹貵趄趀趉跘跓跍跇跖跜跏跕跙跈跗跅軯軷軺"],["dc40","軹軦軮軥軵軧軨軶軫軱軬軴軩逭逴逯鄆鄬鄄郿郼鄈郹郻鄁鄀鄇鄅鄃酡酤酟酢酠鈁鈊鈥鈃鈚鈦鈏鈌鈀鈒釿釽鈆鈄鈧鈂鈜鈤鈙鈗鈅鈖镻閍閌閐隇陾隈"],["dca1","隉隃隀雂雈雃雱雰靬靰靮頇颩飫鳦黹亃亄亶傽傿僆傮僄僊傴僈僂傰僁傺傱僋僉傶傸凗剺剸剻剼嗃嗛嗌嗐嗋嗊嗝嗀嗔嗄嗩喿嗒喍嗏嗕嗢嗖嗈嗲嗍嗙嗂圔塓塨塤塏塍塉塯塕塎塝塙塥塛堽塣塱壼嫇嫄嫋媺媸媱媵媰媿嫈媻嫆"],["dd40","媷嫀嫊媴媶嫍媹媐寖寘寙尟尳嵱嵣嵊嵥嵲嵬嵞嵨嵧嵢巰幏幎幊幍幋廅廌廆廋廇彀徯徭惷慉慊愫慅愶愲愮慆愯慏愩慀戠酨戣戥戤揅揱揫搐搒搉搠搤"],["dda1","搳摃搟搕搘搹搷搢搣搌搦搰搨摁搵搯搊搚摀搥搧搋揧搛搮搡搎敯斒旓暆暌暕暐暋暊暙暔晸朠楦楟椸楎楢楱椿楅楪椹楂楗楙楺楈楉椵楬椳椽楥棰楸椴楩楀楯楄楶楘楁楴楌椻楋椷楜楏楑椲楒椯楻椼歆歅歃歂歈歁殛嗀毻毼"],["de40","毹毷毸溛滖滈溏滀溟溓溔溠溱溹滆滒溽滁溞滉溷溰滍溦滏溲溾滃滜滘溙溒溎溍溤溡溿溳滐滊溗溮溣煇煔煒煣煠煁煝煢煲煸煪煡煂煘煃煋煰煟煐煓"],["dea1","煄煍煚牏犍犌犑犐犎猼獂猻猺獀獊獉瑄瑊瑋瑒瑑瑗瑀瑏瑐瑎瑂瑆瑍瑔瓡瓿瓾瓽甝畹畷榃痯瘏瘃痷痾痼痹痸瘐痻痶痭痵痽皙皵盝睕睟睠睒睖睚睩睧睔睙睭矠碇碚碔碏碄碕碅碆碡碃硹碙碀碖硻祼禂祽祹稑稘稙稒稗稕稢稓"],["df40","稛稐窣窢窞竫筦筤筭筴筩筲筥筳筱筰筡筸筶筣粲粴粯綈綆綀綍絿綅絺綎絻綃絼綌綔綄絽綒罭罫罧罨罬羦羥羧翛翜耡腤腠腷腜腩腛腢腲朡腞腶腧腯"],["dfa1","腄腡舝艉艄艀艂艅蓱萿葖葶葹蒏蒍葥葑葀蒆葧萰葍葽葚葙葴葳葝蔇葞萷萺萴葺葃葸萲葅萩菙葋萯葂萭葟葰萹葎葌葒葯蓅蒎萻葇萶萳葨葾葄萫葠葔葮葐蜋蜄蛷蜌蛺蛖蛵蝍蛸蜎蜉蜁蛶蜍蜅裖裋裍裎裞裛裚裌裐覅覛觟觥觤"],["e040","觡觠觢觜触詶誆詿詡訿詷誂誄詵誃誁詴詺谼豋豊豥豤豦貆貄貅賌赨赩趑趌趎趏趍趓趔趐趒跰跠跬跱跮跐跩跣跢跧跲跫跴輆軿輁輀輅輇輈輂輋遒逿"],["e0a1","遄遉逽鄐鄍鄏鄑鄖鄔鄋鄎酮酯鉈鉒鈰鈺鉦鈳鉥鉞銃鈮鉊鉆鉭鉬鉏鉠鉧鉯鈶鉡鉰鈱鉔鉣鉐鉲鉎鉓鉌鉖鈲閟閜閞閛隒隓隑隗雎雺雽雸雵靳靷靸靲頏頍頎颬飶飹馯馲馰馵骭骫魛鳪鳭鳧麀黽僦僔僗僨僳僛僪僝僤僓僬僰僯僣僠"],["e140","凘劀劁勩勫匰厬嘧嘕嘌嘒嗼嘏嘜嘁嘓嘂嗺嘝嘄嗿嗹墉塼墐墘墆墁塿塴墋塺墇墑墎塶墂墈塻墔墏壾奫嫜嫮嫥嫕嫪嫚嫭嫫嫳嫢嫠嫛嫬嫞嫝嫙嫨嫟孷寠"],["e1a1","寣屣嶂嶀嵽嶆嵺嶁嵷嶊嶉嶈嵾嵼嶍嵹嵿幘幙幓廘廑廗廎廜廕廙廒廔彄彃彯徶愬愨慁慞慱慳慒慓慲慬憀慴慔慺慛慥愻慪慡慖戩戧戫搫摍摛摝摴摶摲摳摽摵摦撦摎撂摞摜摋摓摠摐摿搿摬摫摙摥摷敳斠暡暠暟朅朄朢榱榶槉"],["e240","榠槎榖榰榬榼榑榙榎榧榍榩榾榯榿槄榽榤槔榹槊榚槏榳榓榪榡榞槙榗榐槂榵榥槆歊歍歋殞殟殠毃毄毾滎滵滱漃漥滸漷滻漮漉潎漙漚漧漘漻漒滭漊"],["e2a1","漶潳滹滮漭潀漰漼漵滫漇漎潃漅滽滶漹漜滼漺漟漍漞漈漡熇熐熉熀熅熂熏煻熆熁熗牄牓犗犕犓獃獍獑獌瑢瑳瑱瑵瑲瑧瑮甀甂甃畽疐瘖瘈瘌瘕瘑瘊瘔皸瞁睼瞅瞂睮瞀睯睾瞃碲碪碴碭碨硾碫碞碥碠碬碢碤禘禊禋禖禕禔禓"],["e340","禗禈禒禐稫穊稰稯稨稦窨窫窬竮箈箜箊箑箐箖箍箌箛箎箅箘劄箙箤箂粻粿粼粺綧綷緂綣綪緁緀緅綝緎緄緆緋緌綯綹綖綼綟綦綮綩綡緉罳翢翣翥翞"],["e3a1","耤聝聜膉膆膃膇膍膌膋舕蒗蒤蒡蒟蒺蓎蓂蒬蒮蒫蒹蒴蓁蓍蒪蒚蒱蓐蒝蒧蒻蒢蒔蓇蓌蒛蒩蒯蒨蓖蒘蒶蓏蒠蓗蓔蓒蓛蒰蒑虡蜳蜣蜨蝫蝀蜮蜞蜡蜙蜛蝃蜬蝁蜾蝆蜠蜲蜪蜭蜼蜒蜺蜱蜵蝂蜦蜧蜸蜤蜚蜰蜑裷裧裱裲裺裾裮裼裶裻"],["e440","裰裬裫覝覡覟覞觩觫觨誫誙誋誒誏誖谽豨豩賕賏賗趖踉踂跿踍跽踊踃踇踆踅跾踀踄輐輑輎輍鄣鄜鄠鄢鄟鄝鄚鄤鄡鄛酺酲酹酳銥銤鉶銛鉺銠銔銪銍"],["e4a1","銦銚銫鉹銗鉿銣鋮銎銂銕銢鉽銈銡銊銆銌銙銧鉾銇銩銝銋鈭隞隡雿靘靽靺靾鞃鞀鞂靻鞄鞁靿韎韍頖颭颮餂餀餇馝馜駃馹馻馺駂馽駇骱髣髧鬾鬿魠魡魟鳱鳲鳵麧僿儃儰僸儆儇僶僾儋儌僽儊劋劌勱勯噈噂噌嘵噁噊噉噆噘"],["e540","噚噀嘳嘽嘬嘾嘸嘪嘺圚墫墝墱墠墣墯墬墥墡壿嫿嫴嫽嫷嫶嬃嫸嬂嫹嬁嬇嬅嬏屧嶙嶗嶟嶒嶢嶓嶕嶠嶜嶡嶚嶞幩幝幠幜緳廛廞廡彉徲憋憃慹憱憰憢憉"],["e5a1","憛憓憯憭憟憒憪憡憍慦憳戭摮摰撖撠撅撗撜撏撋撊撌撣撟摨撱撘敶敺敹敻斲斳暵暰暩暲暷暪暯樀樆樗槥槸樕槱槤樠槿槬槢樛樝槾樧槲槮樔槷槧橀樈槦槻樍槼槫樉樄樘樥樏槶樦樇槴樖歑殥殣殢殦氁氀毿氂潁漦潾澇濆澒"],["e640","澍澉澌潢潏澅潚澖潶潬澂潕潲潒潐潗澔澓潝漀潡潫潽潧澐潓澋潩潿澕潣潷潪潻熲熯熛熰熠熚熩熵熝熥熞熤熡熪熜熧熳犘犚獘獒獞獟獠獝獛獡獚獙"],["e6a1","獢璇璉璊璆璁瑽璅璈瑼瑹甈甇畾瘥瘞瘙瘝瘜瘣瘚瘨瘛皜皝皞皛瞍瞏瞉瞈磍碻磏磌磑磎磔磈磃磄磉禚禡禠禜禢禛歶稹窲窴窳箷篋箾箬篎箯箹篊箵糅糈糌糋緷緛緪緧緗緡縃緺緦緶緱緰緮緟罶羬羰羭翭翫翪翬翦翨聤聧膣膟"],["e740","膞膕膢膙膗舖艏艓艒艐艎艑蔤蔻蔏蔀蔩蔎蔉蔍蔟蔊蔧蔜蓻蔫蓺蔈蔌蓴蔪蓲蔕蓷蓫蓳蓼蔒蓪蓩蔖蓾蔨蔝蔮蔂蓽蔞蓶蔱蔦蓧蓨蓰蓯蓹蔘蔠蔰蔋蔙蔯虢"],["e7a1","蝖蝣蝤蝷蟡蝳蝘蝔蝛蝒蝡蝚蝑蝞蝭蝪蝐蝎蝟蝝蝯蝬蝺蝮蝜蝥蝏蝻蝵蝢蝧蝩衚褅褌褔褋褗褘褙褆褖褑褎褉覢覤覣觭觰觬諏諆誸諓諑諔諕誻諗誾諀諅諘諃誺誽諙谾豍貏賥賟賙賨賚賝賧趠趜趡趛踠踣踥踤踮踕踛踖踑踙踦踧"],["e840","踔踒踘踓踜踗踚輬輤輘輚輠輣輖輗遳遰遯遧遫鄯鄫鄩鄪鄲鄦鄮醅醆醊醁醂醄醀鋐鋃鋄鋀鋙銶鋏鋱鋟鋘鋩鋗鋝鋌鋯鋂鋨鋊鋈鋎鋦鋍鋕鋉鋠鋞鋧鋑鋓"],["e8a1","銵鋡鋆銴镼閬閫閮閰隤隢雓霅霈霂靚鞊鞎鞈韐韏頞頝頦頩頨頠頛頧颲餈飺餑餔餖餗餕駜駍駏駓駔駎駉駖駘駋駗駌骳髬髫髳髲髱魆魃魧魴魱魦魶魵魰魨魤魬鳼鳺鳽鳿鳷鴇鴀鳹鳻鴈鴅鴄麃黓鼏鼐儜儓儗儚儑凞匴叡噰噠噮"],["e940","噳噦噣噭噲噞噷圜圛壈墽壉墿墺壂墼壆嬗嬙嬛嬡嬔嬓嬐嬖嬨嬚嬠嬞寯嶬嶱嶩嶧嶵嶰嶮嶪嶨嶲嶭嶯嶴幧幨幦幯廩廧廦廨廥彋徼憝憨憖懅憴懆懁懌憺"],["e9a1","憿憸憌擗擖擐擏擉撽撉擃擛擳擙攳敿敼斢曈暾曀曊曋曏暽暻暺曌朣樴橦橉橧樲橨樾橝橭橶橛橑樨橚樻樿橁橪橤橐橏橔橯橩橠樼橞橖橕橍橎橆歕歔歖殧殪殫毈毇氄氃氆澭濋澣濇澼濎濈潞濄澽澞濊澨瀄澥澮澺澬澪濏澿澸"],["ea40","澢濉澫濍澯澲澰燅燂熿熸燖燀燁燋燔燊燇燏熽燘熼燆燚燛犝犞獩獦獧獬獥獫獪瑿璚璠璔璒璕璡甋疀瘯瘭瘱瘽瘳瘼瘵瘲瘰皻盦瞚瞝瞡瞜瞛瞢瞣瞕瞙"],["eaa1","瞗磝磩磥磪磞磣磛磡磢磭磟磠禤穄穈穇窶窸窵窱窷篞篣篧篝篕篥篚篨篹篔篪篢篜篫篘篟糒糔糗糐糑縒縡縗縌縟縠縓縎縜縕縚縢縋縏縖縍縔縥縤罃罻罼罺羱翯耪耩聬膱膦膮膹膵膫膰膬膴膲膷膧臲艕艖艗蕖蕅蕫蕍蕓蕡蕘"],["eb40","蕀蕆蕤蕁蕢蕄蕑蕇蕣蔾蕛蕱蕎蕮蕵蕕蕧蕠薌蕦蕝蕔蕥蕬虣虥虤螛螏螗螓螒螈螁螖螘蝹螇螣螅螐螑螝螄螔螜螚螉褞褦褰褭褮褧褱褢褩褣褯褬褟觱諠"],["eba1","諢諲諴諵諝謔諤諟諰諈諞諡諨諿諯諻貑貒貐賵賮賱賰賳赬赮趥趧踳踾踸蹀蹅踶踼踽蹁踰踿躽輶輮輵輲輹輷輴遶遹遻邆郺鄳鄵鄶醓醐醑醍醏錧錞錈錟錆錏鍺錸錼錛錣錒錁鍆錭錎錍鋋錝鋺錥錓鋹鋷錴錂錤鋿錩錹錵錪錔錌"],["ec40","錋鋾錉錀鋻錖閼闍閾閹閺閶閿閵閽隩雔霋霒霐鞙鞗鞔韰韸頵頯頲餤餟餧餩馞駮駬駥駤駰駣駪駩駧骹骿骴骻髶髺髹髷鬳鮀鮅鮇魼魾魻鮂鮓鮒鮐魺鮕"],["eca1","魽鮈鴥鴗鴠鴞鴔鴩鴝鴘鴢鴐鴙鴟麈麆麇麮麭黕黖黺鼒鼽儦儥儢儤儠儩勴嚓嚌嚍嚆嚄嚃噾嚂噿嚁壖壔壏壒嬭嬥嬲嬣嬬嬧嬦嬯嬮孻寱寲嶷幬幪徾徻懃憵憼懧懠懥懤懨懞擯擩擣擫擤擨斁斀斶旚曒檍檖檁檥檉檟檛檡檞檇檓檎"],["ed40","檕檃檨檤檑橿檦檚檅檌檒歛殭氉濌澩濴濔濣濜濭濧濦濞濲濝濢濨燡燱燨燲燤燰燢獳獮獯璗璲璫璐璪璭璱璥璯甐甑甒甏疄癃癈癉癇皤盩瞵瞫瞲瞷瞶"],["eda1","瞴瞱瞨矰磳磽礂磻磼磲礅磹磾礄禫禨穜穛穖穘穔穚窾竀竁簅簏篲簀篿篻簎篴簋篳簂簉簃簁篸篽簆篰篱簐簊糨縭縼繂縳顈縸縪繉繀繇縩繌縰縻縶繄縺罅罿罾罽翴翲耬膻臄臌臊臅臇膼臩艛艚艜薃薀薏薧薕薠薋薣蕻薤薚薞"],["ee40","蕷蕼薉薡蕺蕸蕗薎薖薆薍薙薝薁薢薂薈薅蕹蕶薘薐薟虨螾螪螭蟅螰螬螹螵螼螮蟉蟃蟂蟌螷螯蟄蟊螴螶螿螸螽蟞螲褵褳褼褾襁襒褷襂覭覯覮觲觳謞"],["eea1","謘謖謑謅謋謢謏謒謕謇謍謈謆謜謓謚豏豰豲豱豯貕貔賹赯蹎蹍蹓蹐蹌蹇轃轀邅遾鄸醚醢醛醙醟醡醝醠鎡鎃鎯鍤鍖鍇鍼鍘鍜鍶鍉鍐鍑鍠鍭鎏鍌鍪鍹鍗鍕鍒鍏鍱鍷鍻鍡鍞鍣鍧鎀鍎鍙闇闀闉闃闅閷隮隰隬霠霟霘霝霙鞚鞡鞜"],["ef40","鞞鞝韕韔韱顁顄顊顉顅顃餥餫餬餪餳餲餯餭餱餰馘馣馡騂駺駴駷駹駸駶駻駽駾駼騃骾髾髽鬁髼魈鮚鮨鮞鮛鮦鮡鮥鮤鮆鮢鮠鮯鴳鵁鵧鴶鴮鴯鴱鴸鴰"],["efa1","鵅鵂鵃鴾鴷鵀鴽翵鴭麊麉麍麰黈黚黻黿鼤鼣鼢齔龠儱儭儮嚘嚜嚗嚚嚝嚙奰嬼屩屪巀幭幮懘懟懭懮懱懪懰懫懖懩擿攄擽擸攁攃擼斔旛曚曛曘櫅檹檽櫡櫆檺檶檷櫇檴檭歞毉氋瀇瀌瀍瀁瀅瀔瀎濿瀀濻瀦濼濷瀊爁燿燹爃燽獶"],["f040","璸瓀璵瓁璾璶璻瓂甔甓癜癤癙癐癓癗癚皦皽盬矂瞺磿礌礓礔礉礐礒礑禭禬穟簜簩簙簠簟簭簝簦簨簢簥簰繜繐繖繣繘繢繟繑繠繗繓羵羳翷翸聵臑臒"],["f0a1","臐艟艞薴藆藀藃藂薳薵薽藇藄薿藋藎藈藅薱薶藒蘤薸薷薾虩蟧蟦蟢蟛蟫蟪蟥蟟蟳蟤蟔蟜蟓蟭蟘蟣螤蟗蟙蠁蟴蟨蟝襓襋襏襌襆襐襑襉謪謧謣謳謰謵譇謯謼謾謱謥謷謦謶謮謤謻謽謺豂豵貙貘貗賾贄贂贀蹜蹢蹠蹗蹖蹞蹥蹧"],["f140","蹛蹚蹡蹝蹩蹔轆轇轈轋鄨鄺鄻鄾醨醥醧醯醪鎵鎌鎒鎷鎛鎝鎉鎧鎎鎪鎞鎦鎕鎈鎙鎟鎍鎱鎑鎲鎤鎨鎴鎣鎥闒闓闑隳雗雚巂雟雘雝霣霢霥鞬鞮鞨鞫鞤鞪"],["f1a1","鞢鞥韗韙韖韘韺顐顑顒颸饁餼餺騏騋騉騍騄騑騊騅騇騆髀髜鬈鬄鬅鬩鬵魊魌魋鯇鯆鯃鮿鯁鮵鮸鯓鮶鯄鮹鮽鵜鵓鵏鵊鵛鵋鵙鵖鵌鵗鵒鵔鵟鵘鵚麎麌黟鼁鼀鼖鼥鼫鼪鼩鼨齌齕儴儵劖勷厴嚫嚭嚦嚧嚪嚬壚壝壛夒嬽嬾嬿巃幰"],["f240","徿懻攇攐攍攉攌攎斄旞旝曞櫧櫠櫌櫑櫙櫋櫟櫜櫐櫫櫏櫍櫞歠殰氌瀙瀧瀠瀖瀫瀡瀢瀣瀩瀗瀤瀜瀪爌爊爇爂爅犥犦犤犣犡瓋瓅璷瓃甖癠矉矊矄矱礝礛"],["f2a1","礡礜礗礞禰穧穨簳簼簹簬簻糬糪繶繵繸繰繷繯繺繲繴繨罋罊羃羆羷翽翾聸臗臕艤艡艣藫藱藭藙藡藨藚藗藬藲藸藘藟藣藜藑藰藦藯藞藢蠀蟺蠃蟶蟷蠉蠌蠋蠆蟼蠈蟿蠊蠂襢襚襛襗襡襜襘襝襙覈覷覶觶譐譈譊譀譓譖譔譋譕"],["f340","譑譂譒譗豃豷豶貚贆贇贉趬趪趭趫蹭蹸蹳蹪蹯蹻軂轒轑轏轐轓辴酀鄿醰醭鏞鏇鏏鏂鏚鏐鏹鏬鏌鏙鎩鏦鏊鏔鏮鏣鏕鏄鏎鏀鏒鏧镽闚闛雡霩霫霬霨霦"],["f3a1","鞳鞷鞶韝韞韟顜顙顝顗颿颽颻颾饈饇饃馦馧騚騕騥騝騤騛騢騠騧騣騞騜騔髂鬋鬊鬎鬌鬷鯪鯫鯠鯞鯤鯦鯢鯰鯔鯗鯬鯜鯙鯥鯕鯡鯚鵷鶁鶊鶄鶈鵱鶀鵸鶆鶋鶌鵽鵫鵴鵵鵰鵩鶅鵳鵻鶂鵯鵹鵿鶇鵨麔麑黀黼鼭齀齁齍齖齗齘匷嚲"],["f440","嚵嚳壣孅巆巇廮廯忀忁懹攗攖攕攓旟曨曣曤櫳櫰櫪櫨櫹櫱櫮櫯瀼瀵瀯瀷瀴瀱灂瀸瀿瀺瀹灀瀻瀳灁爓爔犨獽獼璺皫皪皾盭矌矎矏矍矲礥礣礧礨礤礩"],["f4a1","禲穮穬穭竷籉籈籊籇籅糮繻繾纁纀羺翿聹臛臙舋艨艩蘢藿蘁藾蘛蘀藶蘄蘉蘅蘌藽蠙蠐蠑蠗蠓蠖襣襦覹觷譠譪譝譨譣譥譧譭趮躆躈躄轙轖轗轕轘轚邍酃酁醷醵醲醳鐋鐓鏻鐠鐏鐔鏾鐕鐐鐨鐙鐍鏵鐀鏷鐇鐎鐖鐒鏺鐉鏸鐊鏿"],["f540","鏼鐌鏶鐑鐆闞闠闟霮霯鞹鞻韽韾顠顢顣顟飁飂饐饎饙饌饋饓騲騴騱騬騪騶騩騮騸騭髇髊髆鬐鬒鬑鰋鰈鯷鰅鰒鯸鱀鰇鰎鰆鰗鰔鰉鶟鶙鶤鶝鶒鶘鶐鶛"],["f5a1","鶠鶔鶜鶪鶗鶡鶚鶢鶨鶞鶣鶿鶩鶖鶦鶧麙麛麚黥黤黧黦鼰鼮齛齠齞齝齙龑儺儹劘劗囃嚽嚾孈孇巋巏廱懽攛欂櫼欃櫸欀灃灄灊灈灉灅灆爝爚爙獾甗癪矐礭礱礯籔籓糲纊纇纈纋纆纍罍羻耰臝蘘蘪蘦蘟蘣蘜蘙蘧蘮蘡蘠蘩蘞蘥"],["f640","蠩蠝蠛蠠蠤蠜蠫衊襭襩襮襫觺譹譸譅譺譻贐贔趯躎躌轞轛轝酆酄酅醹鐿鐻鐶鐩鐽鐼鐰鐹鐪鐷鐬鑀鐱闥闤闣霵霺鞿韡顤飉飆飀饘饖騹騽驆驄驂驁騺"],["f6a1","騿髍鬕鬗鬘鬖鬺魒鰫鰝鰜鰬鰣鰨鰩鰤鰡鶷鶶鶼鷁鷇鷊鷏鶾鷅鷃鶻鶵鷎鶹鶺鶬鷈鶱鶭鷌鶳鷍鶲鹺麜黫黮黭鼛鼘鼚鼱齎齥齤龒亹囆囅囋奱孋孌巕巑廲攡攠攦攢欋欈欉氍灕灖灗灒爞爟犩獿瓘瓕瓙瓗癭皭礵禴穰穱籗籜籙籛籚"],["f740","糴糱纑罏羇臞艫蘴蘵蘳蘬蘲蘶蠬蠨蠦蠪蠥襱覿覾觻譾讄讂讆讅譿贕躕躔躚躒躐躖躗轠轢酇鑌鑐鑊鑋鑏鑇鑅鑈鑉鑆霿韣顪顩飋饔饛驎驓驔驌驏驈驊"],["f7a1","驉驒驐髐鬙鬫鬻魖魕鱆鱈鰿鱄鰹鰳鱁鰼鰷鰴鰲鰽鰶鷛鷒鷞鷚鷋鷐鷜鷑鷟鷩鷙鷘鷖鷵鷕鷝麶黰鼵鼳鼲齂齫龕龢儽劙壨壧奲孍巘蠯彏戁戃戄攩攥斖曫欑欒欏毊灛灚爢玂玁玃癰矔籧籦纕艬蘺虀蘹蘼蘱蘻蘾蠰蠲蠮蠳襶襴襳觾"],["f840","讌讎讋讈豅贙躘轤轣醼鑢鑕鑝鑗鑞韄韅頀驖驙鬞鬟鬠鱒鱘鱐鱊鱍鱋鱕鱙鱌鱎鷻鷷鷯鷣鷫鷸鷤鷶鷡鷮鷦鷲鷰鷢鷬鷴鷳鷨鷭黂黐黲黳鼆鼜鼸鼷鼶齃齏"],["f8a1","齱齰齮齯囓囍孎屭攭曭曮欓灟灡灝灠爣瓛瓥矕礸禷禶籪纗羉艭虃蠸蠷蠵衋讔讕躞躟躠躝醾醽釂鑫鑨鑩雥靆靃靇韇韥驞髕魙鱣鱧鱦鱢鱞鱠鸂鷾鸇鸃鸆鸅鸀鸁鸉鷿鷽鸄麠鼞齆齴齵齶囔攮斸欘欙欗欚灢爦犪矘矙礹籩籫糶纚"],["f940","纘纛纙臠臡虆虇虈襹襺襼襻觿讘讙躥躤躣鑮鑭鑯鑱鑳靉顲饟鱨鱮鱭鸋鸍鸐鸏鸒鸑麡黵鼉齇齸齻齺齹圞灦籯蠼趲躦釃鑴鑸鑶鑵驠鱴鱳鱱鱵鸔鸓黶鼊"],["f9a1","龤灨灥糷虪蠾蠽蠿讞貜躩軉靋顳顴飌饡馫驤驦驧鬤鸕鸗齈戇欞爧虌躨钂钀钁驩驨鬮鸙爩虋讟钃鱹麷癵驫鱺鸝灩灪麤齾齉龘碁銹裏墻恒粧嫺╔╦╗╠╬╣╚╩╝╒╤╕╞╪╡╘╧╛╓╥╖╟╫╢╙╨╜║═╭╮╰╯▓"]],xi=[["8740","䏰䰲䘃䖦䕸𧉧䵷䖳𧲱䳢𧳅㮕䜶䝄䱇䱀𤊿𣘗𧍒𦺋𧃒䱗𪍑䝏䗚䲅𧱬䴇䪤䚡𦬣爥𥩔𡩣𣸆𣽡晍囻"],["8767","綕夝𨮹㷴霴𧯯寛𡵞媤㘥𩺰嫑宷峼杮薓𩥅瑡璝㡵𡵓𣚞𦀡㻬"],["87a1","𥣞㫵竼龗𤅡𨤍𣇪𠪊𣉞䌊蒄龖鐯䤰蘓墖靊鈘秐稲晠権袝瑌篅枂稬剏遆㓦珄𥶹瓆鿇垳䤯呌䄱𣚎堘穲𧭥讏䚮𦺈䆁𥶙箮𢒼鿈𢓁𢓉𢓌鿉蔄𣖻䂴鿊䓡𪷿拁灮鿋"],["8840","㇀",4,"𠄌㇅𠃑𠃍㇆㇇𠃋𡿨㇈𠃊㇉㇊㇋㇌𠄎㇍㇎ĀÁǍÀĒÉĚÈŌÓǑÒ࿿Ê̄Ế࿿Ê̌ỀÊāáǎàɑēéěèīíǐìōóǒòūúǔùǖǘǚ"],["88a1","ǜü࿿ê̄ế࿿ê̌ềêɡ⏚⏛"],["8940","𪎩𡅅"],["8943","攊"],["8946","丽滝鵎釟"],["894c","𧜵撑会伨侨兖兴农凤务动医华发变团声处备夲头学实実岚庆总斉柾栄桥济炼电纤纬纺织经统缆缷艺苏药视设询车轧轮"],["89a1","琑糼緍楆竉刧"],["89ab","醌碸酞肼"],["89b0","贋胶𠧧"],["89b5","肟黇䳍鷉鸌䰾𩷶𧀎鸊𪄳㗁"],["89c1","溚舾甙"],["89c5","䤑马骏龙禇𨑬𡷊𠗐𢫦两亁亀亇亿仫伷㑌侽㹈倃傈㑽㒓㒥円夅凛凼刅争剹劐匧㗇厩㕑厰㕓参吣㕭㕲㚁咓咣咴咹哐哯唘唣唨㖘唿㖥㖿嗗㗅"],["8a40","𧶄唥"],["8a43","𠱂𠴕𥄫喐𢳆㧬𠍁蹆𤶸𩓥䁓𨂾睺𢰸㨴䟕𨅝𦧲𤷪擝𠵼𠾴𠳕𡃴撍蹾𠺖𠰋𠽤𢲩𨉖𤓓"],["8a64","𠵆𩩍𨃩䟴𤺧𢳂骲㩧𩗴㿭㔆𥋇𩟔𧣈𢵄鵮頕"],["8a76","䏙𦂥撴哣𢵌𢯊𡁷㧻𡁯"],["8aa1","𦛚𦜖𧦠擪𥁒𠱃蹨𢆡𨭌𠜱"],["8aac","䠋𠆩㿺塳𢶍"],["8ab2","𤗈𠓼𦂗𠽌𠶖啹䂻䎺"],["8abb","䪴𢩦𡂝膪飵𠶜捹㧾𢝵跀嚡摼㹃"],["8ac9","𪘁𠸉𢫏𢳉"],["8ace","𡃈𣧂㦒㨆𨊛㕸𥹉𢃇噒𠼱𢲲𩜠㒼氽𤸻"],["8adf","𧕴𢺋𢈈𪙛𨳍𠹺𠰴𦠜羓𡃏𢠃𢤹㗻𥇣𠺌𠾍𠺪㾓𠼰𠵇𡅏𠹌"],["8af6","𠺫𠮩𠵈𡃀𡄽㿹𢚖搲𠾭"],["8b40","𣏴𧘹𢯎𠵾𠵿𢱑𢱕㨘𠺘𡃇𠼮𪘲𦭐𨳒𨶙𨳊閪哌苄喹"],["8b55","𩻃鰦骶𧝞𢷮煀腭胬尜𦕲脴㞗卟𨂽醶𠻺𠸏𠹷𠻻㗝𤷫㘉𠳖嚯𢞵𡃉𠸐𠹸𡁸𡅈𨈇𡑕𠹹𤹐𢶤婔𡀝𡀞𡃵𡃶垜𠸑"],["8ba1","𧚔𨋍𠾵𠹻𥅾㜃𠾶𡆀𥋘𪊽𤧚𡠺𤅷𨉼墙剨㘚𥜽箲孨䠀䬬鼧䧧鰟鮍𥭴𣄽嗻㗲嚉丨夂𡯁屮靑𠂆乛亻㔾尣彑忄㣺扌攵歺氵氺灬爫丬犭𤣩罒礻糹罓𦉪㓁"],["8bde","𦍋耂肀𦘒𦥑卝衤见𧢲讠贝钅镸长门𨸏韦页风飞饣𩠐鱼鸟黄歯龜丷𠂇阝户钢"],["8c40","倻淾𩱳龦㷉袏𤅎灷峵䬠𥇍㕙𥴰愢𨨲辧釶熑朙玺𣊁𪄇㲋𡦀䬐磤琂冮𨜏䀉橣𪊺䈣蘏𠩯稪𩥇𨫪靕灍匤𢁾鏴盙𨧣龧矝亣俰傼丯众龨吴綋墒壐𡶶庒庙忂𢜒斋"],["8ca1","𣏹椙橃𣱣泿"],["8ca7","爀𤔅玌㻛𤨓嬕璹讃𥲤𥚕窓篬糃繬苸薗龩袐龪躹龫迏蕟駠鈡龬𨶹𡐿䁱䊢娚"],["8cc9","顨杫䉶圽"],["8cce","藖𤥻芿𧄍䲁𦵴嵻𦬕𦾾龭龮宖龯曧繛湗秊㶈䓃𣉖𢞖䎚䔶"],["8ce6","峕𣬚諹屸㴒𣕑嵸龲煗䕘𤃬𡸣䱷㥸㑊𠆤𦱁諌侴𠈹妿腬顖𩣺弻"],["8d40","𠮟"],["8d42","𢇁𨥭䄂䚻𩁹㼇龳𪆵䃸㟖䛷𦱆䅼𨚲𧏿䕭㣔𥒚䕡䔛䶉䱻䵶䗪㿈𤬏㙡䓞䒽䇭崾嵈嵖㷼㠏嶤嶹㠠㠸幂庽弥徃㤈㤔㤿㥍惗愽峥㦉憷憹懏㦸戬抐拥挘㧸嚱"],["8da1","㨃揢揻搇摚㩋擀崕嘡龟㪗斆㪽旿晓㫲暒㬢朖㭂枤栀㭘桊梄㭲㭱㭻椉楃牜楤榟榅㮼槖㯝橥橴橱檂㯬檙㯲檫檵櫔櫶殁毁毪汵沪㳋洂洆洦涁㳯涤涱渕渘温溆𨧀溻滢滚齿滨滩漤漴㵆𣽁澁澾㵪㵵熷岙㶊瀬㶑灐灔灯灿炉𠌥䏁㗱𠻘"],["8e40","𣻗垾𦻓焾𥟠㙎榢𨯩孴穉𥣡𩓙穥穽𥦬窻窰竂竃燑𦒍䇊竚竝竪䇯咲𥰁笋筕笩𥌎𥳾箢筯莜𥮴𦱿篐萡箒箸𥴠㶭𥱥蒒篺簆簵𥳁籄粃𤢂粦晽𤕸糉糇糦籴糳糵糎"],["8ea1","繧䔝𦹄絝𦻖璍綉綫焵綳緒𤁗𦀩緤㴓緵𡟹緥𨍭縝𦄡𦅚繮纒䌫鑬縧罀罁罇礶𦋐駡羗𦍑羣𡙡𠁨䕜𣝦䔃𨌺翺𦒉者耈耝耨耯𪂇𦳃耻耼聡𢜔䦉𦘦𣷣𦛨朥肧𨩈脇脚墰𢛶汿𦒘𤾸擧𡒊舘𡡞橓𤩥𤪕䑺舩𠬍𦩒𣵾俹𡓽蓢荢𦬊𤦧𣔰𡝳𣷸芪椛芳䇛"],["8f40","蕋苐茚𠸖𡞴㛁𣅽𣕚艻苢茘𣺋𦶣𦬅𦮗𣗎㶿茝嗬莅䔋𦶥莬菁菓㑾𦻔橗蕚㒖𦹂𢻯葘𥯤葱㷓䓤檧葊𣲵祘蒨𦮖𦹷𦹃蓞萏莑䒠蒓蓤𥲑䉀𥳀䕃蔴嫲𦺙䔧蕳䔖枿蘖"],["8fa1","𨘥𨘻藁𧂈蘂𡖂𧃍䕫䕪蘨㙈𡢢号𧎚虾蝱𪃸蟮𢰧螱蟚蠏噡虬桖䘏衅衆𧗠𣶹𧗤衞袜䙛袴袵揁装睷𧜏覇覊覦覩覧覼𨨥觧𧤤𧪽誜瞓釾誐𧩙竩𧬺𣾏䜓𧬸煼謌謟𥐰𥕥謿譌譍誩𤩺讐讛誯𡛟䘕衏貛𧵔𧶏貫㜥𧵓賖𧶘𧶽贒贃𡤐賛灜贑𤳉㻐起"],["9040","趩𨀂𡀔𤦊㭼𨆼𧄌竧躭躶軃鋔輙輭𨍥𨐒辥錃𪊟𠩐辳䤪𨧞𨔽𣶻廸𣉢迹𪀔𨚼𨔁𢌥㦀𦻗逷𨔼𧪾遡𨕬𨘋邨𨜓郄𨛦邮都酧㫰醩釄粬𨤳𡺉鈎沟鉁鉢𥖹銹𨫆𣲛𨬌𥗛"],["90a1","𠴱錬鍫𨫡𨯫炏嫃𨫢𨫥䥥鉄𨯬𨰹𨯿鍳鑛躼閅閦鐦閠濶䊹𢙺𨛘𡉼𣸮䧟氜陻隖䅬隣𦻕懚隶磵𨫠隽双䦡𦲸𠉴𦐐𩂯𩃥𤫑𡤕𣌊霱虂霶䨏䔽䖅𤫩灵孁霛靜𩇕靗孊𩇫靟鐥僐𣂷𣂼鞉鞟鞱鞾韀韒韠𥑬韮琜𩐳響韵𩐝𧥺䫑頴頳顋顦㬎𧅵㵑𠘰𤅜"],["9140","𥜆飊颷飈飇䫿𦴧𡛓喰飡飦飬鍸餹𤨩䭲𩡗𩤅駵騌騻騐驘𥜥㛄𩂱𩯕髠髢𩬅髴䰎鬔鬭𨘀倴鬴𦦨㣃𣁽魐魀𩴾婅𡡣鮎𤉋鰂鯿鰌𩹨鷔𩾷𪆒𪆫𪃡𪄣𪇟鵾鶃𪄴鸎梈"],["91a1","鷄𢅛𪆓𪈠𡤻𪈳鴹𪂹𪊴麐麕麞麢䴴麪麯𤍤黁㭠㧥㴝伲㞾𨰫鼂鼈䮖鐤𦶢鼗鼖鼹嚟嚊齅馸𩂋韲葿齢齩竜龎爖䮾𤥵𤦻煷𤧸𤍈𤩑玞𨯚𡣺禟𨥾𨸶鍩鏳𨩄鋬鎁鏋𨥬𤒹爗㻫睲穃烐𤑳𤏸煾𡟯炣𡢾𣖙㻇𡢅𥐯𡟸㜢𡛻𡠹㛡𡝴𡣑𥽋㜣𡛀坛𤨥𡏾𡊨"],["9240","𡏆𡒶蔃𣚦蔃葕𤦔𧅥𣸱𥕜𣻻𧁒䓴𣛮𩦝𦼦柹㜳㰕㷧塬𡤢栐䁗𣜿𤃡𤂋𤄏𦰡哋嚞𦚱嚒𠿟𠮨𠸍鏆𨬓鎜仸儫㠙𤐶亼𠑥𠍿佋侊𥙑婨𠆫𠏋㦙𠌊𠐔㐵伩𠋀𨺳𠉵諚𠈌亘"],["92a1","働儍侢伃𤨎𣺊佂倮偬傁俌俥偘僼兙兛兝兞湶𣖕𣸹𣺿浲𡢄𣺉冨凃𠗠䓝𠒣𠒒𠒑赺𨪜𠜎剙劤𠡳勡鍮䙺熌𤎌𠰠𤦬𡃤槑𠸝瑹㻞璙琔瑖玘䮎𤪼𤂍叐㖄爏𤃉喴𠍅响𠯆圝鉝雴鍦埝垍坿㘾壋媙𨩆𡛺𡝯𡜐娬妸銏婾嫏娒𥥆𡧳𡡡𤊕㛵洅瑃娡𥺃"],["9340","媁𨯗𠐓鏠璌𡌃焅䥲鐈𨧻鎽㞠尞岞幞幈𡦖𡥼𣫮廍孏𡤃𡤄㜁𡢠㛝𡛾㛓脪𨩇𡶺𣑲𨦨弌弎𡤧𡞫婫𡜻孄蘔𧗽衠恾𢡠𢘫忛㺸𢖯𢖾𩂈𦽳懀𠀾𠁆𢘛憙憘恵𢲛𢴇𤛔𩅍"],["93a1","摱𤙥𢭪㨩𢬢𣑐𩣪𢹸挷𪑛撶挱揑𤧣𢵧护𢲡搻敫楲㯴𣂎𣊭𤦉𣊫唍𣋠𡣙𩐿曎𣊉𣆳㫠䆐𥖄𨬢𥖏𡛼𥕛𥐥磮𣄃𡠪𣈴㑤𣈏𣆂𤋉暎𦴤晫䮓昰𧡰𡷫晣𣋒𣋡昞𥡲㣑𣠺𣞼㮙𣞢𣏾瓐㮖枏𤘪梶栞㯄檾㡣𣟕𤒇樳橒櫉欅𡤒攑梘橌㯗橺歗𣿀𣲚鎠鋲𨯪𨫋"],["9440","銉𨀞𨧜鑧涥漋𤧬浧𣽿㶏渄𤀼娽渊塇洤硂焻𤌚𤉶烱牐犇犔𤞏𤜥兹𤪤𠗫瑺𣻸𣙟𤩊𤤗𥿡㼆㺱𤫟𨰣𣼵悧㻳瓌琼鎇琷䒟𦷪䕑疃㽣𤳙𤴆㽘畕癳𪗆㬙瑨𨫌𤦫𤦎㫻"],["94a1","㷍𤩎㻿𤧅𤣳釺圲鍂𨫣𡡤僟𥈡𥇧睸𣈲眎眏睻𤚗𣞁㩞𤣰琸璛㺿𤪺𤫇䃈𤪖𦆮錇𥖁砞碍碈磒珐祙𧝁𥛣䄎禛蒖禥樭𣻺稺秴䅮𡛦䄲鈵秱𠵌𤦌𠊙𣶺𡝮㖗啫㕰㚪𠇔𠰍竢婙𢛵𥪯𥪜娍𠉛磰娪𥯆竾䇹籝籭䈑𥮳𥺼𥺦糍𤧹𡞰粎籼粮檲緜縇緓罎𦉡"],["9540","𦅜𧭈綗𥺂䉪𦭵𠤖柖𠁎𣗏埄𦐒𦏸𤥢翝笧𠠬𥫩𥵃笌𥸎駦虅驣樜𣐿㧢𤧷𦖭騟𦖠蒀𧄧𦳑䓪脷䐂胆脉腂𦞴飃𦩂艢艥𦩑葓𦶧蘐𧈛媆䅿𡡀嬫𡢡嫤𡣘蚠蜨𣶏蠭𧐢娂"],["95a1","衮佅袇袿裦襥襍𥚃襔𧞅𧞄𨯵𨯙𨮜𨧹㺭蒣䛵䛏㟲訽訜𩑈彍鈫𤊄旔焩烄𡡅鵭貟賩𧷜妚矃姰䍮㛔踪躧𤰉輰轊䋴汘澻𢌡䢛潹溋𡟚鯩㚵𤤯邻邗啱䤆醻鐄𨩋䁢𨫼鐧𨰝𨰻蓥訫閙閧閗閖𨴴瑅㻂𤣿𤩂𤏪㻧𣈥随𨻧𨹦𨹥㻌𤧭𤩸𣿮琒瑫㻼靁𩂰"],["9640","桇䨝𩂓𥟟靝鍨𨦉𨰦𨬯𦎾銺嬑譩䤼珹𤈛鞛靱餸𠼦巁𨯅𤪲頟𩓚鋶𩗗釥䓀𨭐𤩧𨭤飜𨩅㼀鈪䤥萔餻饍𧬆㷽馛䭯馪驜𨭥𥣈檏騡嫾騯𩣱䮐𩥈馼䮽䮗鍽塲𡌂堢𤦸"],["96a1","𡓨硄𢜟𣶸棅㵽鑘㤧慐𢞁𢥫愇鱏鱓鱻鰵鰐魿鯏𩸭鮟𪇵𪃾鴡䲮𤄄鸘䲰鴌𪆴𪃭𪃳𩤯鶥蒽𦸒𦿟𦮂藼䔳𦶤𦺄𦷰萠藮𦸀𣟗𦁤秢𣖜𣙀䤭𤧞㵢鏛銾鍈𠊿碹鉷鑍俤㑀遤𥕝砽硔碶硋𡝗𣇉𤥁㚚佲濚濙瀞瀞吔𤆵垻壳垊鴖埗焴㒯𤆬燫𦱀𤾗嬨𡞵𨩉"],["9740","愌嫎娋䊼𤒈㜬䭻𨧼鎻鎸𡣖𠼝葲𦳀𡐓𤋺𢰦𤏁妔𣶷𦝁綨𦅛𦂤𤦹𤦋𨧺鋥珢㻩璴𨭣𡢟㻡𤪳櫘珳珻㻖𤨾𤪔𡟙𤩦𠎧𡐤𤧥瑈𤤖炥𤥶銄珦鍟𠓾錱𨫎𨨖鎆𨯧𥗕䤵𨪂煫"],["97a1","𤥃𠳿嚤𠘚𠯫𠲸唂秄𡟺緾𡛂𤩐𡡒䔮鐁㜊𨫀𤦭妰𡢿𡢃𧒄媡㛢𣵛㚰鉟婹𨪁𡡢鍴㳍𠪴䪖㦊僴㵩㵌𡎜煵䋻𨈘渏𩃤䓫浗𧹏灧沯㳖𣿭𣸭渂漌㵯𠏵畑㚼㓈䚀㻚䡱姄鉮䤾轁𨰜𦯀堒埈㛖𡑒烾𤍢𤩱𢿣𡊰𢎽梹楧𡎘𣓥𧯴𣛟𨪃𣟖𣏺𤲟樚𣚭𦲷萾䓟䓎"],["9840","𦴦𦵑𦲂𦿞漗𧄉茽𡜺菭𦲀𧁓𡟛妉媂𡞳婡婱𡤅𤇼㜭姯𡜼㛇熎鎐暚𤊥婮娫𤊓樫𣻹𧜶𤑛𤋊焝𤉙𨧡侰𦴨峂𤓎𧹍𤎽樌𤉖𡌄炦焳𤏩㶥泟勇𤩏繥姫崯㷳彜𤩝𡟟綤萦"],["98a1","咅𣫺𣌀𠈔坾𠣕𠘙㿥𡾞𪊶瀃𩅛嵰玏糓𨩙𩐠俈翧狍猐𧫴猸猹𥛶獁獈㺩𧬘遬燵𤣲珡臶㻊県㻑沢国琙琞琟㻢㻰㻴㻺瓓㼎㽓畂畭畲疍㽼痈痜㿀癍㿗癴㿜発𤽜熈嘣覀塩䀝睃䀹条䁅㗛瞘䁪䁯属瞾矋売砘点砜䂨砹硇硑硦葈𥔵礳栃礲䄃"],["9940","䄉禑禙辻稆込䅧窑䆲窼艹䇄竏竛䇏両筢筬筻簒簛䉠䉺类粜䊌粸䊔糭输烀𠳏総緔緐緽羮羴犟䎗耠耥笹耮耱联㷌垴炠肷胩䏭脌猪脎脒畠脔䐁㬹腖腙腚"],["99a1","䐓堺腼膄䐥膓䐭膥埯臁臤艔䒏芦艶苊苘苿䒰荗险榊萅烵葤惣蒈䔄蒾蓡蓸蔐蔸蕒䔻蕯蕰藠䕷虲蚒蚲蛯际螋䘆䘗袮裿褤襇覑𧥧訩訸誔誴豑賔賲贜䞘塟跃䟭仮踺嗘坔蹱嗵躰䠷軎転軤軭軲辷迁迊迌逳駄䢭飠鈓䤞鈨鉘鉫銱銮銿"],["9a40","鋣鋫鋳鋴鋽鍃鎄鎭䥅䥑麿鐗匁鐝鐭鐾䥪鑔鑹锭関䦧间阳䧥枠䨤靀䨵鞲韂噔䫤惨颹䬙飱塄餎餙冴餜餷饂饝饢䭰駅䮝騼鬏窃魩鮁鯝鯱鯴䱭鰠㝯𡯂鵉鰺"],["9aa1","黾噐鶓鶽鷀鷼银辶鹻麬麱麽黆铜黢黱黸竈齄𠂔𠊷𠎠椚铃妬𠓗塀铁㞹𠗕𠘕𠙶𡚺块煳𠫂𠫍𠮿呪吆𠯋咞𠯻𠰻𠱓𠱥𠱼惧𠲍噺𠲵𠳝𠳭𠵯𠶲𠷈楕鰯螥𠸄𠸎𠻗𠾐𠼭𠹳尠𠾼帋𡁜𡁏𡁶朞𡁻𡂈𡂖㙇𡂿𡃓𡄯𡄻卤蒭𡋣𡍵𡌶讁𡕷𡘙𡟃𡟇乸炻𡠭𡥪"],["9b40","𡨭𡩅𡰪𡱰𡲬𡻈拃𡻕𡼕熘桕𢁅槩㛈𢉼𢏗𢏺𢜪𢡱𢥏苽𢥧𢦓𢫕覥𢫨辠𢬎鞸𢬿顇骽𢱌"],["9b62","𢲈𢲷𥯨𢴈𢴒𢶷𢶕𢹂𢽴𢿌𣀳𣁦𣌟𣏞徱晈暿𧩹𣕧𣗳爁𤦺矗𣘚𣜖纇𠍆墵朎"],["9ba1","椘𣪧𧙗𥿢𣸑𣺹𧗾𢂚䣐䪸𤄙𨪚𤋮𤌍𤀻𤌴𤎖𤩅𠗊凒𠘑妟𡺨㮾𣳿𤐄𤓖垈𤙴㦛𤜯𨗨𩧉㝢𢇃譞𨭎駖𤠒𤣻𤨕爉𤫀𠱸奥𤺥𤾆𠝹軚𥀬劏圿煱𥊙𥐙𣽊𤪧喼𥑆𥑮𦭒釔㑳𥔿𧘲𥕞䜘𥕢𥕦𥟇𤤿𥡝偦㓻𣏌惞𥤃䝼𨥈𥪮𥮉𥰆𡶐垡煑澶𦄂𧰒遖𦆲𤾚譢𦐂𦑊"],["9c40","嵛𦯷輶𦒄𡤜諪𤧶𦒈𣿯𦔒䯀𦖿𦚵𢜛鑥𥟡憕娧晉侻嚹𤔡𦛼乪𤤴陖涏𦲽㘘襷𦞙𦡮𦐑𦡞營𦣇筂𩃀𠨑𦤦鄄𦤹穅鷰𦧺騦𦨭㙟𦑩𠀡禃𦨴𦭛崬𣔙菏𦮝䛐𦲤画补𦶮墶"],["9ca1","㜜𢖍𧁋𧇍㱔𧊀𧊅銁𢅺𧊋錰𧋦𤧐氹钟𧑐𠻸蠧裵𢤦𨑳𡞱溸𤨪𡠠㦤㚹尐秣䔿暶𩲭𩢤襃𧟌𧡘囖䃟𡘊㦡𣜯𨃨𡏅熭荦𧧝𩆨婧䲷𧂯𨦫𧧽𧨊𧬋𧵦𤅺筃祾𨀉澵𪋟樃𨌘厢𦸇鎿栶靝𨅯𨀣𦦵𡏭𣈯𨁈嶅𨰰𨂃圕頣𨥉嶫𤦈斾槕叒𤪥𣾁㰑朶𨂐𨃴𨄮𡾡𨅏"],["9d40","𨆉𨆯𨈚𨌆𨌯𨎊㗊𨑨𨚪䣺揦𨥖砈鉕𨦸䏲𨧧䏟𨧨𨭆𨯔姸𨰉輋𨿅𩃬筑𩄐𩄼㷷𩅞𤫊运犏嚋𩓧𩗩𩖰𩖸𩜲𩣑𩥉𩥪𩧃𩨨𩬎𩵚𩶛纟𩻸𩼣䲤镇𪊓熢𪋿䶑递𪗋䶜𠲜达嗁"],["9da1","辺𢒰边𤪓䔉繿潖檱仪㓤𨬬𧢝㜺躀𡟵𨀤𨭬𨮙𧨾𦚯㷫𧙕𣲷𥘵𥥖亚𥺁𦉘嚿𠹭踎孭𣺈𤲞揞拐𡟶𡡻攰嘭𥱊吚𥌑㷆𩶘䱽嘢嘞罉𥻘奵𣵀蝰东𠿪𠵉𣚺脗鵞贘瘻鱅癎瞹鍅吲腈苷嘥脲萘肽嗪祢噃吖𠺝㗎嘅嗱曱𨋢㘭甴嗰喺咗啲𠱁𠲖廐𥅈𠹶𢱢"],["9e40","𠺢麫絚嗞𡁵抝靭咔賍燶酶揼掹揾啩𢭃鱲𢺳冚㓟𠶧冧呍唞唓癦踭𦢊疱肶蠄螆裇膶萜𡃁䓬猄𤜆宐茋𦢓噻𢛴𧴯𤆣𧵳𦻐𧊶酰𡇙鈈𣳼𪚩𠺬𠻹牦𡲢䝎𤿂𧿹𠿫䃺"],["9ea1","鱝攟𢶠䣳𤟠𩵼𠿬𠸊恢𧖣𠿭"],["9ead","𦁈𡆇熣纎鵐业丄㕷嬍沲卧㚬㧜卽㚥𤘘墚𤭮舭呋垪𥪕𠥹"],["9ec5","㩒𢑥獴𩺬䴉鯭𣳾𩼰䱛𤾩𩖞𩿞葜𣶶𧊲𦞳𣜠挮紥𣻷𣸬㨪逈勌㹴㙺䗩𠒎癀嫰𠺶硺𧼮墧䂿噼鮋嵴癔𪐴麅䳡痹㟻愙𣃚𤏲"],["9ef5","噝𡊩垧𤥣𩸆刴𧂮㖭汊鵼"],["9f40","籖鬹埞𡝬屓擓𩓐𦌵𧅤蚭𠴨𦴢𤫢𠵱"],["9f4f","凾𡼏嶎霃𡷑麁遌笟鬂峑箣扨挵髿篏鬪籾鬮籂粆鰕篼鬉鼗鰛𤤾齚啳寃俽麘俲剠㸆勑坧偖妷帒韈鶫轜呩鞴饀鞺匬愰"],["9fa1","椬叚鰊鴂䰻陁榀傦畆𡝭駚剳"],["9fae","酙隁酜"],["9fb2","酑𨺗捿𦴣櫊嘑醎畺抅𠏼獏籰𥰡𣳽"],["9fc1","𤤙盖鮝个𠳔莾衂"],["9fc9","届槀僭坺刟巵从氱𠇲伹咜哚劚趂㗾弌㗳"],["9fdb","歒酼龥鮗頮颴骺麨麄煺笔"],["9fe7","毺蠘罸"],["9feb","嘠𪙊蹷齓"],["9ff0","跔蹏鸜踁抂𨍽踨蹵竓𤩷稾磘泪詧瘇"],["a040","𨩚鼦泎蟖痃𪊲硓咢贌狢獱謭猂瓱賫𤪻蘯徺袠䒷"],["a055","𡠻𦸅"],["a058","詾𢔛"],["a05b","惽癧髗鵄鍮鮏蟵"],["a063","蠏賷猬霡鮰㗖犲䰇籑饊𦅙慙䰄麖慽"],["a073","坟慯抦戹拎㩜懢厪𣏵捤栂㗒"],["a0a1","嵗𨯂迚𨸹"],["a0a6","僙𡵆礆匲阸𠼻䁥"],["a0ae","矾"],["a0b0","糂𥼚糚稭聦聣絍甅瓲覔舚朌聢𧒆聛瓰脃眤覉𦟌畓𦻑螩蟎臈螌詉貭譃眫瓸蓚㘵榲趦"],["a0d4","覩瑨涹蟁𤀑瓧㷛煶悤憜㳑煢恷"],["a0e2","罱𨬭牐惩䭾删㰘𣳇𥻗𧙖𥔱𡥄𡋾𩤃𦷜𧂭峁𦆭𨨏𣙷𠃮𦡆𤼎䕢嬟𦍌齐麦𦉫"],["a3c0","␀",31,"␡"],["c6a1","①",9,"⑴",9,"ⅰ",9,"丶丿亅亠冂冖冫勹匸卩厶夊宀巛⼳广廴彐彡攴无疒癶辵隶¨ˆヽヾゝゞ〃仝々〆〇ー［］✽ぁ",23],["c740","す",58,"ァアィイ"],["c7a1","ゥ",81,"А",5,"ЁЖ",4],["c840","Л",26,"ёж",25,"⇧↸↹㇏𠃌乚𠂊刂䒑"],["c8a1","龰冈龱𧘇"],["c8cd","￢￤＇＂㈱№℡゛゜⺀⺄⺆⺇⺈⺊⺌⺍⺕⺜⺝⺥⺧⺪⺬⺮⺶⺼⺾⻆⻊⻌⻍⻏⻖⻗⻞⻣"],["c8f5","ʃɐɛɔɵœøŋʊɪ"],["f9fe","￭"],["fa40","𠕇鋛𠗟𣿅蕌䊵珯况㙉𤥂𨧤鍄𡧛苮𣳈砼杄拟𤤳𨦪𠊠𦮳𡌅侫𢓭倈𦴩𧪄𣘀𤪱𢔓倩𠍾徤𠎀𠍇滛𠐟偽儁㑺儎顬㝃萖𤦤𠒇兠𣎴兪𠯿𢃼𠋥𢔰𠖎𣈳𡦃宂蝽𠖳𣲙冲冸"],["faa1","鴴凉减凑㳜凓𤪦决凢卂凭菍椾𣜭彻刋刦刼劵剗劔効勅簕蕂勠蘍𦬓包𨫞啉滙𣾀𠥔𣿬匳卄𠯢泋𡜦栛珕恊㺪㣌𡛨燝䒢卭却𨚫卾卿𡖖𡘓矦厓𨪛厠厫厮玧𥝲㽙玜叁叅汉义埾叙㪫𠮏叠𣿫𢶣叶𠱷吓灹唫晗浛呭𦭓𠵴啝咏咤䞦𡜍𠻝㶴𠵍"],["fb40","𨦼𢚘啇䳭启琗喆喩嘅𡣗𤀺䕒𤐵暳𡂴嘷曍𣊊暤暭噍噏磱囱鞇叾圀囯园𨭦㘣𡉏坆𤆥汮炋坂㚱𦱾埦𡐖堃𡑔𤍣堦𤯵塜墪㕡壠壜𡈼壻寿坃𪅐𤉸鏓㖡够梦㛃湙"],["fba1","𡘾娤啓𡚒蔅姉𠵎𦲁𦴪𡟜姙𡟻𡞲𦶦浱𡠨𡛕姹𦹅媫婣㛦𤦩婷㜈媖瑥嫓𦾡𢕔㶅𡤑㜲𡚸広勐孶斈孼𧨎䀄䡝𠈄寕慠𡨴𥧌𠖥寳宝䴐尅𡭄尓珎尔𡲥𦬨屉䣝岅峩峯嶋𡷹𡸷崐崘嵆𡺤岺巗苼㠭𤤁𢁉𢅳芇㠶㯂帮檊幵幺𤒼𠳓厦亷廐厨𡝱帉廴𨒂"],["fc40","廹廻㢠廼栾鐛弍𠇁弢㫞䢮𡌺强𦢈𢏐彘𢑱彣鞽𦹮彲鍀𨨶徧嶶㵟𥉐𡽪𧃸𢙨釖𠊞𨨩怱暅𡡷㥣㷇㘹垐𢞴祱㹀悞悤悳𤦂𤦏𧩓璤僡媠慤萤慂慈𦻒憁凴𠙖憇宪𣾷"],["fca1","𢡟懓𨮝𩥝懐㤲𢦀𢣁怣慜攞掋𠄘担𡝰拕𢸍捬𤧟㨗搸揸𡎎𡟼撐澊𢸶頔𤂌𥜝擡擥鑻㩦携㩗敍漖𤨨𤨣斅敭敟𣁾斵𤥀䬷旑䃘𡠩无旣忟𣐀昘𣇷𣇸晄𣆤𣆥晋𠹵晧𥇦晳晴𡸽𣈱𨗴𣇈𥌓矅𢣷馤朂𤎜𤨡㬫槺𣟂杞杧杢𤇍𩃭柗䓩栢湐鈼栁𣏦𦶠桝"],["fd40","𣑯槡樋𨫟楳棃𣗍椁椀㴲㨁𣘼㮀枬楡𨩊䋼椶榘㮡𠏉荣傐槹𣙙𢄪橅𣜃檝㯳枱櫈𩆜㰍欝𠤣惞欵歴𢟍溵𣫛𠎵𡥘㝀吡𣭚毡𣻼毜氷𢒋𤣱𦭑汚舦汹𣶼䓅𣶽𤆤𤤌𤤀"],["fda1","𣳉㛥㳫𠴲鮃𣇹𢒑羏样𦴥𦶡𦷫涖浜湼漄𤥿𤂅𦹲蔳𦽴凇沜渝萮𨬡港𣸯瑓𣾂秌湏媑𣁋濸㜍澝𣸰滺𡒗𤀽䕕鏰潄潜㵎潴𩅰㴻澟𤅄濓𤂑𤅕𤀹𣿰𣾴𤄿凟𤅖𤅗𤅀𦇝灋灾炧炁烌烕烖烟䄄㷨熴熖𤉷焫煅媈煊煮岜𤍥煏鍢𤋁焬𤑚𤨧𤨢熺𨯨炽爎"],["fe40","鑂爕夑鑃爤鍁𥘅爮牀𤥴梽牕牗㹕𣁄栍漽犂猪猫𤠣𨠫䣭𨠄猨献珏玪𠰺𦨮珉瑉𤇢𡛧𤨤昣㛅𤦷𤦍𤧻珷琕椃𤨦琹𠗃㻗瑜𢢭瑠𨺲瑇珤瑶莹瑬㜰瑴鏱樬璂䥓𤪌"],["fea1","𤅟𤩹𨮏孆𨰃𡢞瓈𡦈甎瓩甞𨻙𡩋寗𨺬鎅畍畊畧畮𤾂㼄𤴓疎瑝疞疴瘂瘬癑癏癯癶𦏵皐臯㟸𦤑𦤎皡皥皷盌𦾟葢𥂝𥅽𡸜眞眦着撯𥈠睘𣊬瞯𨥤𨥨𡛁矴砉𡍶𤨒棊碯磇磓隥礮𥗠磗礴碱𧘌辸袄𨬫𦂃𢘜禆褀椂禀𥡗禝𧬹礼禩渪𧄦㺨秆𩄍秔"]];var Qe,Ir;function Di(){return Ir||(Ir=1,Qe={shiftjis:{type:"_dbcs",table:function(){return wi},encodeAdd:{"¥":92,"‾":126},encodeSkipVals:[{from:60736,to:63808}]},csshiftjis:"shiftjis",mskanji:"shiftjis",sjis:"shiftjis",windows31j:"shiftjis",ms31j:"shiftjis",xsjis:"shiftjis",windows932:"shiftjis",ms932:"shiftjis",932:"shiftjis",cp932:"shiftjis",eucjp:{type:"_dbcs",table:function(){return Ti},encodeAdd:{"¥":92,"‾":126}},gb2312:"cp936",gb231280:"cp936",gb23121980:"cp936",csgb2312:"cp936",csiso58gb231280:"cp936",euccn:"cp936",windows936:"cp936",ms936:"cp936",936:"cp936",cp936:{type:"_dbcs",table:function(){return Je}},gbk:{type:"_dbcs",table:function(){return Je.concat(vr)}},xgbk:"gbk",isoir58:"gbk",gb18030:{type:"_dbcs",table:function(){return Je.concat(vr)},gb18030:function(){return Ci},encodeSkipVals:[128],encodeAdd:{"€":41699}},chinese:"gb18030",windows949:"cp949",ms949:"cp949",949:"cp949",cp949:{type:"_dbcs",table:function(){return Ii}},cseuckr:"cp949",csksc56011987:"cp949",euckr:"cp949",isoir149:"cp949",korean:"cp949",ksc56011987:"cp949",ksc56011989:"cp949",ksc5601:"cp949",windows950:"cp950",ms950:"cp950",950:"cp950",cp950:{type:"_dbcs",table:function(){return Cr}},big5:"big5hkscs",big5hkscs:{type:"_dbcs",table:function(){return Cr.concat(xi)},encodeSkipVals:[36457,36463,36478,36523,36532,36557,36560,36695,36713,36718,36811,36862,36973,36986,37060,37084,37105,37311,37551,37552,37553,37554,37585,37959,38090,38361,38652,39285,39798,39800,39803,39878,39902,39916,39926,40002,40019,40034,40040,40043,40055,40124,40125,40144,40279,40282,40388,40431,40443,40617,40687,40701,40800,40907,41079,41180,41183,36812,37576,38468,38637,41636,41637,41639,41638,41676,41678]},cnbig5:"big5hkscs",csbig5:"big5hkscs",xxbig5:"big5hkscs"}),Qe}var xr;function Ni(){return xr||(xr=1,function(s){for(var e=[hi(),di(),pi(),gi(),mi(),yi(),Ei(),bi(),Di()],t=0;t<e.length;t++){var r=e[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(s[o]=r[o])}}(qe)),qe}var et,Dr;function Si(){if(Dr)return et;Dr=1;var s=ge.Buffer;return et=function(e){var t=e.Transform;function r(n,i){this.conv=n,i=i||{},i.decodeStrings=!1,t.call(this,i)}r.prototype=Object.create(t.prototype,{constructor:{value:r}}),r.prototype._transform=function(n,i,a){if(typeof n!="string")return a(new Error("Iconv encoding stream needs strings as its input."));try{var l=this.conv.write(n);l&&l.length&&this.push(l),a()}catch(f){a(f)}},r.prototype._flush=function(n){try{var i=this.conv.end();i&&i.length&&this.push(i),n()}catch(a){n(a)}},r.prototype.collect=function(n){var i=[];return this.on("error",n),this.on("data",function(a){i.push(a)}),this.on("end",function(){n(null,s.concat(i))}),this};function o(n,i){this.conv=n,i=i||{},i.encoding=this.encoding="utf8",t.call(this,i)}return o.prototype=Object.create(t.prototype,{constructor:{value:o}}),o.prototype._transform=function(n,i,a){if(!s.isBuffer(n)&&!(n instanceof Uint8Array))return a(new Error("Iconv decoding stream needs buffers as its input."));try{var l=this.conv.write(n);l&&l.length&&this.push(l,this.encoding),a()}catch(f){a(f)}},o.prototype._flush=function(n){try{var i=this.conv.end();i&&i.length&&this.push(i,this.encoding),n()}catch(a){n(a)}},o.prototype.collect=function(n){var i="";return this.on("error",n),this.on("data",function(a){i+=a}),this.on("end",function(){n(null,i)}),this},{IconvLiteEncoderStream:r,IconvLiteDecoderStream:o}},et}(function(s){var e=ge.Buffer,t=Ht,r=s.exports;r.encodings=null,r.defaultCharUnicode="�",r.defaultCharSingleByte="?",r.encode=function(i,a,l){i=""+(i||"");var f=r.getEncoder(a,l),m=f.write(i),y=f.end();return y&&y.length>0?e.concat([m,y]):m},r.decode=function(i,a,l){typeof i=="string"&&(r.skipDecodeWarning||(console.error("Iconv-lite warning: decode()-ing strings is deprecated. Refer to https://github.com/ashtuchkin/iconv-lite/wiki/Use-Buffers-when-decoding"),r.skipDecodeWarning=!0),i=e.from(""+(i||""),"binary"));var f=r.getDecoder(a,l),m=f.write(i),y=f.end();return y?m+y:m},r.encodingExists=function(i){try{return r.getCodec(i),!0}catch{return!1}},r.toEncoding=r.encode,r.fromEncoding=r.decode,r._codecDataCache={},r.getCodec=function(i){r.encodings||(r.encodings=Ni());for(var a=r._canonicalizeEncoding(i),l={};;){var f=r._codecDataCache[a];if(f)return f;var m=r.encodings[a];switch(typeof m){case"string":a=m;break;case"object":for(var y in m)l[y]=m[y];l.encodingName||(l.encodingName=a),a=m.type;break;case"function":return l.encodingName||(l.encodingName=a),f=new m(l,r),r._codecDataCache[l.encodingName]=f,f;default:throw new Error("Encoding not recognized: '"+i+"' (searched as: '"+a+"')")}}},r._canonicalizeEncoding=function(n){return(""+n).toLowerCase().replace(/:\d{4}$|[^0-9a-z]/g,"")},r.getEncoder=function(i,a){var l=r.getCodec(i),f=new l.encoder(a,l);return l.bomAware&&a&&a.addBOM&&(f=new t.PrependBOM(f,a)),f},r.getDecoder=function(i,a){var l=r.getCodec(i),f=new l.decoder(a,l);return l.bomAware&&!(a&&a.stripBOM===!1)&&(f=new t.StripBOM(f,a)),f},r.enableStreamingAPI=function(i){if(!r.supportsStreams){var a=Si()(i);r.IconvLiteEncoderStream=a.IconvLiteEncoderStream,r.IconvLiteDecoderStream=a.IconvLiteDecoderStream,r.encodeStream=function(f,m){return new r.IconvLiteEncoderStream(r.getEncoder(f,m),m)},r.decodeStream=function(f,m){return new r.IconvLiteDecoderStream(r.getDecoder(f,m),m)},r.supportsStreams=!0}};var o;try{o=require("stream")}catch{}o&&o.Transform?r.enableStreamingAPI(o):r.encodeStream=r.decodeStream=function(){throw new Error("iconv-lite Streaming API is not enabled. Use iconv.enableStreamingAPI(require('stream')); to enable it.")}})(xn);var Nr=xn.exports;const tt=[239,187,191];function Sn(s){const e=new Uint8Array(s);return e.length<3?!1:e[0]===tt[0]&&e[1]===tt[1]&&e[2]===tt[2]}function Li(s){return Sn(s)?s.slice(3):s}function Xt(s){if(s.includes("�"))return!1;const e=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g,t=s.match(e);return!(t&&t.length>s.length*.1)}async function Ie(s){if(Sn(s)){const o=Li(s);return{encoding:"utf-8",confidence:1,text:new TextDecoder("utf-8").decode(o)}}try{const o=new TextDecoder("utf-8",{fatal:!0}).decode(s);if(Xt(o))return{encoding:"utf-8",confidence:1,text:o}}catch{}try{const o=new TextDecoder("utf-8",{fatal:!1}).decode(s);if(Xt(o))return{encoding:"utf-8",confidence:.9,text:o}}catch{}const e=Buffer.from(s),t=["gbk","gb2312","big5","shift_jis","euc-kr","iso-8859-1"];for(const o of t)try{if(Nr.encodingExists(o)){const n=Nr.decode(e,o);if(Pi(n,o))return{encoding:o,confidence:Oi(n,o),text:n}}}catch{continue}return{encoding:"utf-8",confidence:.3,text:new TextDecoder("utf-8",{fatal:!1}).decode(s)}}function Pi(s,e){if(!Xt(s))return!1;switch(e.toLowerCase()){case"gbk":case"gb2312":case"big5":return $t(s);case"shift_jis":return Ln(s);case"euc-kr":return Pn(s);default:return!0}}function Oi(s,e){let t=.7;switch(e.toLowerCase()){case"gbk":case"gb2312":$t(s)&&(t+=.2);break;case"big5":$t(s)&&(t+=.15);break;case"shift_jis":Ln(s)&&(t+=.2);break;case"euc-kr":Pn(s)&&(t+=.2);break}return Math.min(t,1)}function $t(s){return/[\u4e00-\u9fff]/.test(s)}function Ln(s){return/[\u3040-\u309f\u30a0-\u30ff]/.test(s)}function Pn(s){return/[\uac00-\ud7af]/.test(s)}function Mi(s){const{font:e,page:t}=s;let r;if(typeof document<"u"){const y=document.createElement("canvas").getContext("2d");y.font=`${e.weight} ${e.size}px ${e.family}`,r=y.measureText("中").width}else r=Ai(e.size,e.family);const o=e.size*e.lineHeight,n=t.width-t.marginLeft-t.marginRight,i=t.height-t.marginTop-t.marginBottom,a=Math.floor(n/r),l=Math.floor(i/o),f=a*l;return{charWidth:r,lineHeight:o,charsPerLine:a,linesPerPage:l,charsPerPage:f}}function Ai(s,e){let t=1;return e.includes("monospace")?t=.6:e.includes("serif")?t=.9:t=.85,s*t}function Sr(s,e){const t=Mi(e),r=[],o=s.replace(/\r\n/g,`
`).replace(/\r/g,`
`);let n=0,i=1;for(;n<o.length;){const l=Ri(o,n,i,t);r.push(l),n=l.endPosition,i++}const a=r.length>0?Math.round(o.length/r.length):0;return{totalPages:r.length,pages:r,averageCharsPerPage:a}}function Ri(s,e,t,r){const{charsPerLine:o,linesPerPage:n}=r;let i=e,a=0,l="";for(;a<n&&i<s.length;){const f=Fi(s,i,o);if(f.content.length===0)break;l+=f.content,i=f.nextPosition,a++,f.isNewParagraph&&(a+=.5)}return{pageNumber:t,startPosition:e,endPosition:i,content:l,lineCount:a}}function Fi(s,e,t){if(e>=s.length)return{content:"",nextPosition:e,isNewParagraph:!1};const r=s.indexOf(`
`,e),o=r!==-1;let n;if(o&&r-e<=t)n=r+1;else if(n=Math.min(e+t,s.length),n<s.length){const l=Bi(s,e,n);l>e&&(n=l)}const i=s.substring(e,n),a=o&&r<n;return{content:i,nextPosition:n,isNewParagraph:a}}function Bi(s,e,t){for(let r=t-1;r>e;r--){const o=s[r];if(o===" "||o==="	"||o==="-"||o==="，"||o==="。")return r+1}return t}function rt(s,e){for(const t of e)if(s>=t.startPosition&&s<t.endPosition)return t.pageNumber;return e.length>0?e[e.length-1].pageNumber:1}function Lr(s,e){return e.find(t=>t.pageNumber===s)||null}function Ui(s,e){return e===0?0:Math.round(s/e*100)}class On{constructor(e){R(this,"bookContent",null);R(this,"originalText","");R(this,"paginationResult",null);R(this,"currentSettings");R(this,"events",{});R(this,"encodingResult",null);this.currentSettings={font:{family:"Microsoft YaHei, SimSun, serif",size:16,weight:"normal",lineHeight:1.6},page:{width:800,height:600,marginTop:40,marginBottom:40,marginLeft:60,marginRight:60},theme:{mode:"light",backgroundColor:"#ffffff",textColor:"#333333"},readingMode:"pagination",zoomLevel:1,...e}}async loadBook(e){var t,r,o,n,i,a;try{const l=await this.readFileContent(e);this.originalText=l.content,this.encodingResult={encoding:l.encoding,confidence:1,text:l.content},console.log(`TxtReader.loadBook: 文件内容长度 ${((t=this.originalText)==null?void 0:t.length)||0}`),console.log(`TxtReader.loadBook: 编码 ${this.encodingResult.encoding}`),console.log(`TxtReader.loadBook: 内容预览: ${((r=this.originalText)==null?void 0:r.substring(0,200))||"空内容"}`);const f=this.detectChapters(this.originalText);return this.paginationResult=Sr(this.originalText,this.currentSettings),console.log(`TxtReader.loadBook: 分页完成，总页数 ${this.paginationResult.totalPages}`),this.bookContent={id:this.generateBookId(e),title:this.extractTitle(e),author:"未知作者",filePath:e,format:"txt",totalPages:this.paginationResult.totalPages,currentPage:1,progress:0,content:this.originalText,chapters:f,metadata:{fileSize:l.size,createdAt:new Date,language:this.detectLanguage(this.originalText)}},(n=(o=this.events).onLoadComplete)==null||n.call(o,this.bookContent),this.bookContent}catch(l){const f=l instanceof Error?l:new Error(String(l));throw(a=(i=this.events).onError)==null||a.call(i,f),f}}async getPageContent(e){var o,n,i,a,l;if(console.log(`TxtReader.getPageContent: 请求第 ${e} 页`),!this.paginationResult||!this.bookContent)throw console.error("TxtReader.getPageContent: 书籍未加载"),new Error("书籍未加载");console.log(`TxtReader.getPageContent: 总页数 ${this.paginationResult.totalPages}`);const t=Lr(e,this.paginationResult.pages);if(!t)throw console.error(`TxtReader.getPageContent: 页面 ${e} 不存在`),new Error(`页面 ${e} 不存在`);console.log("TxtReader.getPageContent: 页面信息",{pageNumber:t.pageNumber,startPosition:t.startPosition,endPosition:t.endPosition,contentLength:((o=t.content)==null?void 0:o.length)||0}),this.bookContent.currentPage=e,this.bookContent.progress=Ui(t.startPosition,this.originalText.length),(i=(n=this.events).onPageChange)==null||i.call(n,e),(l=(a=this.events).onProgressChange)==null||l.call(a,this.bookContent.progress);const r=this.formatPageContent(t.content);return console.log(`TxtReader.getPageContent: 格式化后内容长度 ${(r==null?void 0:r.length)||0}`),r}async goToPage(e){await this.getPageContent(e)}async nextPage(){if(!this.bookContent||!this.paginationResult)return!1;const e=this.bookContent.currentPage+1;return e<=this.paginationResult.totalPages?(await this.goToPage(e),!0):!1}async previousPage(){if(!this.bookContent)return!1;const e=this.bookContent.currentPage-1;return e>=1?(await this.goToPage(e),!0):!1}async search(e){if(!this.originalText||!this.paginationResult)return[];const t=[],r=new RegExp(e,"gi");let o;for(;(o=r.exec(this.originalText))!==null;){const n=o.index,i=rt(n,this.paginationResult.pages),a=Math.max(0,n-50),l=Math.min(this.originalText.length,n+e.length+50),f=this.originalText.substring(a,l);t.push({text:o[0],page:i,position:n,context:f})}return t}getCurrentPosition(){if(!this.bookContent||!this.paginationResult)return{page:1,characterPosition:0,scrollPosition:0};const e=Lr(this.bookContent.currentPage,this.paginationResult.pages);return{page:this.bookContent.currentPage,characterPosition:(e==null?void 0:e.startPosition)||0,scrollPosition:0}}async setPosition(e){if(this.paginationResult){if(e.page)await this.goToPage(e.page);else if(e.characterPosition!==void 0){const t=rt(e.characterPosition,this.paginationResult.pages);await this.goToPage(t)}}}applySettings(e){if(this.currentSettings={...this.currentSettings,...e},this.originalText&&(e.font||e.page)){const t=this.getCurrentPosition();this.paginationResult=Sr(this.originalText,this.currentSettings),this.bookContent&&(this.bookContent.totalPages=this.paginationResult.totalPages,this.setPosition(t))}}getChapters(){var e;return((e=this.bookContent)==null?void 0:e.chapters)||[]}async goToChapter(e){const r=this.getChapters().find(o=>o.id===e);if(r&&this.paginationResult){const o=rt(r.startPosition,this.paginationResult.pages);await this.goToPage(o)}}setEventListeners(e){this.events={...this.events,...e}}async getFullContent(){if(!this.originalText)throw new Error("书籍未加载");return this.originalText.replace(/\r\n/g,`
`).replace(/\r/g,`
`).trim()}dispose(){this.bookContent=null,this.originalText="",this.paginationResult=null,this.events={},this.encodingResult=null}async readFileContent(e){try{if(typeof window<"u"&&window.electronAPI){const n=window.electronAPI;if(n.txtReader&&n.txtReader.readFile){const i=await n.txtReader.readFile(e);return{content:i.content,encoding:i.encoding||"utf-8",size:i.size||i.content.length}}if(n.invoke){const i=await n.invoke("txt-reader:read-file",e);return{content:i.content,encoding:i.encoding||"utf-8",size:i.size||i.content.length}}if(n.txtReader&&n.txtReader.detectEncoding){const i=await n.txtReader.detectEncoding(e),a=await n.txtReader.readFile(e,i.encoding);return{content:a.content,encoding:a.encoding,size:a.size}}if(n.file&&n.file.read){const i=await n.file.read(e),a=i.buffer||i;return{content:new TextDecoder("utf-8",{fatal:!1}).decode(a),encoding:"utf-8",size:a.byteLength}}}const t=await fetch(e);if(!t.ok)throw new Error(`Failed to read file: ${t.statusText}`);const r=await t.arrayBuffer(),o=await detectEncoding(r);return{content:o.text,encoding:o.encoding,size:r.byteLength}}catch(t){throw new Error(`无法读取文件 ${e}: ${t}`)}}async readFile(e){const t=await this.readFileContent(e);return new TextEncoder().encode(t.content).buffer}generateBookId(e){return`txt_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}extractTitle(e){return(e.split(/[/\\]/).pop()||"").replace(/\.[^/.]+$/,"")||"未命名文档"}detectChapters(e){const t=[],r=e.split(`
`),o=[/^第[一二三四五六七八九十\d]+章\s*.*/,/^第[一二三四五六七八九十\d]+节\s*.*/,/^Chapter\s+\d+.*/i,/^\d+\.\s*.{1,50}$/,/^[一二三四五六七八九十]+、.*/];let n=0,i=1;for(let a=0;a<r.length;a++){const l=r[a].trim();l.length>0&&o.some(m=>m.test(l))&&(t.push({id:`chapter_${i}`,title:l,startPosition:n,endPosition:n+l.length,level:1}),i++),n+=r[a].length+1}return t.length===0&&t.push({id:"chapter_1",title:"正文",startPosition:0,endPosition:e.length,level:1}),t}detectLanguage(e){const t=/[\u4e00-\u9fff]/,r=/[a-zA-Z]/,o=e.match(t),n=e.match(r),i=o?o.length:0,a=n?n.length:0;return i>a?"zh-CN":a>0?"en":"unknown"}formatPageContent(e){return`<p>${e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/\n\n+/g,"</p><p>").replace(/\n/g,"<br>")}</p>`}}class ki{constructor(e){R(this,"config");R(this,"totalPages",0);R(this,"currentScrollTop",0);R(this,"pageCache",new Map);R(this,"loadingPages",new Set);R(this,"onPageLoad");R(this,"onVisibleRangeChange");this.config=e}setTotalPages(e){this.totalPages=e}setPageLoader(e){this.onPageLoad=e}setVisibleRangeChangeHandler(e){this.onVisibleRangeChange=e}updateScrollPosition(e){var r;this.currentScrollTop=e;const t=this.calculateVisibleRange();this.preloadPages(t),this.cleanupCache(t),(r=this.onVisibleRangeChange)==null||r.call(this,t)}calculateVisibleRange(){const{containerHeight:e,pageHeight:t,bufferSize:r}=this.config,o=Math.floor(this.currentScrollTop/t)+1,n=Math.min(Math.ceil((this.currentScrollTop+e)/t),this.totalPages),i=Math.max(1,o-r),a=Math.min(this.totalPages,n+r);return{startPage:o,endPage:n,bufferStartPage:i,bufferEndPage:a,scrollOffset:this.currentScrollTop}}async preloadPages(e){const{preloadThreshold:t}=this.config;for(let o=e.startPage;o<=e.endPage;o++)!this.pageCache.has(o)&&!this.loadingPages.has(o)&&this.loadPage(o);for(let o=e.bufferStartPage;o<=e.bufferEndPage;o++)!this.pageCache.has(o)&&!this.loadingPages.has(o)&&setTimeout(()=>this.loadPage(o),50);const r=[];if(e.endPage+t<=this.totalPages)for(let o=1;o<=t;o++)r.push(e.endPage+o);if(e.startPage-t>=1)for(let o=1;o<=t;o++)r.push(e.startPage-o);r.forEach(o=>{!this.pageCache.has(o)&&!this.loadingPages.has(o)&&setTimeout(()=>this.loadPage(o),100)})}async loadPage(e){if(!(this.loadingPages.has(e)||this.pageCache.has(e))){this.loadingPages.add(e);try{if(this.onPageLoad){const t=await this.onPageLoad(e);this.pageCache.set(e,t)}}catch(t){console.error(`Failed to load page ${e}:`,t)}finally{this.loadingPages.delete(e)}}}cleanupCache(e){const{bufferSize:t}=this.config,r={start:Math.max(1,e.startPage-t*2),end:Math.min(this.totalPages,e.endPage+t*2)};for(const[o]of this.pageCache)(o<r.start||o>r.end)&&this.pageCache.delete(o)}getPageContent(e){return this.pageCache.get(e)||null}getVisiblePages(){const e=this.calculateVisibleRange(),t=[];for(let r=e.startPage;r<=e.endPage;r++){const o=this.pageCache.get(r);o&&t.push(o)}return t}scrollToPage(e){const{pageHeight:t}=this.config,r=(e-1)*t;return this.updateScrollPosition(r),r}getCacheStats(){const e=this.pageCache.size,t=this.loadingPages.size,r=this.totalPages>0?e/this.totalPages:0;return{cachedPages:e,loadingPages:t,totalPages:this.totalPages,cacheHitRate:r}}clearCache(){this.pageCache.clear(),this.loadingPages.clear()}async warmupCache(e=5){const t=Math.min(e,this.totalPages),r=[];for(let o=1;o<=t;o++)r.push(this.loadPage(o));await Promise.all(r)}}function Xi(s){return new ki(s)}function $i(s,e,t){const r=Math.ceil(s/e),o=Math.max(2,Math.min(5,Math.ceil(r/2))),n=Math.max(1,Math.min(3,Math.ceil(r/4)));return{containerHeight:s,pageHeight:e,bufferSize:o,preloadThreshold:n}}class ji{constructor(e){R(this,"config");R(this,"cache",new Map);R(this,"totalCacheSize",0);R(this,"checkTimer");R(this,"onMemoryWarning");R(this,"onMemoryCleanup");this.config=e,this.startMonitoring()}setMemoryWarningHandler(e){this.onMemoryWarning=e}setMemoryCleanupHandler(e){this.onMemoryCleanup=e}set(e,t){const r=this.calculateSize(t),o=Date.now();this.cache.has(e)&&this.delete(e);const n={key:e,data:t,size:r,lastAccessed:o,accessCount:1};this.cache.set(e,n),this.totalCacheSize+=r,this.checkMemoryUsage()}get(e){const t=this.cache.get(e);return t?(t.lastAccessed=Date.now(),t.accessCount++,t.data):null}delete(e){const t=this.cache.get(e);return t?(this.cache.delete(e),this.totalCacheSize-=t.size,!0):!1}has(e){return this.cache.has(e)}clear(){this.cache.clear(),this.totalCacheSize=0}getMemoryStats(){const e=this.getProcessMemoryUsage();return{usedMemory:e.used,totalMemory:e.total,usagePercentage:e.used/e.total*100,cacheSize:this.totalCacheSize,cacheItems:this.cache.size}}cleanup(e=!1){var o;const t=this.totalCacheSize;e?this.clear():this.performLRUCleanup();const r=t-this.totalCacheSize;return(o=this.onMemoryCleanup)==null||o.call(this,r),r}startMonitoring(){this.checkTimer=setInterval(()=>{this.checkMemoryUsage()},this.config.checkInterval)}stopMonitoring(){this.checkTimer&&(clearInterval(this.checkTimer),this.checkTimer=void 0)}checkMemoryUsage(){var t;const e=this.getMemoryStats();e.usagePercentage>=this.config.warningThreshold&&((t=this.onMemoryWarning)==null||t.call(this,e)),e.usagePercentage>=this.config.cleanupThreshold&&this.performLRUCleanup()}performLRUCleanup(){const e=Array.from(this.cache.values()).sort((r,o)=>{const n=r.lastAccessed+r.accessCount*1e3,i=o.lastAccessed+o.accessCount*1e3;return n-i}),t=Math.ceil(e.length*.25);for(let r=0;r<t&&r<e.length;r++)this.delete(e[r].key)}calculateSize(e){return typeof e=="string"?e.length*2:e instanceof ArrayBuffer||e instanceof Uint8Array?e.byteLength:typeof e=="object"&&e!==null?JSON.stringify(e).length*2:64}getProcessMemoryUsage(){if(typeof process<"u"&&process.memoryUsage){const e=process.memoryUsage();return{used:e.heapUsed,total:e.heapTotal}}else if(typeof performance<"u"&&performance.memory){const e=performance.memory;return{used:e.usedJSHeapSize,total:e.totalJSHeapSize}}else return{used:this.totalCacheSize,total:this.config.maxMemoryUsage}}destroy(){this.stopMonitoring(),this.clear()}}function zi(s){const e={maxMemoryUsage:209715200,warningThreshold:80,cleanupThreshold:90,checkInterval:5e3};return new ji({...e,...s})}function Hi(s){const e=["B","KB","MB","GB"];let t=s,r=0;for(;t>=1024&&r<e.length-1;)t/=1024,r++;return`${t.toFixed(2)} ${e[r]}`}class xe{constructor(e,t){R(this,"baseReader");R(this,"virtualScrollManager");R(this,"memoryManager");R(this,"config");R(this,"performanceStats");R(this,"renderTimes",[]);R(this,"events",{});this.baseReader=new On(e),this.config={enableVirtualScroll:!0,enableMemoryManagement:!0,enablePerformanceMonitoring:!0,largeFileThreshold:10*1024*1024,...t},this.performanceStats={memory:{usedMemory:0,totalMemory:0,usagePercentage:0,cacheSize:0,cacheItems:0},virtualScroll:{cachedPages:0,loadingPages:0,totalPages:0,cacheHitRate:0},rendering:{averageRenderTime:0,lastRenderTime:0,totalRenders:0}},this.initializeOptimizations()}initializeOptimizations(){this.config.enableMemoryManagement&&(this.memoryManager=zi({maxMemoryUsage:200*1024*1024,warningThreshold:80,cleanupThreshold:90}),this.memoryManager.setMemoryWarningHandler(e=>{var t,r;console.warn("Memory usage warning:",Hi(e.usedMemory)),(r=(t=this.events).onError)==null||r.call(t,new Error(`内存使用过高: ${e.usagePercentage.toFixed(1)}%`))}))}async loadBook(e){var r,o,n;const t=performance.now();try{const i=await this.baseReader.loadBook(e);return((r=i.metadata)==null?void 0:r.fileSize)&&i.metadata.fileSize>this.config.largeFileThreshold&&this.config.enableVirtualScroll&&this.initializeVirtualScroll(i),this.memoryManager&&this.memoryManager.set(`book_${i.id}`,i),this.recordRenderTime(performance.now()-t),i}catch(i){throw(n=(o=this.events).onError)==null||n.call(o,i instanceof Error?i:new Error(String(i))),i}}initializeVirtualScroll(e){const r={...$i(600,100,e.totalPages),...this.config.virtualScrollConfig};this.virtualScrollManager=Xi(r),this.virtualScrollManager.setTotalPages(e.totalPages),this.virtualScrollManager.setPageLoader(async o=>{const n=await this.baseReader.getPageContent(o);return{pageNumber:o,startPosition:0,endPosition:0,content:n,lineCount:0}}),this.virtualScrollManager.setVisibleRangeChangeHandler(o=>{this.updatePerformanceStats()}),this.virtualScrollManager.warmupCache(3)}async getPageContent(e){var r,o;const t=performance.now();try{let n;if(this.virtualScrollManager){const i=this.virtualScrollManager.getPageContent(e);i?n=i.content:n=await this.baseReader.getPageContent(e)}else n=await this.baseReader.getPageContent(e);return this.memoryManager&&this.memoryManager.set(`page_${e}`,n),this.recordRenderTime(performance.now()-t),n}catch(n){throw(o=(r=this.events).onError)==null||o.call(r,n instanceof Error?n:new Error(String(n))),n}}async goToPage(e){this.virtualScrollManager&&this.virtualScrollManager.scrollToPage(e),await this.baseReader.goToPage(e)}async nextPage(){return await this.baseReader.nextPage()}async previousPage(){return await this.baseReader.previousPage()}async search(e){return await this.baseReader.search(e)}getCurrentPosition(){return this.baseReader.getCurrentPosition()}async setPosition(e){await this.baseReader.setPosition(e)}applySettings(e){this.baseReader.applySettings(e)}getChapters(){return this.baseReader.getChapters()}async goToChapter(e){await this.baseReader.goToChapter(e)}setEventListeners(e){this.events={...this.events,...e},this.baseReader.setEventListeners(e)}updateScrollPosition(e){this.virtualScrollManager&&this.virtualScrollManager.updateScrollPosition(e)}getVisiblePages(){return this.virtualScrollManager?this.virtualScrollManager.getVisiblePages():[]}getPerformanceStats(){return this.updatePerformanceStats(),{...this.performanceStats}}updatePerformanceStats(){if(this.memoryManager&&(this.performanceStats.memory=this.memoryManager.getMemoryStats()),this.virtualScrollManager&&(this.performanceStats.virtualScroll=this.virtualScrollManager.getCacheStats()),this.renderTimes.length>0){const e=this.renderTimes.reduce((t,r)=>t+r,0);this.performanceStats.rendering.averageRenderTime=e/this.renderTimes.length,this.performanceStats.rendering.lastRenderTime=this.renderTimes[this.renderTimes.length-1],this.performanceStats.rendering.totalRenders=this.renderTimes.length}}recordRenderTime(e){this.renderTimes.push(e),this.renderTimes.length>100&&(this.renderTimes=this.renderTimes.slice(-100))}cleanupMemory(){return this.memoryManager?this.memoryManager.cleanup():0}dispose(){this.baseReader.dispose(),this.virtualScrollManager&&this.virtualScrollManager.clearCache(),this.memoryManager&&this.memoryManager.destroy(),this.renderTimes=[],this.events={}}}class qi{constructor(){R(this,"readers",new Map)}registerHandlers(){console.log("TxtReaderIPCHandler: 开始注册IPC处理器..."),this.registerFileHandlers(),this.registerReaderHandlers(),this.registerSearchHandlers(),this.registerBookmarkHandlers(),this.registerPerformanceHandlers(),console.log("TxtReaderIPCHandler: IPC处理器注册完成")}registerFileHandlers(){O.ipcMain.handle("txt-reader:detect-encoding",async(e,t)=>{var r;try{if(console.log(`IPC: 开始检测文件编码 ${t}`),!t||typeof t!="string")throw new Error("文件路径无效");if(!$.existsSync(t))throw new Error("文件不存在");const o=$.readFileSync(t,{flag:"r"}),n=Math.min(o.length,1024*1024),i=o.slice(0,n).buffer,a=await Ie(i);return console.log(`IPC: 成功检测文件编码 ${t}: ${a.encoding} (${a.confidence})`),{encoding:a.encoding,confidence:a.confidence,text:(r=a.text)==null?void 0:r.substring(0,500)}}catch(o){return console.error(`IPC: 检测文件编码失败 ${t}:`,o),{encoding:"utf-8",confidence:0,error:o instanceof Error?o.message:"编码检测失败"}}}),O.ipcMain.handle("txt-reader:get-file-info",async(e,t)=>{try{if(console.log(`IPC: 开始获取文件信息 ${t}`),!t||typeof t!="string")throw new Error("文件路径无效");if(!$.existsSync(t))throw new Error("文件不存在");const r=$.statSync(t),o=B.extname(t).toLowerCase();if(o!==".txt")throw new Error("不支持的文件格式，仅支持TXT文件");const n=$.readFileSync(t,{flag:"r"}),i=Math.min(n.length,1024*1024),a=n.slice(0,i).buffer,l=await Ie(a),f={path:t,name:B.basename(t,o),size:r.size,encoding:l.encoding,lastModified:r.mtime,isValid:!0};return console.log(`IPC: 成功获取文件信息 ${t}`),f}catch(r){return console.error(`IPC: 获取文件信息失败 ${t}:`,r),{path:t,name:B.basename(t),size:0,encoding:"utf-8",lastModified:new Date,isValid:!1,error:r instanceof Error?r.message:"获取文件信息失败"}}}),O.ipcMain.handle("txt-reader:read-file",async(e,t,r)=>{try{if(console.log(`IPC: 开始读取文件 ${t}`),!t||typeof t!="string")throw new Error("文件路径无效");let o=t;if(t.startsWith("/books/")&&(o=t.substring(1),console.log(`TxtReader: 路径格式修复 ${t} -> ${o}`)),!$.existsSync(o))throw new Error("文件不存在");const n=$.readFileSync(o);let i=r;if(!i){const l=n.slice(0,Math.min(n.length,1048576)).buffer;i=(await Ie(l)).encoding}let a;return i==="utf-8"?a=n.toString("utf-8"):a=require("iconv-lite").decode(n,i),console.log(`IPC: 成功读取文件 ${t}, 编码: ${i}, 大小: ${n.length}`),{content:a,encoding:i,size:n.length}}catch(o){return console.error(`IPC: 读取文件失败 ${t}:`,o),{content:"",encoding:"utf-8",size:0,error:o instanceof Error?o.message:"读取文件失败"}}}),O.ipcMain.handle("txt-reader:batch-detect-encoding",async(e,t)=>{try{if(console.log(`IPC: 开始批量检测编码，文件数量: ${t.length}`),!Array.isArray(t))throw new Error("文件路径列表无效");const r=[];for(const o of t)try{if(!$.existsSync(o)){r.push({filePath:o,encoding:"utf-8",confidence:0,error:"文件不存在"});continue}const n=$.readFileSync(o,{flag:"r"}),i=Math.min(n.length,1024*1024),a=n.slice(0,i).buffer,l=await Ie(a);r.push({filePath:o,encoding:l.encoding,confidence:l.confidence})}catch(n){r.push({filePath:o,encoding:"utf-8",confidence:0,error:n instanceof Error?n.message:"检测失败"})}return console.log(`IPC: 批量编码检测完成，成功: ${r.filter(o=>!o.error).length}`),r}catch(r){throw console.error("IPC: 批量编码检测失败:",r),new Error(r instanceof Error?r.message:"批量编码检测失败")}})}registerReaderHandlers(){O.ipcMain.handle("txt-reader:create",async(e,t,r,o)=>{try{if(console.log(`IPC: 开始创建阅读器 ${t} for ${r}`),!t||!r)throw new Error("阅读器ID或文件路径无效");const n=await this.getFileInfo(r);if(!n.isValid)throw new Error(n.error||"文件无效");const i=(o==null?void 0:o.enableVirtualScroll)||(o==null?void 0:o.enableMemoryManagement)||n.size>((o==null?void 0:o.largeFileThreshold)||10*1024*1024);let a;i?a=new xe(void 0,o):a=new On;const l=await a.loadBook(r);return this.readers.set(t,a),console.log(`IPC: 成功创建阅读器 ${t}`),{success:!0,book:l}}catch(n){return console.error(`IPC: 创建阅读器失败 ${t}:`,n),{success:!1,error:n instanceof Error?n.message:"创建阅读器失败"}}}),O.ipcMain.handle("txt-reader:destroy",async(e,t)=>{try{const r=this.readers.get(t);return r?(r.dispose(),this.readers.delete(t),console.log(`IPC: 成功销毁阅读器 ${t}`),!0):!1}catch(r){return console.error(`IPC: 销毁阅读器失败 ${t}:`,r),!1}}),O.ipcMain.handle("txt-reader:get-page",async(e,t,r)=>{try{const o=this.readers.get(t);if(!o)throw new Error("阅读器实例不存在");return{content:await o.getPageContent(r)}}catch(o){return console.error(`IPC: 获取页面内容失败 ${t}:`,o),{content:"",error:o instanceof Error?o.message:"获取页面内容失败"}}}),O.ipcMain.handle("txt-reader:go-to-page",async(e,t,r)=>{try{const o=this.readers.get(t);if(!o)throw new Error("阅读器实例不存在");return await o.goToPage(r),!0}catch(o){return console.error(`IPC: 跳转页面失败 ${t}:`,o),!1}}),O.ipcMain.handle("txt-reader:next-page",async(e,t)=>{try{const r=this.readers.get(t);if(!r)throw new Error("阅读器实例不存在");return await r.nextPage()}catch(r){return console.error(`IPC: 下一页失败 ${t}:`,r),!1}}),O.ipcMain.handle("txt-reader:previous-page",async(e,t)=>{try{const r=this.readers.get(t);if(!r)throw new Error("阅读器实例不存在");return await r.previousPage()}catch(r){return console.error(`IPC: 上一页失败 ${t}:`,r),!1}}),O.ipcMain.handle("txt-reader:get-position",async(e,t)=>{try{const r=this.readers.get(t);if(!r)throw new Error("阅读器实例不存在");return r.getCurrentPosition()}catch(r){return console.error(`IPC: 获取当前位置失败 ${t}:`,r),null}}),O.ipcMain.handle("txt-reader:set-position",async(e,t,r)=>{try{const o=this.readers.get(t);if(!o)throw new Error("阅读器实例不存在");return await o.setPosition(r),!0}catch(o){return console.error(`IPC: 设置阅读位置失败 ${t}:`,o),!1}}),O.ipcMain.handle("txt-reader:get-chapters",async(e,t)=>{try{const r=this.readers.get(t);if(!r)throw new Error("阅读器实例不存在");return r.getChapters()}catch(r){return console.error(`IPC: 获取章节列表失败 ${t}:`,r),[]}}),O.ipcMain.handle("txt-reader:go-to-chapter",async(e,t,r)=>{try{const o=this.readers.get(t);if(!o)throw new Error("阅读器实例不存在");return await o.goToChapter(r),!0}catch(o){return console.error(`IPC: 跳转章节失败 ${t}:`,o),!1}})}registerSearchHandlers(){O.ipcMain.handle("txt-reader:search",async(e,t,r,o)=>{try{const n=this.readers.get(t);if(!n)throw new Error("阅读器实例不存在");if(!r||typeof r!="string")throw new Error("搜索关键词无效");const i=await n.search(r);let a=i;return o!=null&&o.maxResults&&o.maxResults>0&&(a=i.slice(0,o.maxResults)),console.log(`IPC: 搜索完成 ${t}, 关键词: "${r}", 结果: ${a.length}`),a}catch(n){return console.error(`IPC: 搜索失败 ${t}:`,n),[]}}),O.ipcMain.handle("txt-reader:highlight-search",async(e,t,r,o)=>{try{const n=this.readers.get(t);if(!n)throw new Error("阅读器实例不存在");const i=await n.getPageContent(o),a=[];if(r&&r.trim()){const l=new RegExp(r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gi");let f;for(;(f=l.exec(i))!==null;)a.push({start:f.index,end:f.index+f[0].length,text:f[0]})}return{content:i,highlights:a}}catch(n){return console.error(`IPC: 高亮搜索失败 ${t}:`,n),{content:"",highlights:[],error:n instanceof Error?n.message:"高亮搜索失败"}}})}registerBookmarkHandlers(){O.ipcMain.handle("txt-reader:add-bookmark",async(e,t,r,o,n,i,a)=>{try{const l=`bookmark_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;return console.log(`IPC: 添加书签成功 ${t}: ${r}`),{success:!0,bookmarkId:l}}catch(l){return console.error(`IPC: 添加书签失败 ${t}:`,l),{success:!1,error:l instanceof Error?l.message:"添加书签失败"}}}),O.ipcMain.handle("txt-reader:get-bookmarks",async(e,t)=>{try{return[]}catch(r){return console.error(`IPC: 获取书签失败 ${t}:`,r),[]}})}registerPerformanceHandlers(){O.ipcMain.handle("txt-reader:get-performance-stats",async(e,t)=>{try{const r=this.readers.get(t);if(!r)throw new Error("阅读器实例不存在");return r instanceof xe?r.getPerformanceStats():{memory:{usedMemory:0,totalMemory:0,usagePercentage:0,cacheSize:0,cacheItems:0},virtualScroll:{cachedPages:0,loadingPages:0,totalPages:0,cacheHitRate:0},rendering:{averageRenderTime:0,lastRenderTime:0,totalRenders:0}}}catch(r){return console.error(`IPC: 获取性能统计失败 ${t}:`,r),null}}),O.ipcMain.handle("txt-reader:cleanup-memory",async(e,t)=>{try{const r=this.readers.get(t);if(!r)throw new Error("阅读器实例不存在");let o=0;return r instanceof xe&&(o=r.cleanupMemory()),{success:!0,freedMemory:o}}catch(r){return console.error(`IPC: 清理内存失败 ${t}:`,r),{success:!1,freedMemory:0,error:r instanceof Error?r.message:"清理内存失败"}}}),O.ipcMain.handle("txt-reader:update-scroll-position",async(e,t,r)=>{try{const o=this.readers.get(t);if(!o)throw new Error("阅读器实例不存在");return o instanceof xe?(o.updateScrollPosition(r),!0):!1}catch(o){return console.error(`IPC: 更新滚动位置失败 ${t}:`,o),!1}}),O.ipcMain.handle("txt-reader:get-visible-pages",async(e,t)=>{try{const r=this.readers.get(t);if(!r)throw new Error("阅读器实例不存在");return r instanceof xe?r.getVisiblePages():[]}catch(r){return console.error(`IPC: 获取可见页面失败 ${t}:`,r),[]}})}async getFileInfo(e){try{if(!$.existsSync(e))throw new Error("文件不存在");const t=$.statSync(e),r=B.extname(e).toLowerCase();if(r!==".txt")throw new Error("不支持的文件格式");const o=$.readFileSync(e,{flag:"r"}),n=Math.min(o.length,1024*1024),i=o.slice(0,n).buffer,a=await Ie(i);return{path:e,name:B.basename(e,r),size:t.size,encoding:a.encoding,lastModified:t.mtime,isValid:!0}}catch(t){return{path:e,name:B.basename(e),size:0,encoding:"utf-8",lastModified:new Date,isValid:!1,error:t instanceof Error?t.message:"获取文件信息失败"}}}unregisterHandlers(){["txt-reader:detect-encoding","txt-reader:get-file-info","txt-reader:read-file","txt-reader:batch-detect-encoding","txt-reader:create","txt-reader:destroy","txt-reader:get-page","txt-reader:go-to-page","txt-reader:next-page","txt-reader:previous-page","txt-reader:get-position","txt-reader:set-position","txt-reader:get-chapters","txt-reader:go-to-chapter","txt-reader:search","txt-reader:highlight-search","txt-reader:add-bookmark","txt-reader:get-bookmarks","txt-reader:get-performance-stats","txt-reader:cleanup-memory","txt-reader:update-scroll-position","txt-reader:get-visible-pages"].forEach(t=>{O.ipcMain.removeAllListeners(t)}),this.readers.forEach(t=>{t.dispose()}),this.readers.clear(),console.log("TXT阅读器IPC处理器注销完成")}}var ve={exports:{}},Mn={LOCHDR:30,LOCSIG:67324752,LOCVER:4,LOCFLG:6,LOCHOW:8,LOCTIM:10,LOCCRC:14,LOCSIZ:18,LOCLEN:22,LOCNAM:26,LOCEXT:28,EXTSIG:134695760,EXTHDR:16,EXTCRC:4,EXTSIZ:8,EXTLEN:12,CENHDR:46,CENSIG:33639248,CENVEM:4,CENVER:6,CENFLG:8,CENHOW:10,CENTIM:12,CENCRC:16,CENSIZ:20,CENLEN:24,CENNAM:28,CENEXT:30,CENCOM:32,CENDSK:34,CENATT:36,CENATX:38,CENOFF:42,ENDHDR:22,ENDSIG:101010256,ENDSUB:8,ENDTOT:10,ENDSIZ:12,ENDOFF:16,ENDCOM:20,END64HDR:20,END64SIG:117853008,END64START:4,END64OFF:8,END64NUMDISKS:16,ZIP64SIG:101075792,ZIP64HDR:56,ZIP64LEAD:12,ZIP64SIZE:4,ZIP64VEM:12,ZIP64VER:14,ZIP64DSK:16,ZIP64DSKDIR:20,ZIP64SUB:24,ZIP64TOT:32,ZIP64SIZB:40,ZIP64OFF:48,ZIP64EXTRA:56,STORED:0,SHRUNK:1,REDUCED1:2,REDUCED2:3,REDUCED3:4,REDUCED4:5,IMPLODED:6,DEFLATED:8,ENHANCED_DEFLATED:9,PKWARE:10,BZIP2:12,LZMA:14,IBM_TERSE:18,IBM_LZ77:19,AES_ENCRYPT:99,FLG_ENC:1,FLG_COMP1:2,FLG_COMP2:4,FLG_DESC:8,FLG_ENH:16,FLG_PATCH:32,FLG_STR:64,FLG_EFS:2048,FLG_MSK:4096,FILE:2,BUFFER:1,NONE:0,EF_ID:0,EF_SIZE:2,ID_ZIP64:1,ID_AVINFO:7,ID_PFS:8,ID_OS2:9,ID_NTFS:10,ID_OPENVMS:12,ID_UNIX:13,ID_FORK:14,ID_PATCH:15,ID_X509_PKCS7:20,ID_X509_CERTID_F:21,ID_X509_CERTID_C:22,ID_STRONGENC:23,ID_RECORD_MGT:24,ID_X509_PKCS7_RL:25,ID_IBM1:101,ID_IBM2:102,ID_POSZIP:18064,EF_ZIP64_OR_32:4294967295,EF_ZIP64_OR_16:65535,EF_ZIP64_SUNCOMP:0,EF_ZIP64_SCOMP:8,EF_ZIP64_RHO:16,EF_ZIP64_DSN:24},Re={};(function(s){const e={INVALID_LOC:"Invalid LOC header (bad signature)",INVALID_CEN:"Invalid CEN header (bad signature)",INVALID_END:"Invalid END header (bad signature)",DESCRIPTOR_NOT_EXIST:"No descriptor present",DESCRIPTOR_UNKNOWN:"Unknown descriptor format",DESCRIPTOR_FAULTY:"Descriptor data is malformed",NO_DATA:"Nothing to decompress",BAD_CRC:"CRC32 checksum failed {0}",FILE_IN_THE_WAY:"There is a file in the way: {0}",UNKNOWN_METHOD:"Invalid/unsupported compression method",AVAIL_DATA:"inflate::Available inflate data did not terminate",INVALID_DISTANCE:"inflate::Invalid literal/length or distance code in fixed or dynamic block",TO_MANY_CODES:"inflate::Dynamic block code description: too many length or distance codes",INVALID_REPEAT_LEN:"inflate::Dynamic block code description: repeat more than specified lengths",INVALID_REPEAT_FIRST:"inflate::Dynamic block code description: repeat lengths with no first length",INCOMPLETE_CODES:"inflate::Dynamic block code description: code lengths codes incomplete",INVALID_DYN_DISTANCE:"inflate::Dynamic block code description: invalid distance code lengths",INVALID_CODES_LEN:"inflate::Dynamic block code description: invalid literal/length code lengths",INVALID_STORE_BLOCK:"inflate::Stored block length did not match one's complement",INVALID_BLOCK_TYPE:"inflate::Invalid block type (type == 3)",CANT_EXTRACT_FILE:"Could not extract the file",CANT_OVERRIDE:"Target file already exists",DISK_ENTRY_TOO_LARGE:"Number of disk entries is too large",NO_ZIP:"No zip file was loaded",NO_ENTRY:"Entry doesn't exist",DIRECTORY_CONTENT_ERROR:"A directory cannot have content",FILE_NOT_FOUND:'File not found: "{0}"',NOT_IMPLEMENTED:"Not implemented",INVALID_FILENAME:"Invalid filename",INVALID_FORMAT:"Invalid or unsupported zip format. No END header found",INVALID_PASS_PARAM:"Incompatible password parameter",WRONG_PASSWORD:"Wrong Password",COMMENT_TOO_LONG:"Comment is too long",EXTRA_FIELD_PARSE_ERROR:"Extra field parsing error"};function t(r){return function(...o){return o.length&&(r=r.replace(/\{(\d)\}/g,(n,i)=>o[i]||"")),new Error("ADM-ZIP: "+r)}}for(const r of Object.keys(e))s[r]=t(e[r])})(Re);const Vi=$,ee=B,Pr=Mn,Wi=Re,Gi=typeof process=="object"&&process.platform==="win32",Or=s=>typeof s=="object"&&s!==null,An=new Uint32Array(256).map((s,e)=>{for(let t=0;t<8;t++)e&1?e=3988292384^e>>>1:e>>>=1;return e>>>0});function Y(s){this.sep=ee.sep,this.fs=Vi,Or(s)&&Or(s.fs)&&typeof s.fs.statSync=="function"&&(this.fs=s.fs)}var Zi=Y;Y.prototype.makeDir=function(s){const e=this;function t(r){let o=r.split(e.sep)[0];r.split(e.sep).forEach(function(n){if(!(!n||n.substr(-1,1)===":")){o+=e.sep+n;var i;try{i=e.fs.statSync(o)}catch{e.fs.mkdirSync(o)}if(i&&i.isFile())throw Wi.FILE_IN_THE_WAY(`"${o}"`)}})}t(s)};Y.prototype.writeFileTo=function(s,e,t,r){const o=this;if(o.fs.existsSync(s)){if(!t)return!1;var n=o.fs.statSync(s);if(n.isDirectory())return!1}var i=ee.dirname(s);o.fs.existsSync(i)||o.makeDir(i);var a;try{a=o.fs.openSync(s,"w",438)}catch{o.fs.chmodSync(s,438),a=o.fs.openSync(s,"w",438)}if(a)try{o.fs.writeSync(a,e,0,e.length,0)}finally{o.fs.closeSync(a)}return o.fs.chmodSync(s,r||438),!0};Y.prototype.writeFileToAsync=function(s,e,t,r,o){typeof r=="function"&&(o=r,r=void 0);const n=this;n.fs.exists(s,function(i){if(i&&!t)return o(!1);n.fs.stat(s,function(a,l){if(i&&l.isDirectory())return o(!1);var f=ee.dirname(s);n.fs.exists(f,function(m){m||n.makeDir(f),n.fs.open(s,"w",438,function(y,w){y?n.fs.chmod(s,438,function(){n.fs.open(s,"w",438,function(h,E){n.fs.write(E,e,0,e.length,0,function(){n.fs.close(E,function(){n.fs.chmod(s,r||438,function(){o(!0)})})})})}):w?n.fs.write(w,e,0,e.length,0,function(){n.fs.close(w,function(){n.fs.chmod(s,r||438,function(){o(!0)})})}):n.fs.chmod(s,r||438,function(){o(!0)})})})})})};Y.prototype.findFiles=function(s){const e=this;function t(r,o,n){let i=[];return e.fs.readdirSync(r).forEach(function(a){const l=ee.join(r,a),f=e.fs.statSync(l);i.push(ee.normalize(l)+(f.isDirectory()?e.sep:"")),f.isDirectory()&&n&&(i=i.concat(t(l,o,n)))}),i}return t(s,void 0,!0)};Y.prototype.findFilesAsync=function(s,e){const t=this;let r=[];t.fs.readdir(s,function(o,n){if(o)return e(o);let i=n.length;if(!i)return e(null,r);n.forEach(function(a){a=ee.join(s,a),t.fs.stat(a,function(l,f){if(l)return e(l);f&&(r.push(ee.normalize(a)+(f.isDirectory()?t.sep:"")),f.isDirectory()?t.findFilesAsync(a,function(m,y){if(m)return e(m);r=r.concat(y),--i||e(null,r)}):--i||e(null,r))})})})};Y.prototype.getAttributes=function(){};Y.prototype.setAttributes=function(){};Y.crc32update=function(s,e){return An[(s^e)&255]^s>>>8};Y.crc32=function(s){typeof s=="string"&&(s=Buffer.from(s,"utf8"));let e=s.length,t=-1;for(let r=0;r<e;)t=Y.crc32update(t,s[r++]);return~t>>>0};Y.methodToString=function(s){switch(s){case Pr.STORED:return"STORED ("+s+")";case Pr.DEFLATED:return"DEFLATED ("+s+")";default:return"UNSUPPORTED ("+s+")"}};Y.canonical=function(s){if(!s)return"";const e=ee.posix.normalize("/"+s.split("\\").join("/"));return ee.join(".",e)};Y.zipnamefix=function(s){if(!s)return"";const e=ee.posix.normalize("/"+s.split("\\").join("/"));return ee.posix.join(".",e)};Y.findLast=function(s,e){if(!Array.isArray(s))throw new TypeError("arr is not array");const t=s.length>>>0;for(let r=t-1;r>=0;r--)if(e(s[r],r,s))return s[r]};Y.sanitize=function(s,e){s=ee.resolve(ee.normalize(s));for(var t=e.split("/"),r=0,o=t.length;r<o;r++){var n=ee.normalize(ee.join(s,t.slice(r,o).join(ee.sep)));if(n.indexOf(s)===0)return n}return ee.normalize(ee.join(s,ee.basename(e)))};Y.toBuffer=function(e,t){return Buffer.isBuffer(e)?e:e instanceof Uint8Array?Buffer.from(e):typeof e=="string"?t(e):Buffer.alloc(0)};Y.readBigUInt64LE=function(s,e){var t=Buffer.from(s.slice(e,e+8));return t.swap64(),parseInt(`0x${t.toString("hex")}`)};Y.fromDOS2Date=function(s){return new Date((s>>25&127)+1980,Math.max((s>>21&15)-1,0),Math.max(s>>16&31,1),s>>11&31,s>>5&63,(s&31)<<1)};Y.fromDate2DOS=function(s){let e=0,t=0;return s.getFullYear()>1979&&(e=(s.getFullYear()-1980&127)<<9|s.getMonth()+1<<5|s.getDate(),t=s.getHours()<<11|s.getMinutes()<<5|s.getSeconds()>>1),e<<16|t};Y.isWin=Gi;Y.crcTable=An;const Yi=B;var Ki=function(s,{fs:e}){var t=s||"",r=n(),o=null;function n(){return{directory:!1,readonly:!1,hidden:!1,executable:!1,mtime:0,atime:0}}return t&&e.existsSync(t)?(o=e.statSync(t),r.directory=o.isDirectory(),r.mtime=o.mtime,r.atime=o.atime,r.executable=(73&o.mode)!==0,r.readonly=(128&o.mode)===0,r.hidden=Yi.basename(t)[0]==="."):console.warn("Invalid path: "+t),{get directory(){return r.directory},get readOnly(){return r.readonly},get hidden(){return r.hidden},get mtime(){return r.mtime},get atime(){return r.atime},get executable(){return r.executable},decodeAttributes:function(){},encodeAttributes:function(){},toJSON:function(){return{path:t,isDirectory:r.directory,isReadOnly:r.readonly,isHidden:r.hidden,isExecutable:r.executable,mTime:r.mtime,aTime:r.atime}},toString:function(){return JSON.stringify(this.toJSON(),null,"	")}}},Ji={efs:!0,encode:s=>Buffer.from(s,"utf8"),decode:s=>s.toString("utf8")};ve.exports=Zi;ve.exports.Constants=Mn;ve.exports.Errors=Re;ve.exports.FileAttr=Ki;ve.exports.decoder=Ji;var Ne=ve.exports,Fe={},pe=Ne,A=pe.Constants,Qi=function(){var s=20,e=10,t=0,r=0,o=0,n=0,i=0,a=0,l=0,f=0,m=0,y=0,w=0,h=0,E=0;s|=pe.isWin?2560:768,t|=A.FLG_EFS;const g={extraLen:0},_=u=>Math.max(0,u)>>>0,b=u=>Math.max(0,u)&255;return o=pe.fromDate2DOS(new Date),{get made(){return s},set made(u){s=u},get version(){return e},set version(u){e=u},get flags(){return t},set flags(u){t=u},get flags_efs(){return(t&A.FLG_EFS)>0},set flags_efs(u){u?t|=A.FLG_EFS:t&=~A.FLG_EFS},get flags_desc(){return(t&A.FLG_DESC)>0},set flags_desc(u){u?t|=A.FLG_DESC:t&=~A.FLG_DESC},get method(){return r},set method(u){switch(u){case A.STORED:this.version=10;case A.DEFLATED:default:this.version=20}r=u},get time(){return pe.fromDOS2Date(this.timeval)},set time(u){this.timeval=pe.fromDate2DOS(u)},get timeval(){return o},set timeval(u){o=_(u)},get timeHighByte(){return b(o>>>8)},get crc(){return n},set crc(u){n=_(u)},get compressedSize(){return i},set compressedSize(u){i=_(u)},get size(){return a},set size(u){a=_(u)},get fileNameLength(){return l},set fileNameLength(u){l=u},get extraLength(){return f},set extraLength(u){f=u},get extraLocalLength(){return g.extraLen},set extraLocalLength(u){g.extraLen=u},get commentLength(){return m},set commentLength(u){m=u},get diskNumStart(){return y},set diskNumStart(u){y=_(u)},get inAttr(){return w},set inAttr(u){w=_(u)},get attr(){return h},set attr(u){h=_(u)},get fileAttr(){return(h||0)>>16&4095},get offset(){return E},set offset(u){E=_(u)},get encrypted(){return(t&A.FLG_ENC)===A.FLG_ENC},get centralHeaderSize(){return A.CENHDR+l+f+m},get realDataOffset(){return E+A.LOCHDR+g.fnameLen+g.extraLen},get localHeader(){return g},loadLocalHeaderFromBinary:function(u){var c=u.slice(E,E+A.LOCHDR);if(c.readUInt32LE(0)!==A.LOCSIG)throw pe.Errors.INVALID_LOC();g.version=c.readUInt16LE(A.LOCVER),g.flags=c.readUInt16LE(A.LOCFLG),g.method=c.readUInt16LE(A.LOCHOW),g.time=c.readUInt32LE(A.LOCTIM),g.crc=c.readUInt32LE(A.LOCCRC),g.compressedSize=c.readUInt32LE(A.LOCSIZ),g.size=c.readUInt32LE(A.LOCLEN),g.fnameLen=c.readUInt16LE(A.LOCNAM),g.extraLen=c.readUInt16LE(A.LOCEXT);const p=E+A.LOCHDR+g.fnameLen,v=p+g.extraLen;return u.slice(p,v)},loadFromBinary:function(u){if(u.length!==A.CENHDR||u.readUInt32LE(0)!==A.CENSIG)throw pe.Errors.INVALID_CEN();s=u.readUInt16LE(A.CENVEM),e=u.readUInt16LE(A.CENVER),t=u.readUInt16LE(A.CENFLG),r=u.readUInt16LE(A.CENHOW),o=u.readUInt32LE(A.CENTIM),n=u.readUInt32LE(A.CENCRC),i=u.readUInt32LE(A.CENSIZ),a=u.readUInt32LE(A.CENLEN),l=u.readUInt16LE(A.CENNAM),f=u.readUInt16LE(A.CENEXT),m=u.readUInt16LE(A.CENCOM),y=u.readUInt16LE(A.CENDSK),w=u.readUInt16LE(A.CENATT),h=u.readUInt32LE(A.CENATX),E=u.readUInt32LE(A.CENOFF)},localHeaderToBinary:function(){var u=Buffer.alloc(A.LOCHDR);return u.writeUInt32LE(A.LOCSIG,0),u.writeUInt16LE(e,A.LOCVER),u.writeUInt16LE(t,A.LOCFLG),u.writeUInt16LE(r,A.LOCHOW),u.writeUInt32LE(o,A.LOCTIM),u.writeUInt32LE(n,A.LOCCRC),u.writeUInt32LE(i,A.LOCSIZ),u.writeUInt32LE(a,A.LOCLEN),u.writeUInt16LE(l,A.LOCNAM),u.writeUInt16LE(g.extraLen,A.LOCEXT),u},centralHeaderToBinary:function(){var u=Buffer.alloc(A.CENHDR+l+f+m);return u.writeUInt32LE(A.CENSIG,0),u.writeUInt16LE(s,A.CENVEM),u.writeUInt16LE(e,A.CENVER),u.writeUInt16LE(t,A.CENFLG),u.writeUInt16LE(r,A.CENHOW),u.writeUInt32LE(o,A.CENTIM),u.writeUInt32LE(n,A.CENCRC),u.writeUInt32LE(i,A.CENSIZ),u.writeUInt32LE(a,A.CENLEN),u.writeUInt16LE(l,A.CENNAM),u.writeUInt16LE(f,A.CENEXT),u.writeUInt16LE(m,A.CENCOM),u.writeUInt16LE(y,A.CENDSK),u.writeUInt16LE(w,A.CENATT),u.writeUInt32LE(h,A.CENATX),u.writeUInt32LE(E,A.CENOFF),u},toJSON:function(){const u=function(c){return c+" bytes"};return{made:s,version:e,flags:t,method:pe.methodToString(r),time:this.time,crc:"0x"+n.toString(16).toUpperCase(),compressedSize:u(i),size:u(a),fileNameLength:u(l),extraLength:u(f),commentLength:u(m),diskNumStart:y,inAttr:w,attr:h,offset:E,centralHeaderSize:u(A.CENHDR+l+f+m)}},toString:function(){return JSON.stringify(this.toJSON(),null,"	")}}},we=Ne,V=we.Constants,eo=function(){var s=0,e=0,t=0,r=0,o=0;return{get diskEntries(){return s},set diskEntries(n){s=e=n},get totalEntries(){return e},set totalEntries(n){e=s=n},get size(){return t},set size(n){t=n},get offset(){return r},set offset(n){r=n},get commentLength(){return o},set commentLength(n){o=n},get mainHeaderSize(){return V.ENDHDR+o},loadFromBinary:function(n){if((n.length!==V.ENDHDR||n.readUInt32LE(0)!==V.ENDSIG)&&(n.length<V.ZIP64HDR||n.readUInt32LE(0)!==V.ZIP64SIG))throw we.Errors.INVALID_END();n.readUInt32LE(0)===V.ENDSIG?(s=n.readUInt16LE(V.ENDSUB),e=n.readUInt16LE(V.ENDTOT),t=n.readUInt32LE(V.ENDSIZ),r=n.readUInt32LE(V.ENDOFF),o=n.readUInt16LE(V.ENDCOM)):(s=we.readBigUInt64LE(n,V.ZIP64SUB),e=we.readBigUInt64LE(n,V.ZIP64TOT),t=we.readBigUInt64LE(n,V.ZIP64SIZE),r=we.readBigUInt64LE(n,V.ZIP64OFF),o=0)},toBinary:function(){var n=Buffer.alloc(V.ENDHDR+o);return n.writeUInt32LE(V.ENDSIG,0),n.writeUInt32LE(0,4),n.writeUInt16LE(s,V.ENDSUB),n.writeUInt16LE(e,V.ENDTOT),n.writeUInt32LE(t,V.ENDSIZ),n.writeUInt32LE(r,V.ENDOFF),n.writeUInt16LE(o,V.ENDCOM),n.fill(" ",V.ENDHDR),n},toJSON:function(){const n=function(i,a){let l=i.toString(16).toUpperCase();for(;l.length<a;)l="0"+l;return"0x"+l};return{diskEntries:s,totalEntries:e,size:t+" bytes",offset:n(r,4),commentLength:o}},toString:function(){return JSON.stringify(this.toJSON(),null,"	")}}};Fe.EntryHeader=Qi;Fe.MainHeader=eo;var Be={},to=function(s){var e=In,t={chunkSize:(parseInt(s.length/1024)+1)*1024};return{deflate:function(){return e.deflateRawSync(s,t)},deflateAsync:function(r){var o=e.createDeflateRaw(t),n=[],i=0;o.on("data",function(a){n.push(a),i+=a.length}),o.on("end",function(){var a=Buffer.alloc(i),l=0;a.fill(0);for(var f=0;f<n.length;f++){var m=n[f];m.copy(a,l),l+=m.length}r&&r(a)}),o.end(s)}}};const ro=+(process.versions?process.versions.node:"").split(".")[0]||0;var no=function(s,e){var t=In;const r=ro>=15&&e>0?{maxOutputLength:e}:{};return{inflate:function(){return t.inflateRawSync(s,r)},inflateAsync:function(o){var n=t.createInflateRaw(r),i=[],a=0;n.on("data",function(l){i.push(l),a+=l.length}),n.on("end",function(){var l=Buffer.alloc(a),f=0;l.fill(0);for(var m=0;m<i.length;m++){var y=i[m];y.copy(l,f),f+=y.length}o&&o(l)}),n.end(s)}}};const{randomFillSync:Mr}=vn,io=Re,oo=new Uint32Array(256).map((s,e)=>{for(let t=0;t<8;t++)e&1?e=e>>>1^3988292384:e>>>=1;return e>>>0}),Rn=(s,e)=>Math.imul(s,e)>>>0,Ar=(s,e)=>oo[(s^e)&255]^s>>>8,De=()=>typeof Mr=="function"?Mr(Buffer.alloc(12)):De.node();De.node=()=>{const s=Buffer.alloc(12),e=s.length;for(let t=0;t<e;t++)s[t]=Math.random()*256&255;return s};const Me={genSalt:De};function Ue(s){const e=Buffer.isBuffer(s)?s:Buffer.from(s);this.keys=new Uint32Array([305419896,591751049,878082192]);for(let t=0;t<e.length;t++)this.updateKeys(e[t])}Ue.prototype.updateKeys=function(s){const e=this.keys;return e[0]=Ar(e[0],s),e[1]+=e[0]&255,e[1]=Rn(e[1],134775813)+1,e[2]=Ar(e[2],e[1]>>>24),s};Ue.prototype.next=function(){const s=(this.keys[2]|2)>>>0;return Rn(s,s^1)>>8&255};function so(s){const e=new Ue(s);return function(t){const r=Buffer.alloc(t.length);let o=0;for(let n of t)r[o++]=e.updateKeys(n^e.next());return r}}function ao(s){const e=new Ue(s);return function(t,r,o=0){r||(r=Buffer.alloc(t.length));for(let n of t){const i=e.next();r[o++]=n^i,e.updateKeys(n)}return r}}function co(s,e,t){if(!s||!Buffer.isBuffer(s)||s.length<12)return Buffer.alloc(0);const r=so(t),o=r(s.slice(0,12)),n=(e.flags&8)===8?e.timeHighByte:e.crc>>>24;if(o[11]!==n)throw io.WRONG_PASSWORD();return r(s.slice(12))}function lo(s){Buffer.isBuffer(s)&&s.length>=12?Me.genSalt=function(){return s.slice(0,12)}:s==="node"?Me.genSalt=De.node:Me.genSalt=De}function uo(s,e,t,r=!1){s==null&&(s=Buffer.alloc(0)),Buffer.isBuffer(s)||(s=Buffer.from(s.toString()));const o=ao(t),n=Me.genSalt();n[11]=e.crc>>>24&255,r&&(n[10]=e.crc>>>16&255);const i=Buffer.alloc(s.length+12);return o(n,i),o(s,i,12)}var fo={decrypt:co,encrypt:uo,_salter:lo};Be.Deflater=to;Be.Inflater=no;Be.ZipCrypto=fo;var H=Ne,ho=Fe,G=H.Constants,nt=Be,Fn=function(s,e){var t=new ho.EntryHeader,r=Buffer.alloc(0),o=Buffer.alloc(0),n=!1,i=null,a=Buffer.alloc(0),l=Buffer.alloc(0),f=!0;const m=s,y=typeof m.decoder=="object"?m.decoder:H.decoder;f=y.hasOwnProperty("efs")?y.efs:!1;function w(){return!e||!(e instanceof Uint8Array)?Buffer.alloc(0):(l=t.loadLocalHeaderFromBinary(e),e.slice(t.realDataOffset,t.realDataOffset+t.compressedSize))}function h(c){if(t.flags_desc){const p={},v=t.realDataOffset+t.compressedSize;if(e.readUInt32LE(v)==G.LOCSIG||e.readUInt32LE(v)==G.CENSIG)throw H.Errors.DESCRIPTOR_NOT_EXIST();if(e.readUInt32LE(v)==G.EXTSIG)p.crc=e.readUInt32LE(v+G.EXTCRC),p.compressedSize=e.readUInt32LE(v+G.EXTSIZ),p.size=e.readUInt32LE(v+G.EXTLEN);else if(e.readUInt16LE(v+12)===19280)p.crc=e.readUInt32LE(v+G.EXTCRC-4),p.compressedSize=e.readUInt32LE(v+G.EXTSIZ-4),p.size=e.readUInt32LE(v+G.EXTLEN-4);else throw H.Errors.DESCRIPTOR_UNKNOWN();if(p.compressedSize!==t.compressedSize||p.size!==t.size||p.crc!==t.crc)throw H.Errors.DESCRIPTOR_FAULTY();if(H.crc32(c)!==p.crc)return!1}else if(H.crc32(c)!==t.localHeader.crc)return!1;return!0}function E(c,p,v){if(typeof p>"u"&&typeof c=="string"&&(v=c,c=void 0),n)return c&&p&&p(Buffer.alloc(0),H.Errors.DIRECTORY_CONTENT_ERROR()),Buffer.alloc(0);var I=w();if(I.length===0)return c&&p&&p(I),I;if(t.encrypted){if(typeof v!="string"&&!Buffer.isBuffer(v))throw H.Errors.INVALID_PASS_PARAM();I=nt.ZipCrypto.decrypt(I,t,v)}var N=Buffer.alloc(t.size);switch(t.method){case H.Constants.STORED:if(I.copy(N),h(N))return c&&p&&p(N),N;throw c&&p&&p(N,H.Errors.BAD_CRC()),H.Errors.BAD_CRC();case H.Constants.DEFLATED:var S=new nt.Inflater(I,t.size);if(c)S.inflateAsync(function(d){d.copy(d,0),p&&(h(d)?p(d):p(d,H.Errors.BAD_CRC()))});else{if(S.inflate(N).copy(N,0),!h(N))throw H.Errors.BAD_CRC(`"${y.decode(r)}"`);return N}break;default:throw c&&p&&p(Buffer.alloc(0),H.Errors.UNKNOWN_METHOD()),H.Errors.UNKNOWN_METHOD()}}function g(c,p){if((!i||!i.length)&&Buffer.isBuffer(e))return c&&p&&p(w()),w();if(i.length&&!n){var v;switch(t.method){case H.Constants.STORED:return t.compressedSize=t.size,v=Buffer.alloc(i.length),i.copy(v),c&&p&&p(v),v;default:case H.Constants.DEFLATED:var I=new nt.Deflater(i);if(c)I.deflateAsync(function(S){v=Buffer.alloc(S.length),t.compressedSize=S.length,S.copy(v),p&&p(v)});else{var N=I.deflate();return t.compressedSize=N.length,N}I=null;break}}else if(c&&p)p(Buffer.alloc(0));else return Buffer.alloc(0)}function _(c,p){return(c.readUInt32LE(p+4)<<4)+c.readUInt32LE(p)}function b(c){try{for(var p=0,v,I,N;p+4<c.length;)v=c.readUInt16LE(p),p+=2,I=c.readUInt16LE(p),p+=2,N=c.slice(p,p+I),p+=I,G.ID_ZIP64===v&&u(N)}catch{throw H.Errors.EXTRA_FIELD_PARSE_ERROR()}}function u(c){var p,v,I,N;c.length>=G.EF_ZIP64_SCOMP&&(p=_(c,G.EF_ZIP64_SUNCOMP),t.size===G.EF_ZIP64_OR_32&&(t.size=p)),c.length>=G.EF_ZIP64_RHO&&(v=_(c,G.EF_ZIP64_SCOMP),t.compressedSize===G.EF_ZIP64_OR_32&&(t.compressedSize=v)),c.length>=G.EF_ZIP64_DSN&&(I=_(c,G.EF_ZIP64_RHO),t.offset===G.EF_ZIP64_OR_32&&(t.offset=I)),c.length>=G.EF_ZIP64_DSN+4&&(N=c.readUInt32LE(G.EF_ZIP64_DSN),t.diskNumStart===G.EF_ZIP64_OR_16&&(t.diskNumStart=N))}return{get entryName(){return y.decode(r)},get rawEntryName(){return r},set entryName(c){r=H.toBuffer(c,y.encode);var p=r[r.length-1];n=p===47||p===92,t.fileNameLength=r.length},get efs(){return typeof f=="function"?f(this.entryName):f},get extra(){return a},set extra(c){a=c,t.extraLength=c.length,b(c)},get comment(){return y.decode(o)},set comment(c){if(o=H.toBuffer(c,y.encode),t.commentLength=o.length,o.length>65535)throw H.Errors.COMMENT_TOO_LONG()},get name(){var c=y.decode(r);return n?c.substr(c.length-1).split("/").pop():c.split("/").pop()},get isDirectory(){return n},getCompressedData:function(){return g(!1,null)},getCompressedDataAsync:function(c){g(!0,c)},setData:function(c){i=H.toBuffer(c,H.decoder.encode),!n&&i.length?(t.size=i.length,t.method=H.Constants.DEFLATED,t.crc=H.crc32(c),t.changed=!0):t.method=H.Constants.STORED},getData:function(c){return t.changed?i:E(!1,null,c)},getDataAsync:function(c,p){t.changed?c(i):E(!0,c,p)},set attr(c){t.attr=c},get attr(){return t.attr},set header(c){t.loadFromBinary(c)},get header(){return t},packCentralHeader:function(){t.flags_efs=this.efs,t.extraLength=a.length;var c=t.centralHeaderToBinary(),p=H.Constants.CENHDR;return r.copy(c,p),p+=r.length,a.copy(c,p),p+=t.extraLength,o.copy(c,p),c},packLocalHeader:function(){let c=0;t.flags_efs=this.efs,t.extraLocalLength=l.length;const p=t.localHeaderToBinary(),v=Buffer.alloc(p.length+r.length+t.extraLocalLength);return p.copy(v,c),c+=p.length,r.copy(v,c),c+=r.length,l.copy(v,c),c+=l.length,v},toJSON:function(){const c=function(p){return"<"+(p&&p.length+" bytes buffer"||"null")+">"};return{entryName:this.entryName,name:this.name,comment:this.comment,isDirectory:this.isDirectory,header:t.toJSON(),compressedData:c(e),data:c(i)}},toString:function(){return JSON.stringify(this.toJSON(),null,"	")}}};const Rr=Fn,po=Fe,re=Ne;var go=function(s,e){var t=[],r={},o=Buffer.alloc(0),n=new po.MainHeader,i=!1;const a=new Set,l=e,{noSort:f,decoder:m}=l;s?h(l.readEntries):i=!0;function y(){const g=new Set;for(const _ of Object.keys(r)){const b=_.split("/");if(b.pop(),!!b.length)for(let u=0;u<b.length;u++){const c=b.slice(0,u+1).join("/")+"/";g.add(c)}}for(const _ of g)if(!(_ in r)){const b=new Rr(l);b.entryName=_,b.attr=16,b.temporary=!0,t.push(b),r[b.entryName]=b,a.add(b)}}function w(){if(i=!0,r={},n.diskEntries>(s.length-n.offset)/re.Constants.CENHDR)throw re.Errors.DISK_ENTRY_TOO_LARGE();t=new Array(n.diskEntries);for(var g=n.offset,_=0;_<t.length;_++){var b=g,u=new Rr(l,s);u.header=s.slice(b,b+=re.Constants.CENHDR),u.entryName=s.slice(b,b+=u.header.fileNameLength),u.header.extraLength&&(u.extra=s.slice(b,b+=u.header.extraLength)),u.header.commentLength&&(u.comment=s.slice(b,b+u.header.commentLength)),g+=u.header.centralHeaderSize,t[_]=u,r[u.entryName]=u}a.clear(),y()}function h(g){var _=s.length-re.Constants.ENDHDR,b=Math.max(0,_-65535),u=b,c=s.length,p=-1,v=0;for((typeof l.trailingSpace=="boolean"?l.trailingSpace:!1)&&(b=0),_;_>=u;_--)if(s[_]===80){if(s.readUInt32LE(_)===re.Constants.ENDSIG){p=_,v=_,c=_+re.Constants.ENDHDR,u=_-re.Constants.END64HDR;continue}if(s.readUInt32LE(_)===re.Constants.END64SIG){u=b;continue}if(s.readUInt32LE(_)===re.Constants.ZIP64SIG){p=_,c=_+re.readBigUInt64LE(s,_+re.Constants.ZIP64SIZE)+re.Constants.ZIP64LEAD;break}}if(p==-1)throw re.Errors.INVALID_FORMAT();n.loadFromBinary(s.slice(p,c)),n.commentLength&&(o=s.slice(v+re.Constants.ENDHDR)),g&&w()}function E(){t.length>1&&!f&&t.sort((g,_)=>g.entryName.toLowerCase().localeCompare(_.entryName.toLowerCase()))}return{get entries(){return i||w(),t.filter(g=>!a.has(g))},get comment(){return m.decode(o)},set comment(g){o=re.toBuffer(g,m.encode),n.commentLength=o.length},getEntryCount:function(){return i?t.length:n.diskEntries},forEach:function(g){this.entries.forEach(g)},getEntry:function(g){return i||w(),r[g]||null},setEntry:function(g){i||w(),t.push(g),r[g.entryName]=g,n.totalEntries=t.length},deleteFile:function(g,_=!0){i||w();const b=r[g];this.getEntryChildren(b,_).map(c=>c.entryName).forEach(this.deleteEntry)},deleteEntry:function(g){i||w();const _=r[g],b=t.indexOf(_);b>=0&&(t.splice(b,1),delete r[g],n.totalEntries=t.length)},getEntryChildren:function(g,_=!0){if(i||w(),typeof g=="object")if(g.isDirectory&&_){const b=[],u=g.entryName;for(const c of t)c.entryName.startsWith(u)&&b.push(c);return b}else return[g];return[]},getChildCount:function(g){if(g&&g.isDirectory){const _=this.getEntryChildren(g);return _.includes(g)?_.length-1:_.length}return 0},compressToBuffer:function(){i||w(),E();const g=[],_=[];let b=0,u=0;n.size=0,n.offset=0;let c=0;for(const I of this.entries){const N=I.getCompressedData();I.header.offset=u;const S=I.packLocalHeader(),d=S.length+N.length;u+=d,g.push(S),g.push(N);const D=I.packCentralHeader();_.push(D),n.size+=D.length,b+=d+D.length,c++}b+=n.mainHeaderSize,n.offset=u,n.totalEntries=c,u=0;const p=Buffer.alloc(b);for(const I of g)I.copy(p,u),u+=I.length;for(const I of _)I.copy(p,u),u+=I.length;const v=n.toBinary();return o&&o.copy(v,re.Constants.ENDHDR),v.copy(p,u),s=p,i=!1,p},toAsyncBuffer:function(g,_,b,u){try{i||w(),E();const c=[],p=[];let v=0,I=0,N=0;n.size=0,n.offset=0;const S=function(d){if(d.length>0){const D=d.shift(),L=D.entryName+D.extra.toString();b&&b(L),D.getCompressedDataAsync(function(M){u&&u(L),D.header.offset=I;const F=D.packLocalHeader(),k=F.length+M.length;I+=k,c.push(F),c.push(M);const q=D.packCentralHeader();p.push(q),n.size+=q.length,v+=k+q.length,N++,S(d)})}else{v+=n.mainHeaderSize,n.offset=I,n.totalEntries=N,I=0;const D=Buffer.alloc(v);c.forEach(function(M){M.copy(D,I),I+=M.length}),p.forEach(function(M){M.copy(D,I),I+=M.length});const L=n.toBinary();o&&o.copy(L,re.Constants.ENDHDR),L.copy(D,I),s=D,i=!1,g(D)}};S(Array.from(this.entries))}catch(c){_(c)}}}};const W=Ne,Z=B,mo=Fn,yo=go,ye=(...s)=>W.findLast(s,e=>typeof e=="boolean"),Fr=(...s)=>W.findLast(s,e=>typeof e=="string"),Eo=(...s)=>W.findLast(s,e=>typeof e=="function"),bo={noSort:!1,readEntries:!1,method:W.Constants.NONE,fs:null};var wo=function(s,e){let t=null;const r=Object.assign(Object.create(null),bo);s&&typeof s=="object"&&(s instanceof Uint8Array||(Object.assign(r,s),s=r.input?r.input:void 0,r.input&&delete r.input),Buffer.isBuffer(s)&&(t=s,r.method=W.Constants.BUFFER,s=void 0)),Object.assign(r,e);const o=new W(r);if((typeof r.decoder!="object"||typeof r.decoder.encode!="function"||typeof r.decoder.decode!="function")&&(r.decoder=W.decoder),s&&typeof s=="string")if(o.fs.existsSync(s))r.method=W.Constants.FILE,r.filename=s,t=o.fs.readFileSync(s);else throw W.Errors.INVALID_FILENAME();const n=new yo(t,r),{canonical:i,sanitize:a,zipnamefix:l}=W;function f(h){if(h&&n){var E;if(typeof h=="string"&&(E=n.getEntry(Z.posix.normalize(h))),typeof h=="object"&&typeof h.entryName<"u"&&typeof h.header<"u"&&(E=n.getEntry(h.entryName)),E)return E}return null}function m(h){const{join:E,normalize:g,sep:_}=Z.posix;return E(".",g(_+h.split("\\").join(_)+_))}function y(h){return h instanceof RegExp?function(E){return function(g){return E.test(g)}}(h):typeof h!="function"?()=>!0:h}const w=(h,E)=>{let g=E.slice(-1);return g=g===o.sep?o.sep:"",Z.relative(h,E)+g};return{readFile:function(h,E){var g=f(h);return g&&g.getData(E)||null},childCount:function(h){const E=f(h);if(E)return n.getChildCount(E)},readFileAsync:function(h,E){var g=f(h);g?g.getDataAsync(E):E(null,"getEntry failed for:"+h)},readAsText:function(h,E){var g=f(h);if(g){var _=g.getData();if(_&&_.length)return _.toString(E||"utf8")}return""},readAsTextAsync:function(h,E,g){var _=f(h);_?_.getDataAsync(function(b,u){if(u){E(b,u);return}b&&b.length?E(b.toString(g||"utf8")):E("")}):E("")},deleteFile:function(h,E=!0){var g=f(h);g&&n.deleteFile(g.entryName,E)},deleteEntry:function(h){var E=f(h);E&&n.deleteEntry(E.entryName)},addZipComment:function(h){n.comment=h},getZipComment:function(){return n.comment||""},addZipEntryComment:function(h,E){var g=f(h);g&&(g.comment=E)},getZipEntryComment:function(h){var E=f(h);return E&&E.comment||""},updateFile:function(h,E){var g=f(h);g&&g.setData(E)},addLocalFile:function(h,E,g,_){if(o.fs.existsSync(h)){E=E?m(E):"";const b=Z.win32.basename(Z.win32.normalize(h));E+=g||b;const u=o.fs.statSync(h),c=u.isFile()?o.fs.readFileSync(h):Buffer.alloc(0);u.isDirectory()&&(E+=o.sep),this.addFile(E,c,_,u)}else throw W.Errors.FILE_NOT_FOUND(h)},addLocalFileAsync:function(h,E){h=typeof h=="object"?h:{localPath:h};const g=Z.resolve(h.localPath),{comment:_}=h;let{zipPath:b,zipName:u}=h;const c=this;o.fs.stat(g,function(p,v){if(p)return E(p,!1);b=b?m(b):"";const I=Z.win32.basename(Z.win32.normalize(g));if(b+=u||I,v.isFile())o.fs.readFile(g,function(N,S){return N?E(N,!1):(c.addFile(b,S,_,v),setImmediate(E,void 0,!0))});else if(v.isDirectory())return b+=o.sep,c.addFile(b,Buffer.alloc(0),_,v),setImmediate(E,void 0,!0)})},addLocalFolder:function(h,E,g){if(g=y(g),E=E?m(E):"",h=Z.normalize(h),o.fs.existsSync(h)){const _=o.findFiles(h),b=this;if(_.length)for(const u of _){const c=Z.join(E,w(h,u));g(c)&&b.addLocalFile(u,Z.dirname(c))}}else throw W.Errors.FILE_NOT_FOUND(h)},addLocalFolderAsync:function(h,E,g,_){_=y(_),g=g?m(g):"",h=Z.normalize(h);var b=this;o.fs.open(h,"r",function(u){if(u&&u.code==="ENOENT")E(void 0,W.Errors.FILE_NOT_FOUND(h));else if(u)E(void 0,u);else{var c=o.findFiles(h),p=-1,v=function(){if(p+=1,p<c.length){var I=c[p],N=w(h,I).split("\\").join("/");N=N.normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^\x20-\x7E]/g,""),_(N)?o.fs.stat(I,function(S,d){S&&E(void 0,S),d.isFile()?o.fs.readFile(I,function(D,L){D?E(void 0,D):(b.addFile(g+N,L,"",d),v())}):(b.addFile(g+N+"/",Buffer.alloc(0),"",d),v())}):process.nextTick(()=>{v()})}else E(!0,void 0)};v()}})},addLocalFolderAsync2:function(h,E){const g=this;h=typeof h=="object"?h:{localPath:h},localPath=Z.resolve(m(h.localPath));let{zipPath:_,filter:b,namefix:u}=h;b instanceof RegExp?b=function(v){return function(I){return v.test(I)}}(b):typeof b!="function"&&(b=function(){return!0}),_=_?m(_):"",u=="latin1"&&(u=v=>v.normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^\x20-\x7E]/g,"")),typeof u!="function"&&(u=v=>v);const c=v=>Z.join(_,u(w(localPath,v))),p=v=>Z.win32.basename(Z.win32.normalize(u(v)));o.fs.open(localPath,"r",function(v){v&&v.code==="ENOENT"?E(void 0,W.Errors.FILE_NOT_FOUND(localPath)):v?E(void 0,v):o.findFilesAsync(localPath,function(I,N){if(I)return E(I);N=N.filter(S=>b(c(S))),N.length||E(void 0,!1),setImmediate(N.reverse().reduce(function(S,d){return function(D,L){if(D||L===!1)return setImmediate(S,D,!1);g.addLocalFileAsync({localPath:d,zipPath:Z.dirname(c(d)),zipName:p(d)},S)}},E))})})},addLocalFolderPromise:function(h,E){return new Promise((g,_)=>{this.addLocalFolderAsync2(Object.assign({localPath:h},E),(b,u)=>{b&&_(b),u&&g(this)})})},addFile:function(h,E,g,_){h=l(h);let b=f(h);const u=b!=null;u||(b=new mo(r),b.entryName=h),b.comment=g||"";const c=typeof _=="object"&&_ instanceof o.fs.Stats;c&&(b.header.time=_.mtime);var p=b.isDirectory?16:0;let v=b.isDirectory?16384:32768;return c?v|=4095&_.mode:typeof _=="number"?v|=4095&_:v|=b.isDirectory?493:420,p=(p|v<<16)>>>0,b.attr=p,b.setData(E),u||n.setEntry(b),b},getEntries:function(h){return n.password=h,n?n.entries:[]},getEntry:function(h){return f(h)},getEntryCount:function(){return n.getEntryCount()},forEach:function(h){return n.forEach(h)},extractEntryTo:function(h,E,g,_,b,u){_=ye(!1,_),b=ye(!1,b),g=ye(!0,g),u=Fr(b,u);var c=f(h);if(!c)throw W.Errors.NO_ENTRY();var p=i(c.entryName),v=a(E,u&&!c.isDirectory?u:g?p:Z.basename(p));if(c.isDirectory){var I=n.getEntryChildren(c);return I.forEach(function(d){if(d.isDirectory)return;var D=d.getData();if(!D)throw W.Errors.CANT_EXTRACT_FILE();var L=i(d.entryName),M=a(E,g?L:Z.basename(L));const F=b?d.header.fileAttr:void 0;o.writeFileTo(M,D,_,F)}),!0}var N=c.getData(n.password);if(!N)throw W.Errors.CANT_EXTRACT_FILE();if(o.fs.existsSync(v)&&!_)throw W.Errors.CANT_OVERRIDE();const S=b?h.header.fileAttr:void 0;return o.writeFileTo(v,N,_,S),!0},test:function(h){if(!n)return!1;for(var E in n.entries)try{if(E.isDirectory)continue;var g=n.entries[E].getData(h);if(!g)return!1}catch{return!1}return!0},extractAllTo:function(h,E,g,_){if(g=ye(!1,g),_=Fr(g,_),E=ye(!1,E),!n)throw W.Errors.NO_ZIP();n.entries.forEach(function(b){var u=a(h,i(b.entryName));if(b.isDirectory){o.makeDir(u);return}var c=b.getData(_);if(!c)throw W.Errors.CANT_EXTRACT_FILE();const p=g?b.header.fileAttr:void 0;o.writeFileTo(u,c,E,p);try{o.fs.utimesSync(u,b.header.time,b.header.time)}catch{throw W.Errors.CANT_EXTRACT_FILE()}})},extractAllToAsync:function(h,E,g,_){if(_=Eo(E,g,_),g=ye(!1,g),E=ye(!1,E),!_)return new Promise((v,I)=>{this.extractAllToAsync(h,E,g,function(N){N?I(N):v(this)})});if(!n){_(W.Errors.NO_ZIP());return}h=Z.resolve(h);const b=v=>a(h,Z.normalize(i(v.entryName))),u=(v,I)=>new Error(v+': "'+I+'"'),c=[],p=[];n.entries.forEach(v=>{v.isDirectory?c.push(v):p.push(v)});for(const v of c){const I=b(v),N=g?v.header.fileAttr:void 0;try{o.makeDir(I),N&&o.fs.chmodSync(I,N),o.fs.utimesSync(I,v.header.time,v.header.time)}catch{_(u("Unable to create folder",I))}}p.reverse().reduce(function(v,I){return function(N){if(N)v(N);else{const S=Z.normalize(i(I.entryName)),d=a(h,S);I.getDataAsync(function(D,L){if(L)v(L);else if(!D)v(W.Errors.CANT_EXTRACT_FILE());else{const M=g?I.header.fileAttr:void 0;o.writeFileToAsync(d,D,E,M,function(F){F||v(u("Unable to write file",d)),o.fs.utimes(d,I.header.time,I.header.time,function(k){k?v(u("Unable to set times",d)):v()})})}})}}},_)()},writeZip:function(h,E){if(arguments.length===1&&typeof h=="function"&&(E=h,h=""),!h&&r.filename&&(h=r.filename),!!h){var g=n.compressToBuffer();if(g){var _=o.writeFileTo(h,g,!0);typeof E=="function"&&E(_?null:new Error("failed"),"")}}},writeZipPromise:function(h,E){const{overwrite:g,perm:_}=Object.assign({overwrite:!0},E);return new Promise((b,u)=>{!h&&r.filename&&(h=r.filename),h||u("ADM-ZIP: ZIP File Name Missing"),this.toBufferPromise().then(c=>{const p=v=>v?b(v):u("ADM-ZIP: Wasn't able to write zip file");o.writeFileToAsync(h,c,g,_,p)},u)})},toBufferPromise:function(){return new Promise((h,E)=>{n.toAsyncBuffer(h,E)})},toBuffer:function(h,E,g,_){return typeof h=="function"?(n.toAsyncBuffer(h,E,g,_),null):n.compressToBuffer()}}};const To=li(wo);var it={},Br;function Wt(){return Br||(Br=1,(function(){it.defaults={"0.1":{explicitCharkey:!1,trim:!0,normalize:!0,normalizeTags:!1,attrkey:"@",charkey:"#",explicitArray:!1,ignoreAttrs:!1,mergeAttrs:!1,explicitRoot:!1,validator:null,xmlns:!1,explicitChildren:!1,childkey:"@@",charsAsChildren:!1,includeWhiteChars:!1,async:!1,strict:!0,attrNameProcessors:null,attrValueProcessors:null,tagNameProcessors:null,valueProcessors:null,emptyTag:""},"0.2":{explicitCharkey:!1,trim:!1,normalize:!1,normalizeTags:!1,attrkey:"$",charkey:"_",explicitArray:!0,ignoreAttrs:!1,mergeAttrs:!1,explicitRoot:!0,validator:null,xmlns:!1,explicitChildren:!1,preserveChildrenOrder:!1,childkey:"$$",charsAsChildren:!1,includeWhiteChars:!1,async:!1,strict:!0,attrNameProcessors:null,attrValueProcessors:null,tagNameProcessors:null,valueProcessors:null,rootName:"root",xmldec:{version:"1.0",encoding:"UTF-8",standalone:!0},doctype:null,renderOpts:{pretty:!0,indent:"  ",newline:`
`},headless:!1,chunkSize:1e4,emptyTag:"",cdata:!1}}}).call(U)),it}var ot={},fe={},he={},Ur;function de(){return Ur||(Ur=1,(function(){var s,e,t,r,o,n,i,a=[].slice,l={}.hasOwnProperty;s=function(){var f,m,y,w,h,E;if(E=arguments[0],h=2<=arguments.length?a.call(arguments,1):[],o(Object.assign))Object.assign.apply(null,arguments);else for(f=0,y=h.length;f<y;f++)if(w=h[f],w!=null)for(m in w)l.call(w,m)&&(E[m]=w[m]);return E},o=function(f){return!!f&&Object.prototype.toString.call(f)==="[object Function]"},n=function(f){var m;return!!f&&((m=typeof f)=="function"||m==="object")},t=function(f){return o(Array.isArray)?Array.isArray(f):Object.prototype.toString.call(f)==="[object Array]"},r=function(f){var m;if(t(f))return!f.length;for(m in f)if(l.call(f,m))return!1;return!0},i=function(f){var m,y;return n(f)&&(y=Object.getPrototypeOf(f))&&(m=y.constructor)&&typeof m=="function"&&m instanceof m&&Function.prototype.toString.call(m)===Function.prototype.toString.call(Object)},e=function(f){return o(f.valueOf)?f.valueOf():f},he.assign=s,he.isFunction=o,he.isObject=n,he.isArray=t,he.isEmpty=r,he.isPlainObject=i,he.getValue=e}).call(U)),he}var st={exports:{}},kr;function Bn(){return kr||(kr=1,(function(){st.exports=function(){function s(){}return s.prototype.hasFeature=function(e,t){return!0},s.prototype.createDocumentType=function(e,t,r){throw new Error("This DOM method is not implemented.")},s.prototype.createDocument=function(e,t,r){throw new Error("This DOM method is not implemented.")},s.prototype.createHTMLDocument=function(e){throw new Error("This DOM method is not implemented.")},s.prototype.getFeature=function(e,t){throw new Error("This DOM method is not implemented.")},s}()}).call(U)),st.exports}var at={exports:{}},ct={exports:{}},lt={exports:{}},Xr;function _o(){return Xr||(Xr=1,(function(){lt.exports=function(){function s(){}return s.prototype.handleError=function(e){throw new Error(e)},s}()}).call(U)),lt.exports}var ut={exports:{}},$r;function vo(){return $r||($r=1,(function(){ut.exports=function(){function s(e){this.arr=e||[]}return Object.defineProperty(s.prototype,"length",{get:function(){return this.arr.length}}),s.prototype.item=function(e){return this.arr[e]||null},s.prototype.contains=function(e){return this.arr.indexOf(e)!==-1},s}()}).call(U)),ut.exports}var jr;function Co(){return jr||(jr=1,(function(){var s,e;s=_o(),e=vo(),ct.exports=function(){function t(){this.defaultParams={"canonical-form":!1,"cdata-sections":!1,comments:!1,"datatype-normalization":!1,"element-content-whitespace":!0,entities:!0,"error-handler":new s,infoset:!0,"validate-if-schema":!1,namespaces:!0,"namespace-declarations":!0,"normalize-characters":!1,"schema-location":"","schema-type":"","split-cdata-sections":!0,validate:!1,"well-formed":!0},this.params=Object.create(this.defaultParams)}return Object.defineProperty(t.prototype,"parameterNames",{get:function(){return new e(Object.keys(this.defaultParams))}}),t.prototype.getParameter=function(r){return this.params.hasOwnProperty(r)?this.params[r]:null},t.prototype.canSetParameter=function(r,o){return!0},t.prototype.setParameter=function(r,o){return o!=null?this.params[r]=o:delete this.params[r]},t}()}).call(U)),ct.exports}var ft={exports:{}},ht={exports:{}},dt={exports:{}},zr;function K(){return zr||(zr=1,(function(){dt.exports={Element:1,Attribute:2,Text:3,CData:4,EntityReference:5,EntityDeclaration:6,ProcessingInstruction:7,Comment:8,Document:9,DocType:10,DocumentFragment:11,NotationDeclaration:12,Declaration:201,Raw:202,AttributeDeclaration:203,ElementDeclaration:204,Dummy:205}}).call(U)),dt.exports}var pt={exports:{}},Hr;function Un(){return Hr||(Hr=1,(function(){var s;s=K(),ce(),pt.exports=function(){function e(t,r,o){if(this.parent=t,this.parent&&(this.options=this.parent.options,this.stringify=this.parent.stringify),r==null)throw new Error("Missing attribute name. "+this.debugInfo(r));this.name=this.stringify.name(r),this.value=this.stringify.attValue(o),this.type=s.Attribute,this.isId=!1,this.schemaTypeInfo=null}return Object.defineProperty(e.prototype,"nodeType",{get:function(){return this.type}}),Object.defineProperty(e.prototype,"ownerElement",{get:function(){return this.parent}}),Object.defineProperty(e.prototype,"textContent",{get:function(){return this.value},set:function(t){return this.value=t||""}}),Object.defineProperty(e.prototype,"namespaceURI",{get:function(){return""}}),Object.defineProperty(e.prototype,"prefix",{get:function(){return""}}),Object.defineProperty(e.prototype,"localName",{get:function(){return this.name}}),Object.defineProperty(e.prototype,"specified",{get:function(){return!0}}),e.prototype.clone=function(){return Object.create(this)},e.prototype.toString=function(t){return this.options.writer.attribute(this,this.options.writer.filterOptions(t))},e.prototype.debugInfo=function(t){return t=t||this.name,t==null?"parent: <"+this.parent.name+">":"attribute: {"+t+"}, parent: <"+this.parent.name+">"},e.prototype.isEqualNode=function(t){return!(t.namespaceURI!==this.namespaceURI||t.prefix!==this.prefix||t.localName!==this.localName||t.value!==this.value)},e}()}).call(U)),pt.exports}var gt={exports:{}},qr;function Gt(){return qr||(qr=1,(function(){gt.exports=function(){function s(e){this.nodes=e}return Object.defineProperty(s.prototype,"length",{get:function(){return Object.keys(this.nodes).length||0}}),s.prototype.clone=function(){return this.nodes=null},s.prototype.getNamedItem=function(e){return this.nodes[e]},s.prototype.setNamedItem=function(e){var t;return t=this.nodes[e.nodeName],this.nodes[e.nodeName]=e,t||null},s.prototype.removeNamedItem=function(e){var t;return t=this.nodes[e],delete this.nodes[e],t||null},s.prototype.item=function(e){return this.nodes[Object.keys(this.nodes)[e]]||null},s.prototype.getNamedItemNS=function(e,t){throw new Error("This DOM method is not implemented.")},s.prototype.setNamedItemNS=function(e){throw new Error("This DOM method is not implemented.")},s.prototype.removeNamedItemNS=function(e,t){throw new Error("This DOM method is not implemented.")},s}()}).call(U)),gt.exports}var Vr;function Zt(){return Vr||(Vr=1,(function(){var s,e,t,r,o,n,i,a,l=function(m,y){for(var w in y)f.call(y,w)&&(m[w]=y[w]);function h(){this.constructor=m}return h.prototype=y.prototype,m.prototype=new h,m.__super__=y.prototype,m},f={}.hasOwnProperty;a=de(),i=a.isObject,n=a.isFunction,o=a.getValue,r=ce(),s=K(),e=Un(),t=Gt(),ht.exports=function(m){l(y,m);function y(w,h,E){var g,_,b,u;if(y.__super__.constructor.call(this,w),h==null)throw new Error("Missing element name. "+this.debugInfo());if(this.name=this.stringify.name(h),this.type=s.Element,this.attribs={},this.schemaTypeInfo=null,E!=null&&this.attribute(E),w.type===s.Document&&(this.isRoot=!0,this.documentObject=w,w.rootObject=this,w.children)){for(u=w.children,_=0,b=u.length;_<b;_++)if(g=u[_],g.type===s.DocType){g.name=this.name;break}}}return Object.defineProperty(y.prototype,"tagName",{get:function(){return this.name}}),Object.defineProperty(y.prototype,"namespaceURI",{get:function(){return""}}),Object.defineProperty(y.prototype,"prefix",{get:function(){return""}}),Object.defineProperty(y.prototype,"localName",{get:function(){return this.name}}),Object.defineProperty(y.prototype,"id",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(y.prototype,"className",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(y.prototype,"classList",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(y.prototype,"attributes",{get:function(){return(!this.attributeMap||!this.attributeMap.nodes)&&(this.attributeMap=new t(this.attribs)),this.attributeMap}}),y.prototype.clone=function(){var w,h,E,g;E=Object.create(this),E.isRoot&&(E.documentObject=null),E.attribs={},g=this.attribs;for(h in g)f.call(g,h)&&(w=g[h],E.attribs[h]=w.clone());return E.children=[],this.children.forEach(function(_){var b;return b=_.clone(),b.parent=E,E.children.push(b)}),E},y.prototype.attribute=function(w,h){var E,g;if(w!=null&&(w=o(w)),i(w))for(E in w)f.call(w,E)&&(g=w[E],this.attribute(E,g));else n(h)&&(h=h.apply()),this.options.keepNullAttributes&&h==null?this.attribs[w]=new e(this,w,""):h!=null&&(this.attribs[w]=new e(this,w,h));return this},y.prototype.removeAttribute=function(w){var h,E,g;if(w==null)throw new Error("Missing attribute name. "+this.debugInfo());if(w=o(w),Array.isArray(w))for(E=0,g=w.length;E<g;E++)h=w[E],delete this.attribs[h];else delete this.attribs[w];return this},y.prototype.toString=function(w){return this.options.writer.element(this,this.options.writer.filterOptions(w))},y.prototype.att=function(w,h){return this.attribute(w,h)},y.prototype.a=function(w,h){return this.attribute(w,h)},y.prototype.getAttribute=function(w){return this.attribs.hasOwnProperty(w)?this.attribs[w].value:null},y.prototype.setAttribute=function(w,h){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.getAttributeNode=function(w){return this.attribs.hasOwnProperty(w)?this.attribs[w]:null},y.prototype.setAttributeNode=function(w){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.removeAttributeNode=function(w){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.getElementsByTagName=function(w){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.getAttributeNS=function(w,h){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.setAttributeNS=function(w,h,E){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.removeAttributeNS=function(w,h){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.getAttributeNodeNS=function(w,h){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.setAttributeNodeNS=function(w){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.getElementsByTagNameNS=function(w,h){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.hasAttribute=function(w){return this.attribs.hasOwnProperty(w)},y.prototype.hasAttributeNS=function(w,h){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.setIdAttribute=function(w,h){return this.attribs.hasOwnProperty(w)?this.attribs[w].isId:h},y.prototype.setIdAttributeNS=function(w,h,E){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.setIdAttributeNode=function(w,h){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.getElementsByTagName=function(w){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.getElementsByTagNameNS=function(w,h){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.getElementsByClassName=function(w){throw new Error("This DOM method is not implemented."+this.debugInfo())},y.prototype.isEqualNode=function(w){var h,E,g;if(!y.__super__.isEqualNode.apply(this,arguments).isEqualNode(w)||w.namespaceURI!==this.namespaceURI||w.prefix!==this.prefix||w.localName!==this.localName||w.attribs.length!==this.attribs.length)return!1;for(h=E=0,g=this.attribs.length-1;0<=g?E<=g:E>=g;h=0<=g?++E:--E)if(!this.attribs[h].isEqualNode(w.attribs[h]))return!1;return!0},y}(r)}).call(U)),ht.exports}var mt={exports:{}},yt={exports:{}},Wr;function ke(){return Wr||(Wr=1,(function(){var s,e=function(r,o){for(var n in o)t.call(o,n)&&(r[n]=o[n]);function i(){this.constructor=r}return i.prototype=o.prototype,r.prototype=new i,r.__super__=o.prototype,r},t={}.hasOwnProperty;s=ce(),yt.exports=function(r){e(o,r);function o(n){o.__super__.constructor.call(this,n),this.value=""}return Object.defineProperty(o.prototype,"data",{get:function(){return this.value},set:function(n){return this.value=n||""}}),Object.defineProperty(o.prototype,"length",{get:function(){return this.value.length}}),Object.defineProperty(o.prototype,"textContent",{get:function(){return this.value},set:function(n){return this.value=n||""}}),o.prototype.clone=function(){return Object.create(this)},o.prototype.substringData=function(n,i){throw new Error("This DOM method is not implemented."+this.debugInfo())},o.prototype.appendData=function(n){throw new Error("This DOM method is not implemented."+this.debugInfo())},o.prototype.insertData=function(n,i){throw new Error("This DOM method is not implemented."+this.debugInfo())},o.prototype.deleteData=function(n,i){throw new Error("This DOM method is not implemented."+this.debugInfo())},o.prototype.replaceData=function(n,i,a){throw new Error("This DOM method is not implemented."+this.debugInfo())},o.prototype.isEqualNode=function(n){return!(!o.__super__.isEqualNode.apply(this,arguments).isEqualNode(n)||n.data!==this.data)},o}(s)}).call(U)),yt.exports}var Gr;function Yt(){return Gr||(Gr=1,(function(){var s,e,t=function(o,n){for(var i in n)r.call(n,i)&&(o[i]=n[i]);function a(){this.constructor=o}return a.prototype=n.prototype,o.prototype=new a,o.__super__=n.prototype,o},r={}.hasOwnProperty;s=K(),e=ke(),mt.exports=function(o){t(n,o);function n(i,a){if(n.__super__.constructor.call(this,i),a==null)throw new Error("Missing CDATA text. "+this.debugInfo());this.name="#cdata-section",this.type=s.CData,this.value=this.stringify.cdata(a)}return n.prototype.clone=function(){return Object.create(this)},n.prototype.toString=function(i){return this.options.writer.cdata(this,this.options.writer.filterOptions(i))},n}(e)}).call(U)),mt.exports}var Et={exports:{}},Zr;function Kt(){return Zr||(Zr=1,(function(){var s,e,t=function(o,n){for(var i in n)r.call(n,i)&&(o[i]=n[i]);function a(){this.constructor=o}return a.prototype=n.prototype,o.prototype=new a,o.__super__=n.prototype,o},r={}.hasOwnProperty;s=K(),e=ke(),Et.exports=function(o){t(n,o);function n(i,a){if(n.__super__.constructor.call(this,i),a==null)throw new Error("Missing comment text. "+this.debugInfo());this.name="#comment",this.type=s.Comment,this.value=this.stringify.comment(a)}return n.prototype.clone=function(){return Object.create(this)},n.prototype.toString=function(i){return this.options.writer.comment(this,this.options.writer.filterOptions(i))},n}(e)}).call(U)),Et.exports}var bt={exports:{}},Yr;function Jt(){return Yr||(Yr=1,(function(){var s,e,t,r=function(n,i){for(var a in i)o.call(i,a)&&(n[a]=i[a]);function l(){this.constructor=n}return l.prototype=i.prototype,n.prototype=new l,n.__super__=i.prototype,n},o={}.hasOwnProperty;t=de().isObject,e=ce(),s=K(),bt.exports=function(n){r(i,n);function i(a,l,f,m){var y;i.__super__.constructor.call(this,a),t(l)&&(y=l,l=y.version,f=y.encoding,m=y.standalone),l||(l="1.0"),this.type=s.Declaration,this.version=this.stringify.xmlVersion(l),f!=null&&(this.encoding=this.stringify.xmlEncoding(f)),m!=null&&(this.standalone=this.stringify.xmlStandalone(m))}return i.prototype.toString=function(a){return this.options.writer.declaration(this,this.options.writer.filterOptions(a))},i}(e)}).call(U)),bt.exports}var wt={exports:{}},Tt={exports:{}},Kr;function Qt(){return Kr||(Kr=1,(function(){var s,e,t=function(o,n){for(var i in n)r.call(n,i)&&(o[i]=n[i]);function a(){this.constructor=o}return a.prototype=n.prototype,o.prototype=new a,o.__super__=n.prototype,o},r={}.hasOwnProperty;e=ce(),s=K(),Tt.exports=function(o){t(n,o);function n(i,a,l,f,m,y){if(n.__super__.constructor.call(this,i),a==null)throw new Error("Missing DTD element name. "+this.debugInfo());if(l==null)throw new Error("Missing DTD attribute name. "+this.debugInfo(a));if(!f)throw new Error("Missing DTD attribute type. "+this.debugInfo(a));if(!m)throw new Error("Missing DTD attribute default. "+this.debugInfo(a));if(m.indexOf("#")!==0&&(m="#"+m),!m.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/))throw new Error("Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. "+this.debugInfo(a));if(y&&!m.match(/^(#FIXED|#DEFAULT)$/))throw new Error("Default value only applies to #FIXED or #DEFAULT. "+this.debugInfo(a));this.elementName=this.stringify.name(a),this.type=s.AttributeDeclaration,this.attributeName=this.stringify.name(l),this.attributeType=this.stringify.dtdAttType(f),y&&(this.defaultValue=this.stringify.dtdAttDefault(y)),this.defaultValueType=m}return n.prototype.toString=function(i){return this.options.writer.dtdAttList(this,this.options.writer.filterOptions(i))},n}(e)}).call(U)),Tt.exports}var _t={exports:{}},Jr;function er(){return Jr||(Jr=1,(function(){var s,e,t,r=function(n,i){for(var a in i)o.call(i,a)&&(n[a]=i[a]);function l(){this.constructor=n}return l.prototype=i.prototype,n.prototype=new l,n.__super__=i.prototype,n},o={}.hasOwnProperty;t=de().isObject,e=ce(),s=K(),_t.exports=function(n){r(i,n);function i(a,l,f,m){if(i.__super__.constructor.call(this,a),f==null)throw new Error("Missing DTD entity name. "+this.debugInfo(f));if(m==null)throw new Error("Missing DTD entity value. "+this.debugInfo(f));if(this.pe=!!l,this.name=this.stringify.name(f),this.type=s.EntityDeclaration,!t(m))this.value=this.stringify.dtdEntityValue(m),this.internal=!0;else{if(!m.pubID&&!m.sysID)throw new Error("Public and/or system identifiers are required for an external entity. "+this.debugInfo(f));if(m.pubID&&!m.sysID)throw new Error("System identifier is required for a public external entity. "+this.debugInfo(f));if(this.internal=!1,m.pubID!=null&&(this.pubID=this.stringify.dtdPubID(m.pubID)),m.sysID!=null&&(this.sysID=this.stringify.dtdSysID(m.sysID)),m.nData!=null&&(this.nData=this.stringify.dtdNData(m.nData)),this.pe&&this.nData)throw new Error("Notation declaration is not allowed in a parameter entity. "+this.debugInfo(f))}}return Object.defineProperty(i.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(i.prototype,"systemId",{get:function(){return this.sysID}}),Object.defineProperty(i.prototype,"notationName",{get:function(){return this.nData||null}}),Object.defineProperty(i.prototype,"inputEncoding",{get:function(){return null}}),Object.defineProperty(i.prototype,"xmlEncoding",{get:function(){return null}}),Object.defineProperty(i.prototype,"xmlVersion",{get:function(){return null}}),i.prototype.toString=function(a){return this.options.writer.dtdEntity(this,this.options.writer.filterOptions(a))},i}(e)}).call(U)),_t.exports}var vt={exports:{}},Qr;function tr(){return Qr||(Qr=1,(function(){var s,e,t=function(o,n){for(var i in n)r.call(n,i)&&(o[i]=n[i]);function a(){this.constructor=o}return a.prototype=n.prototype,o.prototype=new a,o.__super__=n.prototype,o},r={}.hasOwnProperty;e=ce(),s=K(),vt.exports=function(o){t(n,o);function n(i,a,l){if(n.__super__.constructor.call(this,i),a==null)throw new Error("Missing DTD element name. "+this.debugInfo());l||(l="(#PCDATA)"),Array.isArray(l)&&(l="("+l.join(",")+")"),this.name=this.stringify.name(a),this.type=s.ElementDeclaration,this.value=this.stringify.dtdElementValue(l)}return n.prototype.toString=function(i){return this.options.writer.dtdElement(this,this.options.writer.filterOptions(i))},n}(e)}).call(U)),vt.exports}var Ct={exports:{}},en;function rr(){return en||(en=1,(function(){var s,e,t=function(o,n){for(var i in n)r.call(n,i)&&(o[i]=n[i]);function a(){this.constructor=o}return a.prototype=n.prototype,o.prototype=new a,o.__super__=n.prototype,o},r={}.hasOwnProperty;e=ce(),s=K(),Ct.exports=function(o){t(n,o);function n(i,a,l){if(n.__super__.constructor.call(this,i),a==null)throw new Error("Missing DTD notation name. "+this.debugInfo(a));if(!l.pubID&&!l.sysID)throw new Error("Public or system identifiers are required for an external entity. "+this.debugInfo(a));this.name=this.stringify.name(a),this.type=s.NotationDeclaration,l.pubID!=null&&(this.pubID=this.stringify.dtdPubID(l.pubID)),l.sysID!=null&&(this.sysID=this.stringify.dtdSysID(l.sysID))}return Object.defineProperty(n.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(n.prototype,"systemId",{get:function(){return this.sysID}}),n.prototype.toString=function(i){return this.options.writer.dtdNotation(this,this.options.writer.filterOptions(i))},n}(e)}).call(U)),Ct.exports}var tn;function nr(){return tn||(tn=1,(function(){var s,e,t,r,o,n,i,a,l=function(m,y){for(var w in y)f.call(y,w)&&(m[w]=y[w]);function h(){this.constructor=m}return h.prototype=y.prototype,m.prototype=new h,m.__super__=y.prototype,m},f={}.hasOwnProperty;a=de().isObject,i=ce(),s=K(),e=Qt(),r=er(),t=tr(),o=rr(),n=Gt(),wt.exports=function(m){l(y,m);function y(w,h,E){var g,_,b,u,c,p;if(y.__super__.constructor.call(this,w),this.type=s.DocType,w.children){for(u=w.children,_=0,b=u.length;_<b;_++)if(g=u[_],g.type===s.Element){this.name=g.name;break}}this.documentObject=w,a(h)&&(c=h,h=c.pubID,E=c.sysID),E==null&&(p=[h,E],E=p[0],h=p[1]),h!=null&&(this.pubID=this.stringify.dtdPubID(h)),E!=null&&(this.sysID=this.stringify.dtdSysID(E))}return Object.defineProperty(y.prototype,"entities",{get:function(){var w,h,E,g,_;for(g={},_=this.children,h=0,E=_.length;h<E;h++)w=_[h],w.type===s.EntityDeclaration&&!w.pe&&(g[w.name]=w);return new n(g)}}),Object.defineProperty(y.prototype,"notations",{get:function(){var w,h,E,g,_;for(g={},_=this.children,h=0,E=_.length;h<E;h++)w=_[h],w.type===s.NotationDeclaration&&(g[w.name]=w);return new n(g)}}),Object.defineProperty(y.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(y.prototype,"systemId",{get:function(){return this.sysID}}),Object.defineProperty(y.prototype,"internalSubset",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),y.prototype.element=function(w,h){var E;return E=new t(this,w,h),this.children.push(E),this},y.prototype.attList=function(w,h,E,g,_){var b;return b=new e(this,w,h,E,g,_),this.children.push(b),this},y.prototype.entity=function(w,h){var E;return E=new r(this,!1,w,h),this.children.push(E),this},y.prototype.pEntity=function(w,h){var E;return E=new r(this,!0,w,h),this.children.push(E),this},y.prototype.notation=function(w,h){var E;return E=new o(this,w,h),this.children.push(E),this},y.prototype.toString=function(w){return this.options.writer.docType(this,this.options.writer.filterOptions(w))},y.prototype.ele=function(w,h){return this.element(w,h)},y.prototype.att=function(w,h,E,g,_){return this.attList(w,h,E,g,_)},y.prototype.ent=function(w,h){return this.entity(w,h)},y.prototype.pent=function(w,h){return this.pEntity(w,h)},y.prototype.not=function(w,h){return this.notation(w,h)},y.prototype.up=function(){return this.root()||this.documentObject},y.prototype.isEqualNode=function(w){return!(!y.__super__.isEqualNode.apply(this,arguments).isEqualNode(w)||w.name!==this.name||w.publicId!==this.publicId||w.systemId!==this.systemId)},y}(i)}).call(U)),wt.exports}var It={exports:{}},rn;function ir(){return rn||(rn=1,(function(){var s,e,t=function(o,n){for(var i in n)r.call(n,i)&&(o[i]=n[i]);function a(){this.constructor=o}return a.prototype=n.prototype,o.prototype=new a,o.__super__=n.prototype,o},r={}.hasOwnProperty;s=K(),e=ce(),It.exports=function(o){t(n,o);function n(i,a){if(n.__super__.constructor.call(this,i),a==null)throw new Error("Missing raw text. "+this.debugInfo());this.type=s.Raw,this.value=this.stringify.raw(a)}return n.prototype.clone=function(){return Object.create(this)},n.prototype.toString=function(i){return this.options.writer.raw(this,this.options.writer.filterOptions(i))},n}(e)}).call(U)),It.exports}var xt={exports:{}},nn;function or(){return nn||(nn=1,(function(){var s,e,t=function(o,n){for(var i in n)r.call(n,i)&&(o[i]=n[i]);function a(){this.constructor=o}return a.prototype=n.prototype,o.prototype=new a,o.__super__=n.prototype,o},r={}.hasOwnProperty;s=K(),e=ke(),xt.exports=function(o){t(n,o);function n(i,a){if(n.__super__.constructor.call(this,i),a==null)throw new Error("Missing element text. "+this.debugInfo());this.name="#text",this.type=s.Text,this.value=this.stringify.text(a)}return Object.defineProperty(n.prototype,"isElementContentWhitespace",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(n.prototype,"wholeText",{get:function(){var i,a,l;for(l="",a=this.previousSibling;a;)l=a.data+l,a=a.previousSibling;for(l+=this.data,i=this.nextSibling;i;)l=l+i.data,i=i.nextSibling;return l}}),n.prototype.clone=function(){return Object.create(this)},n.prototype.toString=function(i){return this.options.writer.text(this,this.options.writer.filterOptions(i))},n.prototype.splitText=function(i){throw new Error("This DOM method is not implemented."+this.debugInfo())},n.prototype.replaceWholeText=function(i){throw new Error("This DOM method is not implemented."+this.debugInfo())},n}(e)}).call(U)),xt.exports}var Dt={exports:{}},on;function sr(){return on||(on=1,(function(){var s,e,t=function(o,n){for(var i in n)r.call(n,i)&&(o[i]=n[i]);function a(){this.constructor=o}return a.prototype=n.prototype,o.prototype=new a,o.__super__=n.prototype,o},r={}.hasOwnProperty;s=K(),e=ke(),Dt.exports=function(o){t(n,o);function n(i,a,l){if(n.__super__.constructor.call(this,i),a==null)throw new Error("Missing instruction target. "+this.debugInfo());this.type=s.ProcessingInstruction,this.target=this.stringify.insTarget(a),this.name=this.target,l&&(this.value=this.stringify.insValue(l))}return n.prototype.clone=function(){return Object.create(this)},n.prototype.toString=function(i){return this.options.writer.processingInstruction(this,this.options.writer.filterOptions(i))},n.prototype.isEqualNode=function(i){return!(!n.__super__.isEqualNode.apply(this,arguments).isEqualNode(i)||i.target!==this.target)},n}(e)}).call(U)),Dt.exports}var Nt={exports:{}},sn;function kn(){return sn||(sn=1,(function(){var s,e,t=function(o,n){for(var i in n)r.call(n,i)&&(o[i]=n[i]);function a(){this.constructor=o}return a.prototype=n.prototype,o.prototype=new a,o.__super__=n.prototype,o},r={}.hasOwnProperty;e=ce(),s=K(),Nt.exports=function(o){t(n,o);function n(i){n.__super__.constructor.call(this,i),this.type=s.Dummy}return n.prototype.clone=function(){return Object.create(this)},n.prototype.toString=function(i){return""},n}(e)}).call(U)),Nt.exports}var St={exports:{}},an;function Io(){return an||(an=1,(function(){St.exports=function(){function s(e){this.nodes=e}return Object.defineProperty(s.prototype,"length",{get:function(){return this.nodes.length||0}}),s.prototype.clone=function(){return this.nodes=null},s.prototype.item=function(e){return this.nodes[e]||null},s}()}).call(U)),St.exports}var Lt={exports:{}},cn;function xo(){return cn||(cn=1,(function(){Lt.exports={Disconnected:1,Preceding:2,Following:4,Contains:8,ContainedBy:16,ImplementationSpecific:32}}).call(U)),Lt.exports}var ln;function ce(){return ln||(ln=1,(function(){var s,e,t,r,o,n,i,a,l,f,m,y,w,h,E,g,_,b={}.hasOwnProperty;_=de(),g=_.isObject,E=_.isFunction,h=_.isEmpty,w=_.getValue,a=null,t=null,r=null,o=null,n=null,m=null,y=null,f=null,i=null,e=null,l=null,s=null,ft.exports=function(){function u(c){this.parent=c,this.parent&&(this.options=this.parent.options,this.stringify=this.parent.stringify),this.value=null,this.children=[],this.baseURI=null,a||(a=Zt(),t=Yt(),r=Kt(),o=Jt(),n=nr(),m=ir(),y=or(),f=sr(),i=kn(),e=K(),l=Io(),Gt(),s=xo())}return Object.defineProperty(u.prototype,"nodeName",{get:function(){return this.name}}),Object.defineProperty(u.prototype,"nodeType",{get:function(){return this.type}}),Object.defineProperty(u.prototype,"nodeValue",{get:function(){return this.value}}),Object.defineProperty(u.prototype,"parentNode",{get:function(){return this.parent}}),Object.defineProperty(u.prototype,"childNodes",{get:function(){return(!this.childNodeList||!this.childNodeList.nodes)&&(this.childNodeList=new l(this.children)),this.childNodeList}}),Object.defineProperty(u.prototype,"firstChild",{get:function(){return this.children[0]||null}}),Object.defineProperty(u.prototype,"lastChild",{get:function(){return this.children[this.children.length-1]||null}}),Object.defineProperty(u.prototype,"previousSibling",{get:function(){var c;return c=this.parent.children.indexOf(this),this.parent.children[c-1]||null}}),Object.defineProperty(u.prototype,"nextSibling",{get:function(){var c;return c=this.parent.children.indexOf(this),this.parent.children[c+1]||null}}),Object.defineProperty(u.prototype,"ownerDocument",{get:function(){return this.document()||null}}),Object.defineProperty(u.prototype,"textContent",{get:function(){var c,p,v,I,N;if(this.nodeType===e.Element||this.nodeType===e.DocumentFragment){for(N="",I=this.children,p=0,v=I.length;p<v;p++)c=I[p],c.textContent&&(N+=c.textContent);return N}else return null},set:function(c){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),u.prototype.setParent=function(c){var p,v,I,N,S;for(this.parent=c,c&&(this.options=c.options,this.stringify=c.stringify),N=this.children,S=[],v=0,I=N.length;v<I;v++)p=N[v],S.push(p.setParent(this));return S},u.prototype.element=function(c,p,v){var I,N,S,d,D,L,M,F,k,q,j;if(L=null,p===null&&v==null&&(k=[{},null],p=k[0],v=k[1]),p==null&&(p={}),p=w(p),g(p)||(q=[p,v],v=q[0],p=q[1]),c!=null&&(c=w(c)),Array.isArray(c))for(S=0,M=c.length;S<M;S++)N=c[S],L=this.element(N);else if(E(c))L=this.element(c.apply());else if(g(c)){for(D in c)if(b.call(c,D))if(j=c[D],E(j)&&(j=j.apply()),!this.options.ignoreDecorators&&this.stringify.convertAttKey&&D.indexOf(this.stringify.convertAttKey)===0)L=this.attribute(D.substr(this.stringify.convertAttKey.length),j);else if(!this.options.separateArrayItems&&Array.isArray(j)&&h(j))L=this.dummy();else if(g(j)&&h(j))L=this.element(D);else if(!this.options.keepNullNodes&&j==null)L=this.dummy();else if(!this.options.separateArrayItems&&Array.isArray(j))for(d=0,F=j.length;d<F;d++)N=j[d],I={},I[D]=N,L=this.element(I);else g(j)?!this.options.ignoreDecorators&&this.stringify.convertTextKey&&D.indexOf(this.stringify.convertTextKey)===0?L=this.element(j):(L=this.element(D),L.element(j)):L=this.element(D,j)}else!this.options.keepNullNodes&&v===null?L=this.dummy():!this.options.ignoreDecorators&&this.stringify.convertTextKey&&c.indexOf(this.stringify.convertTextKey)===0?L=this.text(v):!this.options.ignoreDecorators&&this.stringify.convertCDataKey&&c.indexOf(this.stringify.convertCDataKey)===0?L=this.cdata(v):!this.options.ignoreDecorators&&this.stringify.convertCommentKey&&c.indexOf(this.stringify.convertCommentKey)===0?L=this.comment(v):!this.options.ignoreDecorators&&this.stringify.convertRawKey&&c.indexOf(this.stringify.convertRawKey)===0?L=this.raw(v):!this.options.ignoreDecorators&&this.stringify.convertPIKey&&c.indexOf(this.stringify.convertPIKey)===0?L=this.instruction(c.substr(this.stringify.convertPIKey.length),v):L=this.node(c,p,v);if(L==null)throw new Error("Could not create any elements with: "+c+". "+this.debugInfo());return L},u.prototype.insertBefore=function(c,p,v){var I,N,S,d,D;if(c!=null&&c.type)return S=c,d=p,S.setParent(this),d?(N=children.indexOf(d),D=children.splice(N),children.push(S),Array.prototype.push.apply(children,D)):children.push(S),S;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(c));return N=this.parent.children.indexOf(this),D=this.parent.children.splice(N),I=this.parent.element(c,p,v),Array.prototype.push.apply(this.parent.children,D),I},u.prototype.insertAfter=function(c,p,v){var I,N,S;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(c));return N=this.parent.children.indexOf(this),S=this.parent.children.splice(N+1),I=this.parent.element(c,p,v),Array.prototype.push.apply(this.parent.children,S),I},u.prototype.remove=function(){var c;if(this.isRoot)throw new Error("Cannot remove the root element. "+this.debugInfo());return c=this.parent.children.indexOf(this),[].splice.apply(this.parent.children,[c,c-c+1].concat([])),this.parent},u.prototype.node=function(c,p,v){var I,N;return c!=null&&(c=w(c)),p||(p={}),p=w(p),g(p)||(N=[p,v],v=N[0],p=N[1]),I=new a(this,c,p),v!=null&&I.text(v),this.children.push(I),I},u.prototype.text=function(c){var p;return g(c)&&this.element(c),p=new y(this,c),this.children.push(p),this},u.prototype.cdata=function(c){var p;return p=new t(this,c),this.children.push(p),this},u.prototype.comment=function(c){var p;return p=new r(this,c),this.children.push(p),this},u.prototype.commentBefore=function(c){var p,v;return p=this.parent.children.indexOf(this),v=this.parent.children.splice(p),this.parent.comment(c),Array.prototype.push.apply(this.parent.children,v),this},u.prototype.commentAfter=function(c){var p,v;return p=this.parent.children.indexOf(this),v=this.parent.children.splice(p+1),this.parent.comment(c),Array.prototype.push.apply(this.parent.children,v),this},u.prototype.raw=function(c){var p;return p=new m(this,c),this.children.push(p),this},u.prototype.dummy=function(){var c;return c=new i(this),c},u.prototype.instruction=function(c,p){var v,I,N,S,d;if(c!=null&&(c=w(c)),p!=null&&(p=w(p)),Array.isArray(c))for(S=0,d=c.length;S<d;S++)v=c[S],this.instruction(v);else if(g(c))for(v in c)b.call(c,v)&&(I=c[v],this.instruction(v,I));else E(p)&&(p=p.apply()),N=new f(this,c,p),this.children.push(N);return this},u.prototype.instructionBefore=function(c,p){var v,I;return v=this.parent.children.indexOf(this),I=this.parent.children.splice(v),this.parent.instruction(c,p),Array.prototype.push.apply(this.parent.children,I),this},u.prototype.instructionAfter=function(c,p){var v,I;return v=this.parent.children.indexOf(this),I=this.parent.children.splice(v+1),this.parent.instruction(c,p),Array.prototype.push.apply(this.parent.children,I),this},u.prototype.declaration=function(c,p,v){var I,N;return I=this.document(),N=new o(I,c,p,v),I.children.length===0?I.children.unshift(N):I.children[0].type===e.Declaration?I.children[0]=N:I.children.unshift(N),I.root()||I},u.prototype.dtd=function(c,p){var v,I,N,S,d,D,L,M,F,k;for(I=this.document(),N=new n(I,c,p),F=I.children,S=d=0,L=F.length;d<L;S=++d)if(v=F[S],v.type===e.DocType)return I.children[S]=N,N;for(k=I.children,S=D=0,M=k.length;D<M;S=++D)if(v=k[S],v.isRoot)return I.children.splice(S,0,N),N;return I.children.push(N),N},u.prototype.up=function(){if(this.isRoot)throw new Error("The root node has no parent. Use doc() if you need to get the document object.");return this.parent},u.prototype.root=function(){var c;for(c=this;c;){if(c.type===e.Document)return c.rootObject;if(c.isRoot)return c;c=c.parent}},u.prototype.document=function(){var c;for(c=this;c;){if(c.type===e.Document)return c;c=c.parent}},u.prototype.end=function(c){return this.document().end(c)},u.prototype.prev=function(){var c;if(c=this.parent.children.indexOf(this),c<1)throw new Error("Already at the first node. "+this.debugInfo());return this.parent.children[c-1]},u.prototype.next=function(){var c;if(c=this.parent.children.indexOf(this),c===-1||c===this.parent.children.length-1)throw new Error("Already at the last node. "+this.debugInfo());return this.parent.children[c+1]},u.prototype.importDocument=function(c){var p;return p=c.root().clone(),p.parent=this,p.isRoot=!1,this.children.push(p),this},u.prototype.debugInfo=function(c){var p,v;return c=c||this.name,c==null&&!((p=this.parent)!=null&&p.name)?"":c==null?"parent: <"+this.parent.name+">":(v=this.parent)!=null&&v.name?"node: <"+c+">, parent: <"+this.parent.name+">":"node: <"+c+">"},u.prototype.ele=function(c,p,v){return this.element(c,p,v)},u.prototype.nod=function(c,p,v){return this.node(c,p,v)},u.prototype.txt=function(c){return this.text(c)},u.prototype.dat=function(c){return this.cdata(c)},u.prototype.com=function(c){return this.comment(c)},u.prototype.ins=function(c,p){return this.instruction(c,p)},u.prototype.doc=function(){return this.document()},u.prototype.dec=function(c,p,v){return this.declaration(c,p,v)},u.prototype.e=function(c,p,v){return this.element(c,p,v)},u.prototype.n=function(c,p,v){return this.node(c,p,v)},u.prototype.t=function(c){return this.text(c)},u.prototype.d=function(c){return this.cdata(c)},u.prototype.c=function(c){return this.comment(c)},u.prototype.r=function(c){return this.raw(c)},u.prototype.i=function(c,p){return this.instruction(c,p)},u.prototype.u=function(){return this.up()},u.prototype.importXMLBuilder=function(c){return this.importDocument(c)},u.prototype.replaceChild=function(c,p){throw new Error("This DOM method is not implemented."+this.debugInfo())},u.prototype.removeChild=function(c){throw new Error("This DOM method is not implemented."+this.debugInfo())},u.prototype.appendChild=function(c){throw new Error("This DOM method is not implemented."+this.debugInfo())},u.prototype.hasChildNodes=function(){return this.children.length!==0},u.prototype.cloneNode=function(c){throw new Error("This DOM method is not implemented."+this.debugInfo())},u.prototype.normalize=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},u.prototype.isSupported=function(c,p){return!0},u.prototype.hasAttributes=function(){return this.attribs.length!==0},u.prototype.compareDocumentPosition=function(c){var p,v;return p=this,p===c?0:this.document()!==c.document()?(v=s.Disconnected|s.ImplementationSpecific,Math.random()<.5?v|=s.Preceding:v|=s.Following,v):p.isAncestor(c)?s.Contains|s.Preceding:p.isDescendant(c)?s.Contains|s.Following:p.isPreceding(c)?s.Preceding:s.Following},u.prototype.isSameNode=function(c){throw new Error("This DOM method is not implemented."+this.debugInfo())},u.prototype.lookupPrefix=function(c){throw new Error("This DOM method is not implemented."+this.debugInfo())},u.prototype.isDefaultNamespace=function(c){throw new Error("This DOM method is not implemented."+this.debugInfo())},u.prototype.lookupNamespaceURI=function(c){throw new Error("This DOM method is not implemented."+this.debugInfo())},u.prototype.isEqualNode=function(c){var p,v,I;if(c.nodeType!==this.nodeType||c.children.length!==this.children.length)return!1;for(p=v=0,I=this.children.length-1;0<=I?v<=I:v>=I;p=0<=I?++v:--v)if(!this.children[p].isEqualNode(c.children[p]))return!1;return!0},u.prototype.getFeature=function(c,p){throw new Error("This DOM method is not implemented."+this.debugInfo())},u.prototype.setUserData=function(c,p,v){throw new Error("This DOM method is not implemented."+this.debugInfo())},u.prototype.getUserData=function(c){throw new Error("This DOM method is not implemented."+this.debugInfo())},u.prototype.contains=function(c){return c?c===this||this.isDescendant(c):!1},u.prototype.isDescendant=function(c){var p,v,I,N,S;for(S=this.children,I=0,N=S.length;I<N;I++)if(p=S[I],c===p||(v=p.isDescendant(c),v))return!0;return!1},u.prototype.isAncestor=function(c){return c.isDescendant(this)},u.prototype.isPreceding=function(c){var p,v;return p=this.treePosition(c),v=this.treePosition(this),p===-1||v===-1?!1:p<v},u.prototype.isFollowing=function(c){var p,v;return p=this.treePosition(c),v=this.treePosition(this),p===-1||v===-1?!1:p>v},u.prototype.treePosition=function(c){var p,v;return v=0,p=!1,this.foreachTreeNode(this.document(),function(I){if(v++,!p&&I===c)return p=!0}),p?v:-1},u.prototype.foreachTreeNode=function(c,p){var v,I,N,S,d;for(c||(c=this.document()),S=c.children,I=0,N=S.length;I<N;I++){if(v=S[I],d=p(v))return d;if(d=this.foreachTreeNode(v,p),d)return d}},u}()}).call(U)),ft.exports}var Pt={exports:{}},un;function Xn(){return un||(un=1,(function(){var s=function(t,r){return function(){return t.apply(r,arguments)}},e={}.hasOwnProperty;Pt.exports=function(){function t(r){this.assertLegalName=s(this.assertLegalName,this),this.assertLegalChar=s(this.assertLegalChar,this);var o,n,i;r||(r={}),this.options=r,this.options.version||(this.options.version="1.0"),n=r.stringify||{};for(o in n)e.call(n,o)&&(i=n[o],this[o]=i)}return t.prototype.name=function(r){return this.options.noValidation?r:this.assertLegalName(""+r||"")},t.prototype.text=function(r){return this.options.noValidation?r:this.assertLegalChar(this.textEscape(""+r||""))},t.prototype.cdata=function(r){return this.options.noValidation?r:(r=""+r||"",r=r.replace("]]>","]]]]><![CDATA[>"),this.assertLegalChar(r))},t.prototype.comment=function(r){if(this.options.noValidation)return r;if(r=""+r||"",r.match(/--/))throw new Error("Comment text cannot contain double-hypen: "+r);return this.assertLegalChar(r)},t.prototype.raw=function(r){return this.options.noValidation?r:""+r||""},t.prototype.attValue=function(r){return this.options.noValidation?r:this.assertLegalChar(this.attEscape(r=""+r||""))},t.prototype.insTarget=function(r){return this.options.noValidation?r:this.assertLegalChar(""+r||"")},t.prototype.insValue=function(r){if(this.options.noValidation)return r;if(r=""+r||"",r.match(/\?>/))throw new Error("Invalid processing instruction value: "+r);return this.assertLegalChar(r)},t.prototype.xmlVersion=function(r){if(this.options.noValidation)return r;if(r=""+r||"",!r.match(/1\.[0-9]+/))throw new Error("Invalid version number: "+r);return r},t.prototype.xmlEncoding=function(r){if(this.options.noValidation)return r;if(r=""+r||"",!r.match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/))throw new Error("Invalid encoding: "+r);return this.assertLegalChar(r)},t.prototype.xmlStandalone=function(r){return this.options.noValidation?r:r?"yes":"no"},t.prototype.dtdPubID=function(r){return this.options.noValidation?r:this.assertLegalChar(""+r||"")},t.prototype.dtdSysID=function(r){return this.options.noValidation?r:this.assertLegalChar(""+r||"")},t.prototype.dtdElementValue=function(r){return this.options.noValidation?r:this.assertLegalChar(""+r||"")},t.prototype.dtdAttType=function(r){return this.options.noValidation?r:this.assertLegalChar(""+r||"")},t.prototype.dtdAttDefault=function(r){return this.options.noValidation?r:this.assertLegalChar(""+r||"")},t.prototype.dtdEntityValue=function(r){return this.options.noValidation?r:this.assertLegalChar(""+r||"")},t.prototype.dtdNData=function(r){return this.options.noValidation?r:this.assertLegalChar(""+r||"")},t.prototype.convertAttKey="@",t.prototype.convertPIKey="?",t.prototype.convertTextKey="#text",t.prototype.convertCDataKey="#cdata",t.prototype.convertCommentKey="#comment",t.prototype.convertRawKey="#raw",t.prototype.assertLegalChar=function(r){var o,n;if(this.options.noValidation)return r;if(o="",this.options.version==="1.0"){if(o=/[\0-\x08\x0B\f\x0E-\x1F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,n=r.match(o))throw new Error("Invalid character in string: "+r+" at index "+n.index)}else if(this.options.version==="1.1"&&(o=/[\0\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,n=r.match(o)))throw new Error("Invalid character in string: "+r+" at index "+n.index);return r},t.prototype.assertLegalName=function(r){var o;if(this.options.noValidation)return r;if(this.assertLegalChar(r),o=/^([:A-Z_a-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]|[\uD800-\uDB7F][\uDC00-\uDFFF])([\x2D\.0-:A-Z_a-z\xB7\xC0-\xD6\xD8-\xF6\xF8-\u037D\u037F-\u1FFF\u200C\u200D\u203F\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]|[\uD800-\uDB7F][\uDC00-\uDFFF])*$/,!r.match(o))throw new Error("Invalid character in name");return r},t.prototype.textEscape=function(r){var o;return this.options.noValidation?r:(o=this.options.noDoubleEncoding?/(?!&\S+;)&/g:/&/g,r.replace(o,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r/g,"&#xD;"))},t.prototype.attEscape=function(r){var o;return this.options.noValidation?r:(o=this.options.noDoubleEncoding?/(?!&\S+;)&/g:/&/g,r.replace(o,"&amp;").replace(/</g,"&lt;").replace(/"/g,"&quot;").replace(/\t/g,"&#x9;").replace(/\n/g,"&#xA;").replace(/\r/g,"&#xD;"))},t}()}).call(U)),Pt.exports}var Ot={exports:{}},Mt={exports:{}},At={exports:{}},fn;function Xe(){return fn||(fn=1,(function(){At.exports={None:0,OpenTag:1,InsideTag:2,CloseTag:3}}).call(U)),At.exports}var hn;function $n(){return hn||(hn=1,(function(){var s,e,t,r={}.hasOwnProperty;t=de().assign,s=K(),Jt(),nr(),Yt(),Kt(),Zt(),ir(),or(),sr(),kn(),Qt(),tr(),er(),rr(),e=Xe(),Mt.exports=function(){function o(n){var i,a,l;n||(n={}),this.options=n,a=n.writer||{};for(i in a)r.call(a,i)&&(l=a[i],this["_"+i]=this[i],this[i]=l)}return o.prototype.filterOptions=function(n){var i,a,l,f,m,y,w,h;return n||(n={}),n=t({},this.options,n),i={writer:this},i.pretty=n.pretty||!1,i.allowEmpty=n.allowEmpty||!1,i.indent=(a=n.indent)!=null?a:"  ",i.newline=(l=n.newline)!=null?l:`
`,i.offset=(f=n.offset)!=null?f:0,i.dontPrettyTextNodes=(m=(y=n.dontPrettyTextNodes)!=null?y:n.dontprettytextnodes)!=null?m:0,i.spaceBeforeSlash=(w=(h=n.spaceBeforeSlash)!=null?h:n.spacebeforeslash)!=null?w:"",i.spaceBeforeSlash===!0&&(i.spaceBeforeSlash=" "),i.suppressPrettyCount=0,i.user={},i.state=e.None,i},o.prototype.indent=function(n,i,a){var l;return!i.pretty||i.suppressPrettyCount?"":i.pretty&&(l=(a||0)+i.offset+1,l>0)?new Array(l).join(i.indent):""},o.prototype.endline=function(n,i,a){return!i.pretty||i.suppressPrettyCount?"":i.newline},o.prototype.attribute=function(n,i,a){var l;return this.openAttribute(n,i,a),l=" "+n.name+'="'+n.value+'"',this.closeAttribute(n,i,a),l},o.prototype.cdata=function(n,i,a){var l;return this.openNode(n,i,a),i.state=e.OpenTag,l=this.indent(n,i,a)+"<![CDATA[",i.state=e.InsideTag,l+=n.value,i.state=e.CloseTag,l+="]]>"+this.endline(n,i,a),i.state=e.None,this.closeNode(n,i,a),l},o.prototype.comment=function(n,i,a){var l;return this.openNode(n,i,a),i.state=e.OpenTag,l=this.indent(n,i,a)+"<!-- ",i.state=e.InsideTag,l+=n.value,i.state=e.CloseTag,l+=" -->"+this.endline(n,i,a),i.state=e.None,this.closeNode(n,i,a),l},o.prototype.declaration=function(n,i,a){var l;return this.openNode(n,i,a),i.state=e.OpenTag,l=this.indent(n,i,a)+"<?xml",i.state=e.InsideTag,l+=' version="'+n.version+'"',n.encoding!=null&&(l+=' encoding="'+n.encoding+'"'),n.standalone!=null&&(l+=' standalone="'+n.standalone+'"'),i.state=e.CloseTag,l+=i.spaceBeforeSlash+"?>",l+=this.endline(n,i,a),i.state=e.None,this.closeNode(n,i,a),l},o.prototype.docType=function(n,i,a){var l,f,m,y,w;if(a||(a=0),this.openNode(n,i,a),i.state=e.OpenTag,y=this.indent(n,i,a),y+="<!DOCTYPE "+n.root().name,n.pubID&&n.sysID?y+=' PUBLIC "'+n.pubID+'" "'+n.sysID+'"':n.sysID&&(y+=' SYSTEM "'+n.sysID+'"'),n.children.length>0){for(y+=" [",y+=this.endline(n,i,a),i.state=e.InsideTag,w=n.children,f=0,m=w.length;f<m;f++)l=w[f],y+=this.writeChildNode(l,i,a+1);i.state=e.CloseTag,y+="]"}return i.state=e.CloseTag,y+=i.spaceBeforeSlash+">",y+=this.endline(n,i,a),i.state=e.None,this.closeNode(n,i,a),y},o.prototype.element=function(n,i,a){var l,f,m,y,w,h,E,g,_,b,u,c,p,v;a||(a=0),b=!1,u="",this.openNode(n,i,a),i.state=e.OpenTag,u+=this.indent(n,i,a)+"<"+n.name,c=n.attribs;for(_ in c)r.call(c,_)&&(l=c[_],u+=this.attribute(l,i,a));if(m=n.children.length,y=m===0?null:n.children[0],m===0||n.children.every(function(I){return(I.type===s.Text||I.type===s.Raw)&&I.value===""}))i.allowEmpty?(u+=">",i.state=e.CloseTag,u+="</"+n.name+">"+this.endline(n,i,a)):(i.state=e.CloseTag,u+=i.spaceBeforeSlash+"/>"+this.endline(n,i,a));else if(i.pretty&&m===1&&(y.type===s.Text||y.type===s.Raw)&&y.value!=null)u+=">",i.state=e.InsideTag,i.suppressPrettyCount++,b=!0,u+=this.writeChildNode(y,i,a+1),i.suppressPrettyCount--,b=!1,i.state=e.CloseTag,u+="</"+n.name+">"+this.endline(n,i,a);else{if(i.dontPrettyTextNodes){for(p=n.children,w=0,E=p.length;w<E;w++)if(f=p[w],(f.type===s.Text||f.type===s.Raw)&&f.value!=null){i.suppressPrettyCount++,b=!0;break}}for(u+=">"+this.endline(n,i,a),i.state=e.InsideTag,v=n.children,h=0,g=v.length;h<g;h++)f=v[h],u+=this.writeChildNode(f,i,a+1);i.state=e.CloseTag,u+=this.indent(n,i,a)+"</"+n.name+">",b&&i.suppressPrettyCount--,u+=this.endline(n,i,a),i.state=e.None}return this.closeNode(n,i,a),u},o.prototype.writeChildNode=function(n,i,a){switch(n.type){case s.CData:return this.cdata(n,i,a);case s.Comment:return this.comment(n,i,a);case s.Element:return this.element(n,i,a);case s.Raw:return this.raw(n,i,a);case s.Text:return this.text(n,i,a);case s.ProcessingInstruction:return this.processingInstruction(n,i,a);case s.Dummy:return"";case s.Declaration:return this.declaration(n,i,a);case s.DocType:return this.docType(n,i,a);case s.AttributeDeclaration:return this.dtdAttList(n,i,a);case s.ElementDeclaration:return this.dtdElement(n,i,a);case s.EntityDeclaration:return this.dtdEntity(n,i,a);case s.NotationDeclaration:return this.dtdNotation(n,i,a);default:throw new Error("Unknown XML node type: "+n.constructor.name)}},o.prototype.processingInstruction=function(n,i,a){var l;return this.openNode(n,i,a),i.state=e.OpenTag,l=this.indent(n,i,a)+"<?",i.state=e.InsideTag,l+=n.target,n.value&&(l+=" "+n.value),i.state=e.CloseTag,l+=i.spaceBeforeSlash+"?>",l+=this.endline(n,i,a),i.state=e.None,this.closeNode(n,i,a),l},o.prototype.raw=function(n,i,a){var l;return this.openNode(n,i,a),i.state=e.OpenTag,l=this.indent(n,i,a),i.state=e.InsideTag,l+=n.value,i.state=e.CloseTag,l+=this.endline(n,i,a),i.state=e.None,this.closeNode(n,i,a),l},o.prototype.text=function(n,i,a){var l;return this.openNode(n,i,a),i.state=e.OpenTag,l=this.indent(n,i,a),i.state=e.InsideTag,l+=n.value,i.state=e.CloseTag,l+=this.endline(n,i,a),i.state=e.None,this.closeNode(n,i,a),l},o.prototype.dtdAttList=function(n,i,a){var l;return this.openNode(n,i,a),i.state=e.OpenTag,l=this.indent(n,i,a)+"<!ATTLIST",i.state=e.InsideTag,l+=" "+n.elementName+" "+n.attributeName+" "+n.attributeType,n.defaultValueType!=="#DEFAULT"&&(l+=" "+n.defaultValueType),n.defaultValue&&(l+=' "'+n.defaultValue+'"'),i.state=e.CloseTag,l+=i.spaceBeforeSlash+">"+this.endline(n,i,a),i.state=e.None,this.closeNode(n,i,a),l},o.prototype.dtdElement=function(n,i,a){var l;return this.openNode(n,i,a),i.state=e.OpenTag,l=this.indent(n,i,a)+"<!ELEMENT",i.state=e.InsideTag,l+=" "+n.name+" "+n.value,i.state=e.CloseTag,l+=i.spaceBeforeSlash+">"+this.endline(n,i,a),i.state=e.None,this.closeNode(n,i,a),l},o.prototype.dtdEntity=function(n,i,a){var l;return this.openNode(n,i,a),i.state=e.OpenTag,l=this.indent(n,i,a)+"<!ENTITY",i.state=e.InsideTag,n.pe&&(l+=" %"),l+=" "+n.name,n.value?l+=' "'+n.value+'"':(n.pubID&&n.sysID?l+=' PUBLIC "'+n.pubID+'" "'+n.sysID+'"':n.sysID&&(l+=' SYSTEM "'+n.sysID+'"'),n.nData&&(l+=" NDATA "+n.nData)),i.state=e.CloseTag,l+=i.spaceBeforeSlash+">"+this.endline(n,i,a),i.state=e.None,this.closeNode(n,i,a),l},o.prototype.dtdNotation=function(n,i,a){var l;return this.openNode(n,i,a),i.state=e.OpenTag,l=this.indent(n,i,a)+"<!NOTATION",i.state=e.InsideTag,l+=" "+n.name,n.pubID&&n.sysID?l+=' PUBLIC "'+n.pubID+'" "'+n.sysID+'"':n.pubID?l+=' PUBLIC "'+n.pubID+'"':n.sysID&&(l+=' SYSTEM "'+n.sysID+'"'),i.state=e.CloseTag,l+=i.spaceBeforeSlash+">"+this.endline(n,i,a),i.state=e.None,this.closeNode(n,i,a),l},o.prototype.openNode=function(n,i,a){},o.prototype.closeNode=function(n,i,a){},o.prototype.openAttribute=function(n,i,a){},o.prototype.closeAttribute=function(n,i,a){},o}()}).call(U)),Mt.exports}var dn;function ar(){return dn||(dn=1,(function(){var s,e=function(r,o){for(var n in o)t.call(o,n)&&(r[n]=o[n]);function i(){this.constructor=r}return i.prototype=o.prototype,r.prototype=new i,r.__super__=o.prototype,r},t={}.hasOwnProperty;s=$n(),Ot.exports=function(r){e(o,r);function o(n){o.__super__.constructor.call(this,n)}return o.prototype.document=function(n,i){var a,l,f,m,y;for(i=this.filterOptions(i),m="",y=n.children,l=0,f=y.length;l<f;l++)a=y[l],m+=this.writeChildNode(a,i,0);return i.pretty&&m.slice(-i.newline.length)===i.newline&&(m=m.slice(0,-i.newline.length)),m},o}(s)}).call(U)),Ot.exports}var pn;function jn(){return pn||(pn=1,(function(){var s,e,t,r,o,n,i,a=function(f,m){for(var y in m)l.call(m,y)&&(f[y]=m[y]);function w(){this.constructor=f}return w.prototype=m.prototype,f.prototype=new w,f.__super__=m.prototype,f},l={}.hasOwnProperty;i=de().isPlainObject,t=Bn(),e=Co(),r=ce(),s=K(),n=Xn(),o=ar(),at.exports=function(f){a(m,f);function m(y){m.__super__.constructor.call(this,null),this.name="#document",this.type=s.Document,this.documentURI=null,this.domConfig=new e,y||(y={}),y.writer||(y.writer=new o),this.options=y,this.stringify=new n(y)}return Object.defineProperty(m.prototype,"implementation",{value:new t}),Object.defineProperty(m.prototype,"doctype",{get:function(){var y,w,h,E;for(E=this.children,w=0,h=E.length;w<h;w++)if(y=E[w],y.type===s.DocType)return y;return null}}),Object.defineProperty(m.prototype,"documentElement",{get:function(){return this.rootObject||null}}),Object.defineProperty(m.prototype,"inputEncoding",{get:function(){return null}}),Object.defineProperty(m.prototype,"strictErrorChecking",{get:function(){return!1}}),Object.defineProperty(m.prototype,"xmlEncoding",{get:function(){return this.children.length!==0&&this.children[0].type===s.Declaration?this.children[0].encoding:null}}),Object.defineProperty(m.prototype,"xmlStandalone",{get:function(){return this.children.length!==0&&this.children[0].type===s.Declaration?this.children[0].standalone==="yes":!1}}),Object.defineProperty(m.prototype,"xmlVersion",{get:function(){return this.children.length!==0&&this.children[0].type===s.Declaration?this.children[0].version:"1.0"}}),Object.defineProperty(m.prototype,"URL",{get:function(){return this.documentURI}}),Object.defineProperty(m.prototype,"origin",{get:function(){return null}}),Object.defineProperty(m.prototype,"compatMode",{get:function(){return null}}),Object.defineProperty(m.prototype,"characterSet",{get:function(){return null}}),Object.defineProperty(m.prototype,"contentType",{get:function(){return null}}),m.prototype.end=function(y){var w;return w={},y?i(y)&&(w=y,y=this.options.writer):y=this.options.writer,y.document(this,y.filterOptions(w))},m.prototype.toString=function(y){return this.options.writer.document(this,this.options.writer.filterOptions(y))},m.prototype.createElement=function(y){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createDocumentFragment=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createTextNode=function(y){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createComment=function(y){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createCDATASection=function(y){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createProcessingInstruction=function(y,w){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createAttribute=function(y){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createEntityReference=function(y){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.getElementsByTagName=function(y){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.importNode=function(y,w){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createElementNS=function(y,w){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createAttributeNS=function(y,w){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.getElementsByTagNameNS=function(y,w){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.getElementById=function(y){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.adoptNode=function(y){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.normalizeDocument=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.renameNode=function(y,w,h){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.getElementsByClassName=function(y){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createEvent=function(y){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createRange=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createNodeIterator=function(y,w,h){throw new Error("This DOM method is not implemented."+this.debugInfo())},m.prototype.createTreeWalker=function(y,w,h){throw new Error("This DOM method is not implemented."+this.debugInfo())},m}(r)}).call(U)),at.exports}var Rt={exports:{}},gn;function Do(){return gn||(gn=1,(function(){var s,e,t,r,o,n,i,a,l,f,m,y,w,h,E,g,_,b,u,c,p,v,I,N={}.hasOwnProperty;I=de(),p=I.isObject,c=I.isFunction,v=I.isPlainObject,u=I.getValue,s=K(),y=jn(),w=Zt(),r=Yt(),o=Kt(),E=ir(),b=or(),h=sr(),f=Jt(),m=nr(),n=Qt(),a=er(),i=tr(),l=rr(),t=Un(),_=Xn(),g=ar(),e=Xe(),Rt.exports=function(){function S(d,D,L){var M;this.name="?xml",this.type=s.Document,d||(d={}),M={},d.writer?v(d.writer)&&(M=d.writer,d.writer=new g):d.writer=new g,this.options=d,this.writer=d.writer,this.writerOptions=this.writer.filterOptions(M),this.stringify=new _(d),this.onDataCallback=D||function(){},this.onEndCallback=L||function(){},this.currentNode=null,this.currentLevel=-1,this.openTags={},this.documentStarted=!1,this.documentCompleted=!1,this.root=null}return S.prototype.createChildNode=function(d){var D,L,M,F,k,q,j,X;switch(d.type){case s.CData:this.cdata(d.value);break;case s.Comment:this.comment(d.value);break;case s.Element:M={},j=d.attribs;for(L in j)N.call(j,L)&&(D=j[L],M[L]=D.value);this.node(d.name,M);break;case s.Dummy:this.dummy();break;case s.Raw:this.raw(d.value);break;case s.Text:this.text(d.value);break;case s.ProcessingInstruction:this.instruction(d.target,d.value);break;default:throw new Error("This XML node type is not supported in a JS object: "+d.constructor.name)}for(X=d.children,k=0,q=X.length;k<q;k++)F=X[k],this.createChildNode(F),F.type===s.Element&&this.up();return this},S.prototype.dummy=function(){return this},S.prototype.node=function(d,D,L){var M;if(d==null)throw new Error("Missing node name.");if(this.root&&this.currentLevel===-1)throw new Error("Document can only have one root node. "+this.debugInfo(d));return this.openCurrent(),d=u(d),D==null&&(D={}),D=u(D),p(D)||(M=[D,L],L=M[0],D=M[1]),this.currentNode=new w(this,d,D),this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,L!=null&&this.text(L),this},S.prototype.element=function(d,D,L){var M,F,k,q,j,X;if(this.currentNode&&this.currentNode.type===s.DocType)this.dtdElement.apply(this,arguments);else if(Array.isArray(d)||p(d)||c(d))for(q=this.options.noValidation,this.options.noValidation=!0,X=new y(this.options).element("TEMP_ROOT"),X.element(d),this.options.noValidation=q,j=X.children,F=0,k=j.length;F<k;F++)M=j[F],this.createChildNode(M),M.type===s.Element&&this.up();else this.node(d,D,L);return this},S.prototype.attribute=function(d,D){var L,M;if(!this.currentNode||this.currentNode.children)throw new Error("att() can only be used immediately after an ele() call in callback mode. "+this.debugInfo(d));if(d!=null&&(d=u(d)),p(d))for(L in d)N.call(d,L)&&(M=d[L],this.attribute(L,M));else c(D)&&(D=D.apply()),this.options.keepNullAttributes&&D==null?this.currentNode.attribs[d]=new t(this,d,""):D!=null&&(this.currentNode.attribs[d]=new t(this,d,D));return this},S.prototype.text=function(d){var D;return this.openCurrent(),D=new b(this,d),this.onData(this.writer.text(D,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},S.prototype.cdata=function(d){var D;return this.openCurrent(),D=new r(this,d),this.onData(this.writer.cdata(D,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},S.prototype.comment=function(d){var D;return this.openCurrent(),D=new o(this,d),this.onData(this.writer.comment(D,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},S.prototype.raw=function(d){var D;return this.openCurrent(),D=new E(this,d),this.onData(this.writer.raw(D,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},S.prototype.instruction=function(d,D){var L,M,F,k,q;if(this.openCurrent(),d!=null&&(d=u(d)),D!=null&&(D=u(D)),Array.isArray(d))for(L=0,k=d.length;L<k;L++)M=d[L],this.instruction(M);else if(p(d))for(M in d)N.call(d,M)&&(F=d[M],this.instruction(M,F));else c(D)&&(D=D.apply()),q=new h(this,d,D),this.onData(this.writer.processingInstruction(q,this.writerOptions,this.currentLevel+1),this.currentLevel+1);return this},S.prototype.declaration=function(d,D,L){var M;if(this.openCurrent(),this.documentStarted)throw new Error("declaration() must be the first node.");return M=new f(this,d,D,L),this.onData(this.writer.declaration(M,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},S.prototype.doctype=function(d,D,L){if(this.openCurrent(),d==null)throw new Error("Missing root node name.");if(this.root)throw new Error("dtd() must come before the root node.");return this.currentNode=new m(this,D,L),this.currentNode.rootNodeName=d,this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,this},S.prototype.dtdElement=function(d,D){var L;return this.openCurrent(),L=new i(this,d,D),this.onData(this.writer.dtdElement(L,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},S.prototype.attList=function(d,D,L,M,F){var k;return this.openCurrent(),k=new n(this,d,D,L,M,F),this.onData(this.writer.dtdAttList(k,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},S.prototype.entity=function(d,D){var L;return this.openCurrent(),L=new a(this,!1,d,D),this.onData(this.writer.dtdEntity(L,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},S.prototype.pEntity=function(d,D){var L;return this.openCurrent(),L=new a(this,!0,d,D),this.onData(this.writer.dtdEntity(L,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},S.prototype.notation=function(d,D){var L;return this.openCurrent(),L=new l(this,d,D),this.onData(this.writer.dtdNotation(L,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},S.prototype.up=function(){if(this.currentLevel<0)throw new Error("The document node has no parent.");return this.currentNode?(this.currentNode.children?this.closeNode(this.currentNode):this.openNode(this.currentNode),this.currentNode=null):this.closeNode(this.openTags[this.currentLevel]),delete this.openTags[this.currentLevel],this.currentLevel--,this},S.prototype.end=function(){for(;this.currentLevel>=0;)this.up();return this.onEnd()},S.prototype.openCurrent=function(){if(this.currentNode)return this.currentNode.children=!0,this.openNode(this.currentNode)},S.prototype.openNode=function(d){var D,L,M,F;if(!d.isOpen){if(!this.root&&this.currentLevel===0&&d.type===s.Element&&(this.root=d),L="",d.type===s.Element){this.writerOptions.state=e.OpenTag,L=this.writer.indent(d,this.writerOptions,this.currentLevel)+"<"+d.name,F=d.attribs;for(M in F)N.call(F,M)&&(D=F[M],L+=this.writer.attribute(D,this.writerOptions,this.currentLevel));L+=(d.children?">":"/>")+this.writer.endline(d,this.writerOptions,this.currentLevel),this.writerOptions.state=e.InsideTag}else this.writerOptions.state=e.OpenTag,L=this.writer.indent(d,this.writerOptions,this.currentLevel)+"<!DOCTYPE "+d.rootNodeName,d.pubID&&d.sysID?L+=' PUBLIC "'+d.pubID+'" "'+d.sysID+'"':d.sysID&&(L+=' SYSTEM "'+d.sysID+'"'),d.children?(L+=" [",this.writerOptions.state=e.InsideTag):(this.writerOptions.state=e.CloseTag,L+=">"),L+=this.writer.endline(d,this.writerOptions,this.currentLevel);return this.onData(L,this.currentLevel),d.isOpen=!0}},S.prototype.closeNode=function(d){var D;if(!d.isClosed)return D="",this.writerOptions.state=e.CloseTag,d.type===s.Element?D=this.writer.indent(d,this.writerOptions,this.currentLevel)+"</"+d.name+">"+this.writer.endline(d,this.writerOptions,this.currentLevel):D=this.writer.indent(d,this.writerOptions,this.currentLevel)+"]>"+this.writer.endline(d,this.writerOptions,this.currentLevel),this.writerOptions.state=e.None,this.onData(D,this.currentLevel),d.isClosed=!0},S.prototype.onData=function(d,D){return this.documentStarted=!0,this.onDataCallback(d,D+1)},S.prototype.onEnd=function(){return this.documentCompleted=!0,this.onEndCallback()},S.prototype.debugInfo=function(d){return d==null?"":"node: <"+d+">"},S.prototype.ele=function(){return this.element.apply(this,arguments)},S.prototype.nod=function(d,D,L){return this.node(d,D,L)},S.prototype.txt=function(d){return this.text(d)},S.prototype.dat=function(d){return this.cdata(d)},S.prototype.com=function(d){return this.comment(d)},S.prototype.ins=function(d,D){return this.instruction(d,D)},S.prototype.dec=function(d,D,L){return this.declaration(d,D,L)},S.prototype.dtd=function(d,D,L){return this.doctype(d,D,L)},S.prototype.e=function(d,D,L){return this.element(d,D,L)},S.prototype.n=function(d,D,L){return this.node(d,D,L)},S.prototype.t=function(d){return this.text(d)},S.prototype.d=function(d){return this.cdata(d)},S.prototype.c=function(d){return this.comment(d)},S.prototype.r=function(d){return this.raw(d)},S.prototype.i=function(d,D){return this.instruction(d,D)},S.prototype.att=function(){return this.currentNode&&this.currentNode.type===s.DocType?this.attList.apply(this,arguments):this.attribute.apply(this,arguments)},S.prototype.a=function(){return this.currentNode&&this.currentNode.type===s.DocType?this.attList.apply(this,arguments):this.attribute.apply(this,arguments)},S.prototype.ent=function(d,D){return this.entity(d,D)},S.prototype.pent=function(d,D){return this.pEntity(d,D)},S.prototype.not=function(d,D){return this.notation(d,D)},S}()}).call(U)),Rt.exports}var Ft={exports:{}},mn;function No(){return mn||(mn=1,(function(){var s,e,t,r=function(n,i){for(var a in i)o.call(i,a)&&(n[a]=i[a]);function l(){this.constructor=n}return l.prototype=i.prototype,n.prototype=new l,n.__super__=i.prototype,n},o={}.hasOwnProperty;s=K(),t=$n(),e=Xe(),Ft.exports=function(n){r(i,n);function i(a,l){this.stream=a,i.__super__.constructor.call(this,l)}return i.prototype.endline=function(a,l,f){return a.isLastRootNode&&l.state===e.CloseTag?"":i.__super__.endline.call(this,a,l,f)},i.prototype.document=function(a,l){var f,m,y,w,h,E,g,_,b;for(g=a.children,m=y=0,h=g.length;y<h;m=++y)f=g[m],f.isLastRootNode=m===a.children.length-1;for(l=this.filterOptions(l),_=a.children,b=[],w=0,E=_.length;w<E;w++)f=_[w],b.push(this.writeChildNode(f,l,0));return b},i.prototype.attribute=function(a,l,f){return this.stream.write(i.__super__.attribute.call(this,a,l,f))},i.prototype.cdata=function(a,l,f){return this.stream.write(i.__super__.cdata.call(this,a,l,f))},i.prototype.comment=function(a,l,f){return this.stream.write(i.__super__.comment.call(this,a,l,f))},i.prototype.declaration=function(a,l,f){return this.stream.write(i.__super__.declaration.call(this,a,l,f))},i.prototype.docType=function(a,l,f){var m,y,w,h;if(f||(f=0),this.openNode(a,l,f),l.state=e.OpenTag,this.stream.write(this.indent(a,l,f)),this.stream.write("<!DOCTYPE "+a.root().name),a.pubID&&a.sysID?this.stream.write(' PUBLIC "'+a.pubID+'" "'+a.sysID+'"'):a.sysID&&this.stream.write(' SYSTEM "'+a.sysID+'"'),a.children.length>0){for(this.stream.write(" ["),this.stream.write(this.endline(a,l,f)),l.state=e.InsideTag,h=a.children,y=0,w=h.length;y<w;y++)m=h[y],this.writeChildNode(m,l,f+1);l.state=e.CloseTag,this.stream.write("]")}return l.state=e.CloseTag,this.stream.write(l.spaceBeforeSlash+">"),this.stream.write(this.endline(a,l,f)),l.state=e.None,this.closeNode(a,l,f)},i.prototype.element=function(a,l,f){var m,y,w,h,E,g,_,b,u;f||(f=0),this.openNode(a,l,f),l.state=e.OpenTag,this.stream.write(this.indent(a,l,f)+"<"+a.name),b=a.attribs;for(_ in b)o.call(b,_)&&(m=b[_],this.attribute(m,l,f));if(w=a.children.length,h=w===0?null:a.children[0],w===0||a.children.every(function(c){return(c.type===s.Text||c.type===s.Raw)&&c.value===""}))l.allowEmpty?(this.stream.write(">"),l.state=e.CloseTag,this.stream.write("</"+a.name+">")):(l.state=e.CloseTag,this.stream.write(l.spaceBeforeSlash+"/>"));else if(l.pretty&&w===1&&(h.type===s.Text||h.type===s.Raw)&&h.value!=null)this.stream.write(">"),l.state=e.InsideTag,l.suppressPrettyCount++,this.writeChildNode(h,l,f+1),l.suppressPrettyCount--,l.state=e.CloseTag,this.stream.write("</"+a.name+">");else{for(this.stream.write(">"+this.endline(a,l,f)),l.state=e.InsideTag,u=a.children,E=0,g=u.length;E<g;E++)y=u[E],this.writeChildNode(y,l,f+1);l.state=e.CloseTag,this.stream.write(this.indent(a,l,f)+"</"+a.name+">")}return this.stream.write(this.endline(a,l,f)),l.state=e.None,this.closeNode(a,l,f)},i.prototype.processingInstruction=function(a,l,f){return this.stream.write(i.__super__.processingInstruction.call(this,a,l,f))},i.prototype.raw=function(a,l,f){return this.stream.write(i.__super__.raw.call(this,a,l,f))},i.prototype.text=function(a,l,f){return this.stream.write(i.__super__.text.call(this,a,l,f))},i.prototype.dtdAttList=function(a,l,f){return this.stream.write(i.__super__.dtdAttList.call(this,a,l,f))},i.prototype.dtdElement=function(a,l,f){return this.stream.write(i.__super__.dtdElement.call(this,a,l,f))},i.prototype.dtdEntity=function(a,l,f){return this.stream.write(i.__super__.dtdEntity.call(this,a,l,f))},i.prototype.dtdNotation=function(a,l,f){return this.stream.write(i.__super__.dtdNotation.call(this,a,l,f))},i}(t)}).call(U)),Ft.exports}var yn;function So(){return yn||(yn=1,(function(){var s,e,t,r,o,n,i,a,l,f;f=de(),a=f.assign,l=f.isFunction,t=Bn(),r=jn(),o=Do(),i=ar(),n=No(),s=K(),e=Xe(),fe.create=function(m,y,w,h){var E,g;if(m==null)throw new Error("Root element needs a name.");return h=a({},y,w,h),E=new r(h),g=E.element(m),h.headless||(E.declaration(h),(h.pubID!=null||h.sysID!=null)&&E.dtd(h)),g},fe.begin=function(m,y,w){var h;return l(m)&&(h=[m,y],y=h[0],w=h[1],m={}),y?new o(m,y,w):new r(m)},fe.stringWriter=function(m){return new i(m)},fe.streamWriter=function(m,y){return new n(m,y)},fe.implementation=new t,fe.nodeType=s,fe.writerState=e}).call(U)),fe}var En;function Lo(){return En||(En=1,(function(){var s,e,t,r,o,n={}.hasOwnProperty;s=So(),e=Wt().defaults,r=function(i){return typeof i=="string"&&(i.indexOf("&")>=0||i.indexOf(">")>=0||i.indexOf("<")>=0)},o=function(i){return"<![CDATA["+t(i)+"]]>"},t=function(i){return i.replace("]]>","]]]]><![CDATA[>")},ot.Builder=function(){function i(a){var l,f,m;this.options={},f=e["0.2"];for(l in f)n.call(f,l)&&(m=f[l],this.options[l]=m);for(l in a)n.call(a,l)&&(m=a[l],this.options[l]=m)}return i.prototype.buildObject=function(a){var l,f,m,y,w;return l=this.options.attrkey,f=this.options.charkey,Object.keys(a).length===1&&this.options.rootName===e["0.2"].rootName?(w=Object.keys(a)[0],a=a[w]):w=this.options.rootName,m=function(h){return function(E,g){var _,b,u,c,p,v;if(typeof g!="object")h.options.cdata&&r(g)?E.raw(o(g)):E.txt(g);else if(Array.isArray(g)){for(c in g)if(n.call(g,c)){b=g[c];for(p in b)u=b[p],E=m(E.ele(p),u).up()}}else for(p in g)if(n.call(g,p))if(b=g[p],p===l){if(typeof b=="object")for(_ in b)v=b[_],E=E.att(_,v)}else if(p===f)h.options.cdata&&r(b)?E=E.raw(o(b)):E=E.txt(b);else if(Array.isArray(b))for(c in b)n.call(b,c)&&(u=b[c],typeof u=="string"?h.options.cdata&&r(u)?E=E.ele(p).raw(o(u)).up():E=E.ele(p,u).up():E=m(E.ele(p),u).up());else typeof b=="object"?E=m(E.ele(p),b).up():typeof b=="string"&&h.options.cdata&&r(b)?E=E.ele(p).raw(o(b)).up():(b==null&&(b=""),E=E.ele(p,b.toString()).up());return E}}(this),y=s.create(w,this.options.xmldec,this.options.doctype,{headless:this.options.headless,allowSurrogateChars:this.options.allowSurrogateChars}),m(y,a).end(this.options.renderOpts)},i}()}).call(U)),ot}var Bt={},Ut={},bn;function Po(){return bn||(bn=1,function(s){(function(e){e.parser=function(C,T){return new r(C,T)},e.SAXParser=r,e.SAXStream=m,e.createStream=f,e.MAX_BUFFER_LENGTH=64*1024;var t=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];e.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"];function r(C,T){if(!(this instanceof r))return new r(C,T);var P=this;n(P),P.q=P.c="",P.bufferCheckPosition=e.MAX_BUFFER_LENGTH,P.opt=T||{},P.opt.lowercase=P.opt.lowercase||P.opt.lowercasetags,P.looseCase=P.opt.lowercase?"toLowerCase":"toUpperCase",P.tags=[],P.closed=P.closedRoot=P.sawRoot=!1,P.tag=P.error=null,P.strict=!!C,P.noscript=!!(C||P.opt.noscript),P.state=d.BEGIN,P.strictEntities=P.opt.strictEntities,P.ENTITIES=P.strictEntities?Object.create(e.XML_ENTITIES):Object.create(e.ENTITIES),P.attribList=[],P.opt.xmlns&&(P.ns=Object.create(g)),P.opt.unquotedAttributeValues===void 0&&(P.opt.unquotedAttributeValues=!C),P.trackPosition=P.opt.position!==!1,P.trackPosition&&(P.position=P.line=P.column=0),L(P,"onready")}Object.create||(Object.create=function(C){function T(){}T.prototype=C;var P=new T;return P}),Object.keys||(Object.keys=function(C){var T=[];for(var P in C)C.hasOwnProperty(P)&&T.push(P);return T});function o(C){for(var T=Math.max(e.MAX_BUFFER_LENGTH,10),P=0,x=0,z=t.length;x<z;x++){var J=C[t[x]].length;if(J>T)switch(t[x]){case"textNode":F(C);break;case"cdata":M(C,"oncdata",C.cdata),C.cdata="";break;case"script":M(C,"onscript",C.script),C.script="";break;default:q(C,"Max buffer length exceeded: "+t[x])}P=Math.max(P,J)}var Q=e.MAX_BUFFER_LENGTH-P;C.bufferCheckPosition=Q+C.position}function n(C){for(var T=0,P=t.length;T<P;T++)C[t[T]]=""}function i(C){F(C),C.cdata!==""&&(M(C,"oncdata",C.cdata),C.cdata=""),C.script!==""&&(M(C,"onscript",C.script),C.script="")}r.prototype={end:function(){j(this)},write:Vn,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){i(this)}};var a;try{a=require("stream").Stream}catch{a=function(){}}a||(a=function(){});var l=e.EVENTS.filter(function(C){return C!=="error"&&C!=="end"});function f(C,T){return new m(C,T)}function m(C,T){if(!(this instanceof m))return new m(C,T);a.apply(this),this._parser=new r(C,T),this.writable=!0,this.readable=!0;var P=this;this._parser.onend=function(){P.emit("end")},this._parser.onerror=function(x){P.emit("error",x),P._parser.error=null},this._decoder=null,l.forEach(function(x){Object.defineProperty(P,"on"+x,{get:function(){return P._parser["on"+x]},set:function(z){if(!z)return P.removeAllListeners(x),P._parser["on"+x]=z,z;P.on(x,z)},enumerable:!0,configurable:!1})})}m.prototype=Object.create(a.prototype,{constructor:{value:m}}),m.prototype.write=function(C){if(typeof Buffer=="function"&&typeof Buffer.isBuffer=="function"&&Buffer.isBuffer(C)){if(!this._decoder){var T=Nn().StringDecoder;this._decoder=new T("utf8")}C=this._decoder.write(C)}return this._parser.write(C.toString()),this.emit("data",C),!0},m.prototype.end=function(C){return C&&C.length&&this.write(C),this._parser.end(),!0},m.prototype.on=function(C,T){var P=this;return!P._parser["on"+C]&&l.indexOf(C)!==-1&&(P._parser["on"+C]=function(){var x=arguments.length===1?[arguments[0]]:Array.apply(null,arguments);x.splice(0,0,C),P.emit.apply(P,x)}),a.prototype.on.call(P,C,T)};var y="[CDATA[",w="DOCTYPE",h="http://www.w3.org/XML/1998/namespace",E="http://www.w3.org/2000/xmlns/",g={xml:h,xmlns:E},_=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,b=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,u=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,c=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function p(C){return C===" "||C===`
`||C==="\r"||C==="	"}function v(C){return C==='"'||C==="'"}function I(C){return C===">"||p(C)}function N(C,T){return C.test(T)}function S(C,T){return!N(C,T)}var d=0;e.STATE={BEGIN:d++,BEGIN_WHITESPACE:d++,TEXT:d++,TEXT_ENTITY:d++,OPEN_WAKA:d++,SGML_DECL:d++,SGML_DECL_QUOTED:d++,DOCTYPE:d++,DOCTYPE_QUOTED:d++,DOCTYPE_DTD:d++,DOCTYPE_DTD_QUOTED:d++,COMMENT_STARTING:d++,COMMENT:d++,COMMENT_ENDING:d++,COMMENT_ENDED:d++,CDATA:d++,CDATA_ENDING:d++,CDATA_ENDING_2:d++,PROC_INST:d++,PROC_INST_BODY:d++,PROC_INST_ENDING:d++,OPEN_TAG:d++,OPEN_TAG_SLASH:d++,ATTRIB:d++,ATTRIB_NAME:d++,ATTRIB_NAME_SAW_WHITE:d++,ATTRIB_VALUE:d++,ATTRIB_VALUE_QUOTED:d++,ATTRIB_VALUE_CLOSED:d++,ATTRIB_VALUE_UNQUOTED:d++,ATTRIB_VALUE_ENTITY_Q:d++,ATTRIB_VALUE_ENTITY_U:d++,CLOSE_TAG:d++,CLOSE_TAG_SAW_WHITE:d++,SCRIPT:d++,SCRIPT_ENDING:d++},e.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},e.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(e.ENTITIES).forEach(function(C){var T=e.ENTITIES[C],P=typeof T=="number"?String.fromCharCode(T):T;e.ENTITIES[C]=P});for(var D in e.STATE)e.STATE[e.STATE[D]]=D;d=e.STATE;function L(C,T,P){C[T]&&C[T](P)}function M(C,T,P){C.textNode&&F(C),L(C,T,P)}function F(C){C.textNode=k(C.opt,C.textNode),C.textNode&&L(C,"ontext",C.textNode),C.textNode=""}function k(C,T){return C.trim&&(T=T.trim()),C.normalize&&(T=T.replace(/\s+/g," ")),T}function q(C,T){return F(C),C.trackPosition&&(T+=`
Line: `+C.line+`
Column: `+C.column+`
Char: `+C.c),T=new Error(T),C.error=T,L(C,"onerror",T),C}function j(C){return C.sawRoot&&!C.closedRoot&&X(C,"Unclosed root tag"),C.state!==d.BEGIN&&C.state!==d.BEGIN_WHITESPACE&&C.state!==d.TEXT&&q(C,"Unexpected end"),F(C),C.c="",C.closed=!0,L(C,"onend"),r.call(C,C.strict,C.opt),C}function X(C,T){if(typeof C!="object"||!(C instanceof r))throw new Error("bad call to strictFail");C.strict&&q(C,T)}function Hn(C){C.strict||(C.tagName=C.tagName[C.looseCase]());var T=C.tags[C.tags.length-1]||C,P=C.tag={name:C.tagName,attributes:{}};C.opt.xmlns&&(P.ns=T.ns),C.attribList.length=0,M(C,"onopentagstart",P)}function $e(C,T){var P=C.indexOf(":"),x=P<0?["",C]:C.split(":"),z=x[0],J=x[1];return T&&C==="xmlns"&&(z="xmlns",J=""),{prefix:z,local:J}}function je(C){if(C.strict||(C.attribName=C.attribName[C.looseCase]()),C.attribList.indexOf(C.attribName)!==-1||C.tag.attributes.hasOwnProperty(C.attribName)){C.attribName=C.attribValue="";return}if(C.opt.xmlns){var T=$e(C.attribName,!0),P=T.prefix,x=T.local;if(P==="xmlns")if(x==="xml"&&C.attribValue!==h)X(C,"xml: prefix must be bound to "+h+`
Actual: `+C.attribValue);else if(x==="xmlns"&&C.attribValue!==E)X(C,"xmlns: prefix must be bound to "+E+`
Actual: `+C.attribValue);else{var z=C.tag,J=C.tags[C.tags.length-1]||C;z.ns===J.ns&&(z.ns=Object.create(J.ns)),z.ns[x]=C.attribValue}C.attribList.push([C.attribName,C.attribValue])}else C.tag.attributes[C.attribName]=C.attribValue,M(C,"onattribute",{name:C.attribName,value:C.attribValue});C.attribName=C.attribValue=""}function me(C,T){if(C.opt.xmlns){var P=C.tag,x=$e(C.tagName);P.prefix=x.prefix,P.local=x.local,P.uri=P.ns[x.prefix]||"",P.prefix&&!P.uri&&(X(C,"Unbound namespace prefix: "+JSON.stringify(C.tagName)),P.uri=x.prefix);var z=C.tags[C.tags.length-1]||C;P.ns&&z.ns!==P.ns&&Object.keys(P.ns).forEach(function(fr){M(C,"onopennamespace",{prefix:fr,uri:P.ns[fr]})});for(var J=0,Q=C.attribList.length;J<Q;J++){var ie=C.attribList[J],oe=ie[0],be=ie[1],te=$e(oe,!0),le=te.prefix,Wn=te.local,ur=le===""?"":P.ns[le]||"",He={name:oe,value:be,prefix:le,local:Wn,uri:ur};le&&le!=="xmlns"&&!ur&&(X(C,"Unbound namespace prefix: "+JSON.stringify(le)),He.uri=le),C.tag.attributes[oe]=He,M(C,"onattribute",He)}C.attribList.length=0}C.tag.isSelfClosing=!!T,C.sawRoot=!0,C.tags.push(C.tag),M(C,"onopentag",C.tag),T||(!C.noscript&&C.tagName.toLowerCase()==="script"?C.state=d.SCRIPT:C.state=d.TEXT,C.tag=null,C.tagName=""),C.attribName=C.attribValue="",C.attribList.length=0}function ze(C){if(!C.tagName){X(C,"Weird empty close tag."),C.textNode+="</>",C.state=d.TEXT;return}if(C.script){if(C.tagName!=="script"){C.script+="</"+C.tagName+">",C.tagName="",C.state=d.SCRIPT;return}M(C,"onscript",C.script),C.script=""}var T=C.tags.length,P=C.tagName;C.strict||(P=P[C.looseCase]());for(var x=P;T--;){var z=C.tags[T];if(z.name!==x)X(C,"Unexpected close tag");else break}if(T<0){X(C,"Unmatched closing tag: "+C.tagName),C.textNode+="</"+C.tagName+">",C.state=d.TEXT;return}C.tagName=P;for(var J=C.tags.length;J-- >T;){var Q=C.tag=C.tags.pop();C.tagName=C.tag.name,M(C,"onclosetag",C.tagName);var ie={};for(var oe in Q.ns)ie[oe]=Q.ns[oe];var be=C.tags[C.tags.length-1]||C;C.opt.xmlns&&Q.ns!==be.ns&&Object.keys(Q.ns).forEach(function(te){var le=Q.ns[te];M(C,"onclosenamespace",{prefix:te,uri:le})})}T===0&&(C.closedRoot=!0),C.tagName=C.attribValue=C.attribName="",C.attribList.length=0,C.state=d.TEXT}function qn(C){var T=C.entity,P=T.toLowerCase(),x,z="";return C.ENTITIES[T]?C.ENTITIES[T]:C.ENTITIES[P]?C.ENTITIES[P]:(T=P,T.charAt(0)==="#"&&(T.charAt(1)==="x"?(T=T.slice(2),x=parseInt(T,16),z=x.toString(16)):(T=T.slice(1),x=parseInt(T,10),z=x.toString(10))),T=T.replace(/^0+/,""),isNaN(x)||z.toLowerCase()!==T?(X(C,"Invalid character entity"),"&"+C.entity+";"):String.fromCodePoint(x))}function cr(C,T){T==="<"?(C.state=d.OPEN_WAKA,C.startTagPosition=C.position):p(T)||(X(C,"Non-whitespace before first tag."),C.textNode=T,C.state=d.TEXT)}function lr(C,T){var P="";return T<C.length&&(P=C.charAt(T)),P}function Vn(C){var T=this;if(this.error)throw this.error;if(T.closed)return q(T,"Cannot write after close. Assign an onready handler.");if(C===null)return j(T);typeof C=="object"&&(C=C.toString());for(var P=0,x="";x=lr(C,P++),T.c=x,!!x;)switch(T.trackPosition&&(T.position++,x===`
`?(T.line++,T.column=0):T.column++),T.state){case d.BEGIN:if(T.state=d.BEGIN_WHITESPACE,x==="\uFEFF")continue;cr(T,x);continue;case d.BEGIN_WHITESPACE:cr(T,x);continue;case d.TEXT:if(T.sawRoot&&!T.closedRoot){for(var z=P-1;x&&x!=="<"&&x!=="&";)x=lr(C,P++),x&&T.trackPosition&&(T.position++,x===`
`?(T.line++,T.column=0):T.column++);T.textNode+=C.substring(z,P-1)}x==="<"&&!(T.sawRoot&&T.closedRoot&&!T.strict)?(T.state=d.OPEN_WAKA,T.startTagPosition=T.position):(!p(x)&&(!T.sawRoot||T.closedRoot)&&X(T,"Text data outside of root node."),x==="&"?T.state=d.TEXT_ENTITY:T.textNode+=x);continue;case d.SCRIPT:x==="<"?T.state=d.SCRIPT_ENDING:T.script+=x;continue;case d.SCRIPT_ENDING:x==="/"?T.state=d.CLOSE_TAG:(T.script+="<"+x,T.state=d.SCRIPT);continue;case d.OPEN_WAKA:if(x==="!")T.state=d.SGML_DECL,T.sgmlDecl="";else if(!p(x))if(N(_,x))T.state=d.OPEN_TAG,T.tagName=x;else if(x==="/")T.state=d.CLOSE_TAG,T.tagName="";else if(x==="?")T.state=d.PROC_INST,T.procInstName=T.procInstBody="";else{if(X(T,"Unencoded <"),T.startTagPosition+1<T.position){var J=T.position-T.startTagPosition;x=new Array(J).join(" ")+x}T.textNode+="<"+x,T.state=d.TEXT}continue;case d.SGML_DECL:if(T.sgmlDecl+x==="--"){T.state=d.COMMENT,T.comment="",T.sgmlDecl="";continue}T.doctype&&T.doctype!==!0&&T.sgmlDecl?(T.state=d.DOCTYPE_DTD,T.doctype+="<!"+T.sgmlDecl+x,T.sgmlDecl=""):(T.sgmlDecl+x).toUpperCase()===y?(M(T,"onopencdata"),T.state=d.CDATA,T.sgmlDecl="",T.cdata=""):(T.sgmlDecl+x).toUpperCase()===w?(T.state=d.DOCTYPE,(T.doctype||T.sawRoot)&&X(T,"Inappropriately located doctype declaration"),T.doctype="",T.sgmlDecl=""):x===">"?(M(T,"onsgmldeclaration",T.sgmlDecl),T.sgmlDecl="",T.state=d.TEXT):(v(x)&&(T.state=d.SGML_DECL_QUOTED),T.sgmlDecl+=x);continue;case d.SGML_DECL_QUOTED:x===T.q&&(T.state=d.SGML_DECL,T.q=""),T.sgmlDecl+=x;continue;case d.DOCTYPE:x===">"?(T.state=d.TEXT,M(T,"ondoctype",T.doctype),T.doctype=!0):(T.doctype+=x,x==="["?T.state=d.DOCTYPE_DTD:v(x)&&(T.state=d.DOCTYPE_QUOTED,T.q=x));continue;case d.DOCTYPE_QUOTED:T.doctype+=x,x===T.q&&(T.q="",T.state=d.DOCTYPE);continue;case d.DOCTYPE_DTD:x==="]"?(T.doctype+=x,T.state=d.DOCTYPE):x==="<"?(T.state=d.OPEN_WAKA,T.startTagPosition=T.position):v(x)?(T.doctype+=x,T.state=d.DOCTYPE_DTD_QUOTED,T.q=x):T.doctype+=x;continue;case d.DOCTYPE_DTD_QUOTED:T.doctype+=x,x===T.q&&(T.state=d.DOCTYPE_DTD,T.q="");continue;case d.COMMENT:x==="-"?T.state=d.COMMENT_ENDING:T.comment+=x;continue;case d.COMMENT_ENDING:x==="-"?(T.state=d.COMMENT_ENDED,T.comment=k(T.opt,T.comment),T.comment&&M(T,"oncomment",T.comment),T.comment=""):(T.comment+="-"+x,T.state=d.COMMENT);continue;case d.COMMENT_ENDED:x!==">"?(X(T,"Malformed comment"),T.comment+="--"+x,T.state=d.COMMENT):T.doctype&&T.doctype!==!0?T.state=d.DOCTYPE_DTD:T.state=d.TEXT;continue;case d.CDATA:x==="]"?T.state=d.CDATA_ENDING:T.cdata+=x;continue;case d.CDATA_ENDING:x==="]"?T.state=d.CDATA_ENDING_2:(T.cdata+="]"+x,T.state=d.CDATA);continue;case d.CDATA_ENDING_2:x===">"?(T.cdata&&M(T,"oncdata",T.cdata),M(T,"onclosecdata"),T.cdata="",T.state=d.TEXT):x==="]"?T.cdata+="]":(T.cdata+="]]"+x,T.state=d.CDATA);continue;case d.PROC_INST:x==="?"?T.state=d.PROC_INST_ENDING:p(x)?T.state=d.PROC_INST_BODY:T.procInstName+=x;continue;case d.PROC_INST_BODY:if(!T.procInstBody&&p(x))continue;x==="?"?T.state=d.PROC_INST_ENDING:T.procInstBody+=x;continue;case d.PROC_INST_ENDING:x===">"?(M(T,"onprocessinginstruction",{name:T.procInstName,body:T.procInstBody}),T.procInstName=T.procInstBody="",T.state=d.TEXT):(T.procInstBody+="?"+x,T.state=d.PROC_INST_BODY);continue;case d.OPEN_TAG:N(b,x)?T.tagName+=x:(Hn(T),x===">"?me(T):x==="/"?T.state=d.OPEN_TAG_SLASH:(p(x)||X(T,"Invalid character in tag name"),T.state=d.ATTRIB));continue;case d.OPEN_TAG_SLASH:x===">"?(me(T,!0),ze(T)):(X(T,"Forward-slash in opening tag not followed by >"),T.state=d.ATTRIB);continue;case d.ATTRIB:if(p(x))continue;x===">"?me(T):x==="/"?T.state=d.OPEN_TAG_SLASH:N(_,x)?(T.attribName=x,T.attribValue="",T.state=d.ATTRIB_NAME):X(T,"Invalid attribute name");continue;case d.ATTRIB_NAME:x==="="?T.state=d.ATTRIB_VALUE:x===">"?(X(T,"Attribute without value"),T.attribValue=T.attribName,je(T),me(T)):p(x)?T.state=d.ATTRIB_NAME_SAW_WHITE:N(b,x)?T.attribName+=x:X(T,"Invalid attribute name");continue;case d.ATTRIB_NAME_SAW_WHITE:if(x==="=")T.state=d.ATTRIB_VALUE;else{if(p(x))continue;X(T,"Attribute without value"),T.tag.attributes[T.attribName]="",T.attribValue="",M(T,"onattribute",{name:T.attribName,value:""}),T.attribName="",x===">"?me(T):N(_,x)?(T.attribName=x,T.state=d.ATTRIB_NAME):(X(T,"Invalid attribute name"),T.state=d.ATTRIB)}continue;case d.ATTRIB_VALUE:if(p(x))continue;v(x)?(T.q=x,T.state=d.ATTRIB_VALUE_QUOTED):(T.opt.unquotedAttributeValues||q(T,"Unquoted attribute value"),T.state=d.ATTRIB_VALUE_UNQUOTED,T.attribValue=x);continue;case d.ATTRIB_VALUE_QUOTED:if(x!==T.q){x==="&"?T.state=d.ATTRIB_VALUE_ENTITY_Q:T.attribValue+=x;continue}je(T),T.q="",T.state=d.ATTRIB_VALUE_CLOSED;continue;case d.ATTRIB_VALUE_CLOSED:p(x)?T.state=d.ATTRIB:x===">"?me(T):x==="/"?T.state=d.OPEN_TAG_SLASH:N(_,x)?(X(T,"No whitespace between attributes"),T.attribName=x,T.attribValue="",T.state=d.ATTRIB_NAME):X(T,"Invalid attribute name");continue;case d.ATTRIB_VALUE_UNQUOTED:if(!I(x)){x==="&"?T.state=d.ATTRIB_VALUE_ENTITY_U:T.attribValue+=x;continue}je(T),x===">"?me(T):T.state=d.ATTRIB;continue;case d.CLOSE_TAG:if(T.tagName)x===">"?ze(T):N(b,x)?T.tagName+=x:T.script?(T.script+="</"+T.tagName,T.tagName="",T.state=d.SCRIPT):(p(x)||X(T,"Invalid tagname in closing tag"),T.state=d.CLOSE_TAG_SAW_WHITE);else{if(p(x))continue;S(_,x)?T.script?(T.script+="</"+x,T.state=d.SCRIPT):X(T,"Invalid tagname in closing tag."):T.tagName=x}continue;case d.CLOSE_TAG_SAW_WHITE:if(p(x))continue;x===">"?ze(T):X(T,"Invalid characters in closing tag");continue;case d.TEXT_ENTITY:case d.ATTRIB_VALUE_ENTITY_Q:case d.ATTRIB_VALUE_ENTITY_U:var Q,ie;switch(T.state){case d.TEXT_ENTITY:Q=d.TEXT,ie="textNode";break;case d.ATTRIB_VALUE_ENTITY_Q:Q=d.ATTRIB_VALUE_QUOTED,ie="attribValue";break;case d.ATTRIB_VALUE_ENTITY_U:Q=d.ATTRIB_VALUE_UNQUOTED,ie="attribValue";break}if(x===";"){var oe=qn(T);T.opt.unparsedEntities&&!Object.values(e.XML_ENTITIES).includes(oe)?(T.entity="",T.state=Q,T.write(oe)):(T[ie]+=oe,T.entity="",T.state=Q)}else N(T.entity.length?c:u,x)?T.entity+=x:(X(T,"Invalid character in entity name"),T[ie]+="&"+T.entity+x,T.entity="",T.state=Q);continue;default:throw new Error(T,"Unknown state: "+T.state)}return T.position>=T.bufferCheckPosition&&o(T),T}/*! http://mths.be/fromcodepoint v0.1.0 by @mathias */String.fromCodePoint||function(){var C=String.fromCharCode,T=Math.floor,P=function(){var x=16384,z=[],J,Q,ie=-1,oe=arguments.length;if(!oe)return"";for(var be="";++ie<oe;){var te=Number(arguments[ie]);if(!isFinite(te)||te<0||te>1114111||T(te)!==te)throw RangeError("Invalid code point: "+te);te<=65535?z.push(te):(te-=65536,J=(te>>10)+55296,Q=te%1024+56320,z.push(J,Q)),(ie+1===oe||z.length>x)&&(be+=C.apply(null,z),z.length=0)}return be};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:P,configurable:!0,writable:!0}):String.fromCodePoint=P}()})(s)}(Ut)),Ut}var kt={},wn;function Oo(){return wn||(wn=1,(function(){kt.stripBOM=function(s){return s[0]==="\uFEFF"?s.substring(1):s}}).call(U)),kt}var Ee={},Tn;function zn(){return Tn||(Tn=1,(function(){var s;s=new RegExp(/(?!xmlns)^.*:/),Ee.normalize=function(e){return e.toLowerCase()},Ee.firstCharLowerCase=function(e){return e.charAt(0).toLowerCase()+e.slice(1)},Ee.stripPrefix=function(e){return e.replace(s,"")},Ee.parseNumbers=function(e){return isNaN(e)||(e=e%1===0?parseInt(e,10):parseFloat(e)),e},Ee.parseBooleans=function(e){return/^(?:true|false)$/i.test(e)&&(e=e.toLowerCase()==="true"),e}}).call(U)),Ee}const Mo={},Ao=Object.freeze(Object.defineProperty({__proto__:null,default:Mo},Symbol.toStringTag,{value:"Module"})),Ro=ui(Ao);var _n;function Fo(){return _n||(_n=1,function(s){(function(){var e,t,r,o,n,i,a,l,f,m=function(h,E){return function(){return h.apply(E,arguments)}},y=function(h,E){for(var g in E)w.call(E,g)&&(h[g]=E[g]);function _(){this.constructor=h}return _.prototype=E.prototype,h.prototype=new _,h.__super__=E.prototype,h},w={}.hasOwnProperty;l=Po(),o=Kn,e=Oo(),a=zn(),f=Ro.setImmediate,t=Wt().defaults,n=function(h){return typeof h=="object"&&h!=null&&Object.keys(h).length===0},i=function(h,E,g){var _,b,u;for(_=0,b=h.length;_<b;_++)u=h[_],E=u(E,g);return E},r=function(h,E,g){var _;return _=Object.create(null),_.value=g,_.writable=!0,_.enumerable=!0,_.configurable=!0,Object.defineProperty(h,E,_)},s.Parser=function(h){y(E,h);function E(g){this.parseStringPromise=m(this.parseStringPromise,this),this.parseString=m(this.parseString,this),this.reset=m(this.reset,this),this.assignOrPush=m(this.assignOrPush,this),this.processAsync=m(this.processAsync,this);var _,b,u;if(!(this instanceof s.Parser))return new s.Parser(g);this.options={},b=t["0.2"];for(_ in b)w.call(b,_)&&(u=b[_],this.options[_]=u);for(_ in g)w.call(g,_)&&(u=g[_],this.options[_]=u);this.options.xmlns&&(this.options.xmlnskey=this.options.attrkey+"ns"),this.options.normalizeTags&&(this.options.tagNameProcessors||(this.options.tagNameProcessors=[]),this.options.tagNameProcessors.unshift(a.normalize)),this.reset()}return E.prototype.processAsync=function(){var g,_;try{return this.remaining.length<=this.options.chunkSize?(g=this.remaining,this.remaining="",this.saxParser=this.saxParser.write(g),this.saxParser.close()):(g=this.remaining.substr(0,this.options.chunkSize),this.remaining=this.remaining.substr(this.options.chunkSize,this.remaining.length),this.saxParser=this.saxParser.write(g),f(this.processAsync))}catch(b){if(_=b,!this.saxParser.errThrown)return this.saxParser.errThrown=!0,this.emit(_)}},E.prototype.assignOrPush=function(g,_,b){return _ in g?(g[_]instanceof Array||r(g,_,[g[_]]),g[_].push(b)):this.options.explicitArray?r(g,_,[b]):r(g,_,b)},E.prototype.reset=function(){var g,_,b,u;return this.removeAllListeners(),this.saxParser=l.parser(this.options.strict,{trim:!1,normalize:!1,xmlns:this.options.xmlns}),this.saxParser.errThrown=!1,this.saxParser.onerror=function(c){return function(p){if(c.saxParser.resume(),!c.saxParser.errThrown)return c.saxParser.errThrown=!0,c.emit("error",p)}}(this),this.saxParser.onend=function(c){return function(){if(!c.saxParser.ended)return c.saxParser.ended=!0,c.emit("end",c.resultObject)}}(this),this.saxParser.ended=!1,this.EXPLICIT_CHARKEY=this.options.explicitCharkey,this.resultObject=null,u=[],g=this.options.attrkey,_=this.options.charkey,this.saxParser.onopentag=function(c){return function(p){var v,I,N,S,d;if(N={},N[_]="",!c.options.ignoreAttrs){d=p.attributes;for(v in d)w.call(d,v)&&(!(g in N)&&!c.options.mergeAttrs&&(N[g]={}),I=c.options.attrValueProcessors?i(c.options.attrValueProcessors,p.attributes[v],v):p.attributes[v],S=c.options.attrNameProcessors?i(c.options.attrNameProcessors,v):v,c.options.mergeAttrs?c.assignOrPush(N,S,I):r(N[g],S,I))}return N["#name"]=c.options.tagNameProcessors?i(c.options.tagNameProcessors,p.name):p.name,c.options.xmlns&&(N[c.options.xmlnskey]={uri:p.uri,local:p.local}),u.push(N)}}(this),this.saxParser.onclosetag=function(c){return function(){var p,v,I,N,S,d,D,L,M,F;if(d=u.pop(),S=d["#name"],(!c.options.explicitChildren||!c.options.preserveChildrenOrder)&&delete d["#name"],d.cdata===!0&&(p=d.cdata,delete d.cdata),M=u[u.length-1],d[_].match(/^\s*$/)&&!p?(v=d[_],delete d[_]):(c.options.trim&&(d[_]=d[_].trim()),c.options.normalize&&(d[_]=d[_].replace(/\s{2,}/g," ").trim()),d[_]=c.options.valueProcessors?i(c.options.valueProcessors,d[_],S):d[_],Object.keys(d).length===1&&_ in d&&!c.EXPLICIT_CHARKEY&&(d=d[_])),n(d)&&(typeof c.options.emptyTag=="function"?d=c.options.emptyTag():d=c.options.emptyTag!==""?c.options.emptyTag:v),c.options.validator!=null&&(F="/"+function(){var k,q,j;for(j=[],k=0,q=u.length;k<q;k++)N=u[k],j.push(N["#name"]);return j}().concat(S).join("/"),function(){var k;try{return d=c.options.validator(F,M&&M[S],d)}catch(q){return k=q,c.emit("error",k)}}()),c.options.explicitChildren&&!c.options.mergeAttrs&&typeof d=="object"){if(!c.options.preserveChildrenOrder)N={},c.options.attrkey in d&&(N[c.options.attrkey]=d[c.options.attrkey],delete d[c.options.attrkey]),!c.options.charsAsChildren&&c.options.charkey in d&&(N[c.options.charkey]=d[c.options.charkey],delete d[c.options.charkey]),Object.getOwnPropertyNames(d).length>0&&(N[c.options.childkey]=d),d=N;else if(M){M[c.options.childkey]=M[c.options.childkey]||[],D={};for(I in d)w.call(d,I)&&r(D,I,d[I]);M[c.options.childkey].push(D),delete d["#name"],Object.keys(d).length===1&&_ in d&&!c.EXPLICIT_CHARKEY&&(d=d[_])}}return u.length>0?c.assignOrPush(M,S,d):(c.options.explicitRoot&&(L=d,d={},r(d,S,L)),c.resultObject=d,c.saxParser.ended=!0,c.emit("end",c.resultObject))}}(this),b=function(c){return function(p){var v,I;if(I=u[u.length-1],I)return I[_]+=p,c.options.explicitChildren&&c.options.preserveChildrenOrder&&c.options.charsAsChildren&&(c.options.includeWhiteChars||p.replace(/\\n/g,"").trim()!=="")&&(I[c.options.childkey]=I[c.options.childkey]||[],v={"#name":"__text__"},v[_]=p,c.options.normalize&&(v[_]=v[_].replace(/\s{2,}/g," ").trim()),I[c.options.childkey].push(v)),I}}(this),this.saxParser.ontext=b,this.saxParser.oncdata=function(c){return function(p){var v;if(v=b(p),v)return v.cdata=!0}}()},E.prototype.parseString=function(g,_){var b;_!=null&&typeof _=="function"&&(this.on("end",function(u){return this.reset(),_(null,u)}),this.on("error",function(u){return this.reset(),_(u)}));try{return g=g.toString(),g.trim()===""?(this.emit("end",null),!0):(g=e.stripBOM(g),this.options.async?(this.remaining=g,f(this.processAsync),this.saxParser):this.saxParser.write(g).close())}catch(u){if(b=u,this.saxParser.errThrown||this.saxParser.ended){if(this.saxParser.ended)throw b}else return this.emit("error",b),this.saxParser.errThrown=!0}},E.prototype.parseStringPromise=function(g){return new Promise(function(_){return function(b,u){return _.parseString(g,function(c,p){return c?u(c):b(p)})}}(this))},E}(o),s.parseString=function(h,E,g){var _,b,u;return g!=null?(typeof g=="function"&&(_=g),typeof E=="object"&&(b=E)):(typeof E=="function"&&(_=E),b={}),u=new s.Parser(b),u.parseString(h,_)},s.parseStringPromise=function(h,E){var g,_;return typeof E=="object"&&(g=E),_=new s.Parser(g),_.parseStringPromise(h)}}).call(U)}(Bt)),Bt}var jt;(function(){var s,e,t,r=function(n,i){for(var a in i)o.call(i,a)&&(n[a]=i[a]);function l(){this.constructor=n}return l.prototype=i.prototype,n.prototype=new l,n.__super__=i.prototype,n},o={}.hasOwnProperty;e=Wt(),s=Lo(),t=Fo(),zn(),e.defaults,function(n){r(i,n);function i(a){this.message=a}return i}(Error),s.Builder,t.Parser,t.parseString,jt=t.parseStringPromise}).call(U);class zt{constructor(e={}){R(this,"zip",null);R(this,"config");R(this,"isLoaded",!1);R(this,"filePath","");R(this,"opfPath","");R(this,"opfDir","");R(this,"chapters",[]);this.config={enableImageExtraction:!0,enableStyleExtraction:!0,maxContentLength:5e4,extractFullContent:!1,enableCFI:!0,timeout:3e4,...e}}async parseEpubBasic(e){try{console.log(`EpubParser: 开始简化解析EPUB文件 ${e}`),this.filePath=e;const t=new Promise((n,i)=>{setTimeout(()=>i(new Error("解析超时")),this.config.timeout)}),r=this.performSimpleParse(e),o=await Promise.race([r,t]);return console.log(`EpubParser: 简化解析完成，书名: ${o.bookInfo.title}`),o}catch(t){throw console.error("EpubParser: 简化解析失败:",t),new Error(`解析EPUB文件失败: ${t.message}`)}}async parseEpub(e){return this.parseEpubBasic(e)}async performSimpleParse(e){console.log("EpubParser: 开始ZIP解析"),this.zip=new To(e),this.filePath=e,console.log("EpubParser: 解析容器信息");const t=this.zip.readAsText("META-INF/container.xml"),r=await jt(t);this.opfPath=r.container.rootfiles[0].rootfile[0].$["full-path"],this.opfDir=B.dirname(this.opfPath),console.log(`EpubParser: OPF文件路径: ${this.opfPath}`);const o=this.zip.readAsText(this.opfPath),n=await jt(o);console.log("EpubParser: 提取元数据");const i=n.package.metadata[0],a={title:this.getMetadataValue(i,"dc:title")||"未知标题",author:this.getMetadataValue(i,"dc:creator")||"未知作者",publisher:this.getMetadataValue(i,"dc:publisher")||"未知出版社",language:this.getMetadataValue(i,"dc:language")||"zh",identifier:this.getMetadataValue(i,"dc:identifier")||"",description:this.getMetadataValue(i,"dc:description")||"",publishDate:this.getMetadataValue(i,"dc:date")||"",rights:this.getMetadataValue(i,"dc:rights")||"",totalChapters:n.package.spine[0].itemref.length,totalWords:0,estimatedReadingTime:Math.ceil(n.package.spine[0].itemref.length*10),lastModified:new Date,isFullyLoaded:!1};console.log("EpubParser: 创建章节列表"),this.chapters=n.package.spine[0].itemref.map((f,m)=>{const y=n.package.manifest[0].item.find(w=>w.$.id===f.$.idref);return{id:f.$.idref,title:`第${m+1}章`,content:"",order:m,level:1,wordCount:0,href:(y==null?void 0:y.$.href)||"",mediaType:(y==null?void 0:y.$["media-type"])||"application/xhtml+xml",hasImages:!1,images:[],styles:[]}});const l=[{id:"root",title:a.title,href:"",level:0,children:this.chapters.map((f,m)=>({id:f.id,title:f.title,href:f.href,level:1,chapterIndex:m,children:[]}))}];return this.isLoaded=!0,console.log(`EpubParser: 简化解析完成，章节数: ${this.chapters.length}`),{bookInfo:a,chapters:this.chapters,toc:l,navigation:{landmarks:[],pageList:[]}}}getMetadataValue(e,t){const r=e[t];return Array.isArray(r)&&r.length>0?typeof r[0]=="string"?r[0]:r[0]._||"":""}async loadChapterContent(e){if(!this.zip)throw new Error("EPUB文件未加载");try{const t=await this.findChapterById(e);if(!t)throw new Error(`章节 ${e} 未找到`);const r=this.opfDir?`${this.opfDir}/${t.href}`:t.href;console.log(`EpubParser: 加载章节内容 ${e}, 路径: ${r}`);const o=this.zip.readAsText(r);if(console.log(`EpubParser: 原始内容长度: ${o.length}`),o.length===0){console.warn(`EpubParser: 章节文件为空或路径错误: ${r}`);const i=this.zip.getEntries();return console.log("ZIP文件中的XHTML文件:"),i.forEach(a=>{(a.entryName.includes(".xhtml")||a.entryName.includes(".html"))&&console.log(`  - ${a.entryName}`)}),"章节内容为空"}const n=this.extractTextFromHtml(o,e);return console.log(`EpubParser: 提取后内容长度: ${n.length}`),t.content=n,t.wordCount=n.length,console.log(`EpubParser: 章节内容加载完成，字数: ${t.wordCount}`),n}catch(t){throw console.error("EpubParser: 加载章节内容失败:",t),console.error(`EpubParser: 章节ID: ${e}`),console.error(`EpubParser: 章节href: ${chapter==null?void 0:chapter.href}`),console.error(`EpubParser: OPF目录: ${this.opfDir}`),t}}extractTextFromHtml(e,t){try{console.log(`EpubParser: 开始提取HTML内容，原始长度: ${e.length}`);let r=e.replace(/<\?xml[^>]*\?>/gi,"").replace(/<!DOCTYPE[^>]*>/gi,"").replace(/<head[^>]*>[\s\S]*?<\/head>/gi,"").replace(/<script[^>]*>[\s\S]*?<\/script>/gi,"").replace(/<style[^>]*>[\s\S]*?<\/style>/gi,"").replace(/<h1[^>]*>(.*?)<\/h1>/gi,`

<h1>$1</h1>

`).replace(/<h2[^>]*>(.*?)<\/h2>/gi,`

<h2>$1</h2>

`).replace(/<h3[^>]*>(.*?)<\/h3>/gi,`

<h3>$1</h3>

`).replace(/<h4[^>]*>(.*?)<\/h4>/gi,`

<h4>$1</h4>

`).replace(/<h5[^>]*>(.*?)<\/h5>/gi,`

<h5>$1</h5>

`).replace(/<h6[^>]*>(.*?)<\/h6>/gi,`

<h6>$1</h6>

`).replace(/<p[^>]*>/gi,`

<p>`).replace(/<\/p>/gi,`</p>

`).replace(/<blockquote[^>]*>/gi,`

<blockquote>`).replace(/<\/blockquote>/gi,`</blockquote>

`).replace(/<div[^>]*>/gi,`
<div>`).replace(/<\/div>/gi,`</div>
`).replace(/<ul[^>]*>/gi,`
<ul>`).replace(/<\/ul>/gi,`</ul>
`).replace(/<ol[^>]*>/gi,`
<ol>`).replace(/<\/ol>/gi,`</ol>
`).replace(/<li[^>]*>/gi,`
<li>`).replace(/<\/li>/gi,"</li>").replace(/<br\s*\/?>/gi,"<br>").replace(/<strong[^>]*>(.*?)<\/strong>/gi,"<strong>$1</strong>").replace(/<b[^>]*>(.*?)<\/b>/gi,"<strong>$1</strong>").replace(/<em[^>]*>(.*?)<\/em>/gi,"<em>$1</em>").replace(/<i[^>]*>(.*?)<\/i>/gi,"<em>$1</em>").replace(/<img([^>]*?)src=["']([^"']+)["']([^>]*?)>/gi,(o,n,i,a)=>{const l=this.resolveImagePath(i,t);return`<img${n}src="${l}"${a}>`}).replace(/<\/?(?:html|body|head|title|meta|link|script|style|span|font|a)[^>]*>/gi,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/&mdash;/g,"—").replace(/&ndash;/g,"–").replace(/&hellip;/g,"…").replace(/&copy;/g,"©").replace(/&reg;/g,"®").replace(/\n{4,}/g,`


`).replace(/[ \t]+/g," ").replace(/^[ \t]+|[ \t]+$/gm,"").replace(/^\n+/,"").replace(/\n+$/,"").trim();return console.log(`EpubParser: HTML内容提取完成，提取后长度: ${r.length}`),r||"内容为空"}catch(r){return console.error("EpubParser: HTML内容提取失败:",r),e}}async findChapterById(e){return this.chapters.find(t=>t.id===e)||null}resolveImagePath(e,t){var r;try{if(e.startsWith("http")||e.startsWith("data:")||e.startsWith("/"))return e;let o=this.opfDir||"";if(t){const i=this.chapters.find(a=>a.id===t);if(i&&i.href){const a=this.opfDir?`${this.opfDir}/${i.href}`:i.href,l=a.lastIndexOf("/");l>=0&&(o=a.substring(0,l))}}console.log(`EpubParser: 解析图片路径 - src: ${e}, chapterId: ${t}, currentChapterDir: ${o}`);let n;if(e.startsWith("../")){let i=e.split("/"),a=0;for(;i[a]==="..";)a++;const l=i.slice(a).join("/");let f=o;for(let m=0;m<a&&f;m++){const y=f.lastIndexOf("/");y>=0?f=f.substring(0,y):f=""}n=f?`${f}/${l}`:l}else if(e.startsWith("./")){const i=e.substring(2);n=o?`${o}/${i}`:i}else n=o?`${o}/${e}`:e;if(console.log(`EpubParser: 解析结果 - imagePath: ${n}`),this.zip){const i=this.zip.getEntry(n);if(i){const a=i.getData(),l=((r=e.split(".").pop())==null?void 0:r.toLowerCase())||"png";let f="image/png";l==="jpg"||l==="jpeg"?f="image/jpeg":l==="svg"?f="image/svg+xml":l==="gif"?f="image/gif":l==="webp"&&(f="image/webp");const m=a.toString("base64"),y=`data:${f};base64,${m}`;return console.log(`EpubParser: 图片转换成功 ${e} -> base64 (${a.length} bytes)`),y}else console.warn(`EpubParser: 图片文件未找到: ${n}`)}return e}catch(o){return console.error(`EpubParser: 图片路径解析失败: ${e}`,o),e}}dispose(){this.zip=null,this.isLoaded=!1,this.filePath="",this.opfPath="",this.opfDir=""}destroy(){this.dispose()}}class Bo{constructor(e={}){R(this,"parser");R(this,"config");R(this,"parseResult",null);R(this,"isLoaded",!1);R(this,"filePath","");R(this,"eventListeners",new Map);R(this,"currentPosition",{chapterIndex:0,chapterProgress:0,overallProgress:0});this.config={enableImageLoading:!0,enableStyleProcessing:!0,maxChapterLength:1e5,enableToc:!0,enableSearch:!0,enableCFI:!0,preloadChapters:2,renderConfig:{},...e},this.parser=new zt({enableImageExtraction:this.config.enableImageLoading,enableStyleExtraction:this.config.enableStyleProcessing,maxContentLength:this.config.maxChapterLength,extractFullContent:!0,enableCFI:this.config.enableCFI})}async loadBook(e){try{return console.log(`EpubReader: 开始加载EPUB文件 ${e}`),this.filePath=e,this.parseResult=await this.parser.parseEpub(e),this.isLoaded=!0,console.log(`EpubReader: 成功加载EPUB，书名: ${this.parseResult.bookInfo.title}`),console.log(`EpubReader: 共${this.parseResult.totalChapters}章，预估阅读时间${this.parseResult.estimatedReadingTime}分钟`),this.emitEvent("book-loaded",this.parseResult),this.config.preloadChapters&&this.config.preloadChapters>0&&this.preloadChapters(0,this.config.preloadChapters),this.convertToBookContent()}catch(t){throw console.error("EpubReader: 加载书籍失败:",t),this.emitEvent("error",{error:t,context:"loadBook"}),new Error(`加载EPUB文件失败: ${t instanceof Error?t.message:"未知错误"}`)}}convertToBookContent(){if(!this.parseResult)throw new Error("书籍未加载");return{title:this.parseResult.bookInfo.title,author:this.parseResult.bookInfo.author,chapters:this.parseResult.chapters.map(e=>({id:e.id,title:e.title,content:e.content,order:e.order})),totalChapters:this.parseResult.totalChapters,currentChapter:this.currentPosition.chapterIndex,metadata:this.parseResult.bookInfo}}async getChapter(e){if(!this.isLoaded||!this.parseResult)throw new Error("书籍未加载");if(e<0||e>=this.parseResult.chapters.length)throw new Error(`章节索引超出范围: ${e}`);const t=this.parseResult.chapters[e];if(!t.content||t.content.trim()===""){console.log(`EpubReader: 按需加载章节内容 ${t.id}`);try{t.content=await this.parser.loadChapterContent(t.id),console.log(`EpubReader: 章节内容加载完成，字数: ${t.content.length}`)}catch(r){console.error("EpubReader: 加载章节内容失败:",r),t.content="章节内容加载失败"}}return this.currentPosition.chapterIndex=e,this.emitEvent("chapter-changed",{chapterIndex:e,chapter:t}),t}getToc(){if(!this.isLoaded||!this.parseResult)throw new Error("书籍未加载");return{toc:this.parseResult.toc,bookInfo:this.parseResult.bookInfo}}async search(e){if(!this.isLoaded||!this.parseResult)throw new Error("书籍未加载");if(!this.config.enableSearch)throw new Error("搜索功能未启用");try{const t=[],r=e.toLowerCase().trim();if(!r)return t;for(let o=0;o<this.parseResult.chapters.length;o++){const n=this.parseResult.chapters[o],i=n.content.toLowerCase();let a=0;for(;;){const l=i.indexOf(r,a);if(l===-1)break;const f=Math.max(0,l-50),m=Math.min(i.length,l+r.length+50),y=n.content.substring(f,m);t.push({text:r,context:y,position:{chapterIndex:o,chapterProgress:l/i.length,overallProgress:(o+l/i.length)/this.parseResult.totalChapters*100},chapterIndex:o,chapterTitle:n.title,range:{start:l,end:l+r.length}}),a=l+1}}return console.log(`EpubReader: 搜索"${e}"找到${t.length}个结果`),this.emitEvent("search-completed",{query:e,results:t}),t}catch(t){throw console.error("EpubReader: 搜索失败:",t),this.emitEvent("error",{error:t,context:"search"}),new Error(`搜索失败: ${t instanceof Error?t.message:"未知错误"}`)}}async goToChapter(e){if(!this.isLoaded||!this.parseResult)throw new Error("书籍未加载");if(e<0||e>=this.parseResult.chapters.length)throw new Error(`章节索引超出范围: ${e}`);this.currentPosition.chapterIndex=e,this.currentPosition.chapterProgress=0,this.currentPosition.overallProgress=e/this.parseResult.totalChapters*100,this.emitEvent("position-changed",this.currentPosition)}updatePosition(e){this.currentPosition={...this.currentPosition,...e},this.parseResult&&(this.currentPosition.overallProgress=(this.currentPosition.chapterIndex+this.currentPosition.chapterProgress)/this.parseResult.totalChapters*100),this.emitEvent("position-changed",this.currentPosition)}getCurrentPosition(){return{...this.currentPosition}}async preloadChapters(e,t){if(!this.parseResult)return;const r=Math.min(e+t,this.parseResult.chapters.length);for(let o=e;o<r;o++)try{console.log(`EpubReader: 预加载章节 ${o+1}`)}catch(n){console.warn(`EpubReader: 预加载章节${o}失败:`,n)}}addEventListener(e,t){this.eventListeners.has(e)||this.eventListeners.set(e,[]),this.eventListeners.get(e).push(t)}removeEventListener(e,t){const r=this.eventListeners.get(e);if(r){const o=r.indexOf(t);o>-1&&r.splice(o,1)}}emitEvent(e,t){const r=this.eventListeners.get(e);if(r){const o={type:e,data:t,timestamp:Date.now()};r.forEach(n=>{try{n(o)}catch(i){console.error("EpubReader: 事件监听器错误:",i)}})}}setParseResult(e){this.parseResult=e,this.isLoaded=!0}setParser(e){this.parser=e}getBookInfo(){var e;return((e=this.parseResult)==null?void 0:e.bookInfo)||null}getChapters(){var e;return((e=this.parseResult)==null?void 0:e.chapters)||[]}getStatus(){var e;return{isLoaded:this.isLoaded,filePath:this.filePath,currentChapter:this.currentPosition.chapterIndex,totalChapters:((e=this.parseResult)==null?void 0:e.totalChapters)||0,progress:this.currentPosition.overallProgress||0}}destroy(){this.parser.destroy(),this.parseResult=null,this.isLoaded=!1,this.filePath="",this.eventListeners.clear(),this.currentPosition={chapterIndex:0,chapterProgress:0,overallProgress:0}}}class Uo{constructor(){R(this,"readers",new Map);R(this,"creationProgress",new Map);console.log("EpubReaderIPCHandler: 初始化现代化EPUB阅读器IPC处理器")}registerHandlers(){O.ipcMain.handle("epub-reader:parse-epub",async(e,t)=>this.handleParseEpub(t)),O.ipcMain.handle("epub-reader:get-file-info",async(e,t)=>this.handleGetFileInfo(t)),O.ipcMain.handle("epub-reader:create",async(e,t,r,o)=>this.handleCreateReader(t,r,o)),O.ipcMain.handle("epub-reader:get-creation-progress",async(e,t)=>this.getCreationProgress(t)),O.ipcMain.handle("epub-reader:destroy",async(e,t)=>this.handleDestroyReader(t)),O.ipcMain.handle("epub-reader:get-chapter",async(e,t,r)=>this.handleGetChapter(t,r)),O.ipcMain.handle("epub-reader:get-toc",async(e,t)=>this.handleGetToc(t)),O.ipcMain.handle("epub-reader:search",async(e,t,r)=>this.handleSearch(t,r)),O.ipcMain.handle("epub-reader:navigate",async(e,t,r,o)=>this.handleNavigate(t,r,o)),O.ipcMain.handle("epub-reader:get-status",async(e,t)=>this.handleGetStatus(t)),O.ipcMain.handle("epub-reader:update-position",async(e,t,r)=>this.handleUpdatePosition(t,r)),console.log("EpubReaderIPCHandler: 所有IPC处理器已注册")}async handleParseEpub(e){try{console.log(`EpubReaderIPC: 开始解析EPUB文件 ${e}`);const t=this.validateFile(e);if(!t.isValid)throw new Error(t.error);const o=await new zt().parseEpubBasic(e);return console.log(`EpubReaderIPC: 解析完成，书名: ${o.bookInfo.title}`),{success:!0,result:o}}catch(t){return console.error("EpubReaderIPC: 解析EPUB失败:",t),{success:!1,error:t instanceof Error?t.message:"解析失败"}}}async handleGetFileInfo(e){try{const t=this.validateFile(e);if(!t.isValid)throw new Error(t.error);const r=$.statSync(e);return{success:!0,fileInfo:{path:e,name:B.basename(e),size:r.size,lastModified:r.mtime,isValid:!0}}}catch(t){return{success:!1,error:t instanceof Error?t.message:"获取文件信息失败"}}}async handleCreateReader(e,t,r){try{console.log(`EpubReaderIPC: 开始创建阅读器实例 ${e}`),this.initCreationProgress(e),this.updateCreationProgress(e,"validation",10,"验证文件...");const o=this.validateFile(t);if(!o.isValid)throw new Error(o.error);this.updateCreationProgress(e,"creation",30,"创建组件...");const n=new zt,i=new Bo(r);this.updateCreationProgress(e,"parsing",50,"快速解析EPUB文件...");const a=await n.parseEpubBasic(t);this.updateCreationProgress(e,"setup",80,"设置阅读器..."),i.setParseResult(a),i.setParser(n);const l={id:e,filePath:t,parser:n,reader:i,parseResult:a,createdAt:Date.now(),lastAccessedAt:Date.now()};return this.readers.set(e,l),this.updateCreationProgress(e,"complete",100,"创建完成"),console.log(`EpubReaderIPC: 阅读器实例创建成功 ${e}`),{success:!0}}catch(o){return console.error(`EpubReaderIPC: 创建阅读器实例失败 ${e}:`,o),this.updateCreationProgress(e,"error",0,"创建失败",o instanceof Error?o.message:"未知错误"),{success:!1,error:o instanceof Error?o.message:"创建阅读器失败"}}}async handleGetChapter(e,t){try{const r=this.getReaderInstance(e),o=await r.reader.getChapter(t);return r.lastAccessedAt=Date.now(),{success:!0,content:o.content,chapter:o}}catch(r){return console.error(`EpubReaderIPC: 获取章节失败 ${e}:`,r),{success:!1,error:r instanceof Error?r.message:"获取章节失败"}}}async handleGetToc(e){try{const t=this.getReaderInstance(e),r=t.reader.getToc();return t.lastAccessedAt=Date.now(),{success:!0,chapters:r.toc,bookInfo:r.bookInfo,toc:r.toc}}catch(t){return console.error(`EpubReaderIPC: 获取目录失败 ${e}:`,t),{success:!1,error:t instanceof Error?t.message:"获取目录失败"}}}async handleSearch(e,t){try{const r=this.getReaderInstance(e),o=await r.reader.search(t);return r.lastAccessedAt=Date.now(),o}catch(r){return console.error(`EpubReaderIPC: 搜索失败 ${e}:`,r),[]}}async handleNavigate(e,t,r){try{const o=this.getReaderInstance(e);switch(t){case"goToChapter":await o.reader.goToChapter(r.chapterIndex);break;default:throw new Error(`未知的导航操作: ${t}`)}return o.lastAccessedAt=Date.now(),{success:!0}}catch(o){return console.error(`EpubReaderIPC: 导航失败 ${e}:`,o),{success:!1,error:o instanceof Error?o.message:"导航失败"}}}async handleUpdatePosition(e,t){try{const r=this.getReaderInstance(e);return r.reader.updatePosition(t),r.lastAccessedAt=Date.now(),{success:!0}}catch(r){return console.error(`EpubReaderIPC: 更新位置失败 ${e}:`,r),{success:!1,error:r instanceof Error?r.message:"更新位置失败"}}}async handleGetStatus(e){try{const t=this.getReaderInstance(e),r=t.reader.getStatus();return t.lastAccessedAt=Date.now(),r}catch(t){return console.error(`EpubReaderIPC: 获取状态失败 ${e}:`,t),null}}async handleDestroyReader(e){try{const t=this.readers.get(e);return t&&(t.reader.destroy(),t.parser.destroy(),this.readers.delete(e),this.creationProgress.delete(e),console.log(`EpubReaderIPC: 阅读器实例已销毁 ${e}`)),!0}catch(t){return console.error(`EpubReaderIPC: 销毁阅读器失败 ${e}:`,t),!1}}getReaderInstance(e){const t=this.readers.get(e);if(!t)throw new Error(`阅读器实例不存在: ${e}`);return t}validateFile(e){try{return!e||typeof e!="string"?{isValid:!1,error:"文件路径无效"}:$.existsSync(e)?B.extname(e).toLowerCase()!==".epub"?{isValid:!1,error:"不是有效的EPUB文件"}:$.statSync(e).size===0?{isValid:!1,error:"文件为空"}:{isValid:!0}:{isValid:!1,error:"文件不存在"}}catch(t){return{isValid:!1,error:`文件验证失败: ${t instanceof Error?t.message:"未知错误"}`}}}initCreationProgress(e){this.creationProgress.set(e,{readerId:e,stage:"init",progress:0,message:"开始创建...",timeElapsed:0})}updateCreationProgress(e,t,r,o,n){const i=this.creationProgress.get(e);i&&this.creationProgress.set(e,{...i,stage:t,progress:r,message:o,timeElapsed:Date.now()-(i.timeElapsed||Date.now()),error:n})}getCreationProgress(e){return this.creationProgress.get(e)||null}unregisterHandlers(){["epub-reader:parse-epub","epub-reader:get-file-info","epub-reader:create","epub-reader:get-creation-progress","epub-reader:destroy","epub-reader:get-chapter","epub-reader:get-toc","epub-reader:search","epub-reader:navigate","epub-reader:get-status","epub-reader:update-position"].forEach(t=>{O.ipcMain.removeAllListeners(t)}),this.readers.forEach(t=>{t.reader.destroy(),t.parser.destroy()}),this.readers.clear(),this.creationProgress.clear(),console.log("EpubReaderIPCHandler: 所有处理器已注销")}}class ko{constructor(e){R(this,"bookService");this.bookService=e}registerHandlers(){console.log("ReaderIPCHandler: 开始注册阅读器IPC处理器..."),O.ipcMain.handle("reader:get-book",async(e,t)=>{try{if(console.log(`ReaderIPC: 获取书籍信息 ${t}`),!t||typeof t!="string")return console.error("ReaderIPC: 无效的书籍ID"),null;const r=await this.bookService.getBookById(t);return r?(console.log(`ReaderIPC: 成功获取书籍 "${r.title}"`),r):(console.log(`ReaderIPC: 未找到书籍 ${t}`),null)}catch(r){return console.error(`ReaderIPC: 获取书籍失败 ${t}:`,r),null}}),O.ipcMain.handle("reader:get-progress",async(e,t)=>{try{if(console.log(`ReaderIPC: 获取阅读进度 ${t}`),!t||typeof t!="string")return console.error("ReaderIPC: 无效的书籍ID"),null;const r=await this.bookService.getBookById(t);if(r){const o={currentPage:r.current_page||r.currentPage||1,progress:r.progress||0};return console.log(`ReaderIPC: 成功获取进度 - 页码: ${o.currentPage}, 进度: ${o.progress}%`),o}else return console.log(`ReaderIPC: 未找到书籍进度 ${t}`),null}catch(r){return console.error("ReaderIPC: 获取进度失败:",r),null}}),O.ipcMain.handle("reader:update-progress",async(e,t,r,o)=>{try{if(console.log(`ReaderIPC: 更新阅读进度 ${t}, 进度: ${r}%, 页码: ${o}`),!t||typeof t!="string")return console.error("ReaderIPC: 无效的书籍ID"),!1;const n=await this.bookService.updateProgress(t,r,o);return console.log(`ReaderIPC: 更新进度${n?"成功":"失败"}`),n}catch(n){return console.error("ReaderIPC: 更新进度失败:",n),!1}}),O.ipcMain.handle("reader:check-file",async(e,t)=>{try{return console.log(`ReaderIPC: 检查文件 ${t}`),!t||typeof t!="string"?(console.error("ReaderIPC: 无效的文件路径"),!1):!0}catch(r){return console.error("ReaderIPC: 检查文件失败:",r),!1}}),O.ipcMain.handle("reader:get-books",async()=>{try{console.log("ReaderIPC: 获取书籍列表");const e=await this.bookService.getAllBooks();return console.log(`ReaderIPC: 成功获取 ${e.length} 本书籍`),e}catch(e){return console.error("ReaderIPC: 获取书籍列表失败:",e),[]}}),console.log("ReaderIPCHandler: 阅读器IPC处理器注册完成")}unregisterHandlers(){["reader:get-book","reader:get-progress","reader:update-progress","reader:check-file","reader:get-books"].forEach(t=>{O.ipcMain.removeAllListeners(t)}),console.log("ReaderIPCHandler: 阅读器IPC处理器注销完成")}}class Xo{constructor(e){R(this,"databaseManager");R(this,"fileManager");R(this,"windowManager");R(this,"bookService");R(this,"bookIPCHandler");R(this,"txtReaderIPCHandler");R(this,"epubReaderIPCHandler");R(this,"readerIPCHandler");this.databaseManager=new oi,this.fileManager=new si,this.windowManager=e,this.bookService=new ai(this.databaseManager,this.fileManager),this.bookIPCHandler=new ci(this.bookService),this.txtReaderIPCHandler=new qi,this.epubReaderIPCHandler=new Uo,this.readerIPCHandler=new ko(this.bookService)}async initialize(){try{console.log("ServiceManager: 开始初始化服务..."),await this.databaseManager.initialize(),console.log("ServiceManager: 数据库初始化完成"),await this.autoFixDatabasePaths(),await this.fileManager.initialize(),console.log("ServiceManager: 文件管理器初始化完成"),this.bookIPCHandler.registerHandlers(),this.txtReaderIPCHandler.registerHandlers(),this.epubReaderIPCHandler.registerHandlers(),this.readerIPCHandler.registerHandlers(),this.registerBookmarkHandlers(),this.registerFileHandlers(),this.registerWindowHandlers(),this.registerShellHandlers(),console.log("ServiceManager: IPC处理器注册完成"),console.log("ServiceManager: 所有服务初始化完成")}catch(e){throw console.error("ServiceManager: 服务初始化失败:",e),e}}async cleanup(){try{console.log("ServiceManager: 开始清理服务..."),this.bookIPCHandler.unregisterHandlers(),this.txtReaderIPCHandler.unregisterHandlers(),this.epubReaderIPCHandler.unregisterHandlers(),this.readerIPCHandler.unregisterHandlers(),this.unregisterBookmarkHandlers(),this.unregisterFileHandlers(),this.unregisterWindowHandlers(),this.unregisterShellHandlers(),console.log("ServiceManager: IPC处理器注销完成"),await this.databaseManager.close(),console.log("ServiceManager: 数据库连接关闭完成"),console.log("ServiceManager: 所有服务清理完成")}catch(e){throw console.error("ServiceManager: 服务清理失败:",e),e}}registerBookmarkHandlers(){O.ipcMain.handle("bookmark:add",async(e,t)=>{try{const r=this.databaseManager.getDatabase(),o=require("crypto").randomUUID(),n=new Date,i={...t,id:o,createdAt:n,updatedAt:n};return r.prepare(`
          INSERT INTO bookmarks (id, book_id, title, content, position, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `).run(i.id,i.bookId,i.title,i.content||"",i.position,i.createdAt.toISOString(),i.updatedAt.toISOString()),console.log("ServiceManager: 书签添加成功",i.id),i}catch(r){throw console.error("ServiceManager: 添加书签失败:",r),r}}),O.ipcMain.handle("bookmark:remove",async(e,t)=>{try{const n=this.databaseManager.getDatabase().prepare("DELETE FROM bookmarks WHERE id = ?").run(t);return console.log("ServiceManager: 书签删除成功",t),n.changes>0}catch(r){throw console.error("ServiceManager: 删除书签失败:",r),r}}),O.ipcMain.handle("bookmark:list",async(e,t)=>{try{const i=this.databaseManager.getDatabase().prepare(`
          SELECT * FROM bookmarks
          WHERE book_id = ? AND deleted_at IS NULL
          ORDER BY created_at DESC
        `).all(t).map(a=>({...a,createdAt:new Date(a.created_at),updatedAt:new Date(a.updated_at)}));return console.log(`ServiceManager: 获取书签列表成功，共 ${i.length} 个书签`),i}catch(r){throw console.error("ServiceManager: 获取书签列表失败:",r),r}}),console.log("ServiceManager: 书签IPC处理器注册完成")}registerFileHandlers(){O.ipcMain.handle("file:read",async(e,t)=>this.fileManager.readFile(t)),O.ipcMain.handle("file:exists",async(e,t)=>this.fileManager.fileExists(t)),O.ipcMain.handle("file:select",async(e,t)=>this.fileManager.selectFiles(t)),O.ipcMain.handle("file:read-pdf",async(e,t)=>{try{console.log("IPC: 读取PDF文件:",t);const r=require("fs");if(!r.existsSync(t))throw new Error(`文件不存在: ${t}`);const o=r.readFileSync(t),n=o.buffer.slice(o.byteOffset,o.byteOffset+o.byteLength);return console.log(`IPC: 成功读取PDF文件，大小: ${n.byteLength} 字节`),{success:!0,data:n,size:n.byteLength}}catch(r){return console.error("IPC: 读取PDF文件失败:",r),{success:!1,error:r instanceof Error?r.message:"未知错误"}}}),O.ipcMain.handle("file:get-data-url",async(e,t)=>{try{console.log("IPC: 获取文件数据URL:",t);const r=require("fs"),o=require("path");if(!r.existsSync(t))throw new Error(`文件不存在: ${t}`);const n=r.readFileSync(t),i=o.extname(t).toLowerCase();let a="application/octet-stream";switch(i){case".pdf":a="application/pdf";break;case".txt":a="text/plain";break;case".epub":a="application/epub+zip";break;case".jpg":case".jpeg":a="image/jpeg";break;case".png":a="image/png";break}const l=n.toString("base64"),f=`data:${a};base64,${l}`;return console.log(`IPC: 成功生成数据URL，MIME类型: ${a}，大小: ${n.length} 字节`),{success:!0,dataUrl:f,mimeType:a,size:n.length}}catch(r){return console.error("IPC: 获取文件数据URL失败:",r),{success:!1,error:r instanceof Error?r.message:"未知错误"}}}),console.log("ServiceManager: 文件IPC处理器注册完成")}unregisterBookmarkHandlers(){["bookmark:add","bookmark:remove","bookmark:list"].forEach(t=>{O.ipcMain.removeAllListeners(t)}),console.log("ServiceManager: 书签IPC处理器注销完成")}unregisterFileHandlers(){["file:read","file:exists","file:select","file:read-pdf","file:get-data-url"].forEach(t=>{O.ipcMain.removeAllListeners(t)}),console.log("ServiceManager: 文件IPC处理器注销完成")}registerWindowHandlers(){O.ipcMain.handle("window:minimize",()=>{this.windowManager.minimizeWindow()}),O.ipcMain.handle("window:maximize",()=>{this.windowManager.toggleMaximizeWindow()}),O.ipcMain.handle("window:close",()=>{this.windowManager.closeWindow()}),O.ipcMain.handle("window:toggle-fullscreen",()=>{this.windowManager.toggleFullscreen()}),console.log("ServiceManager: 窗口IPC处理器注册完成")}registerShellHandlers(){O.ipcMain.handle("shell:open-path",async(e,t)=>{try{console.log(`Shell: 尝试打开文件 ${t}`);const{shell:r}=require("electron"),o=await r.openPath(t);if(o)throw console.log(`Shell: 打开文件失败 ${t}, 错误: ${o}`),new Error(o);return console.log(`Shell: 成功打开文件 ${t}`),"success"}catch(r){throw console.error(`Shell: 打开文件失败 ${t}:`,r),r}}),console.log("ServiceManager: Shell IPC处理器注册完成")}unregisterWindowHandlers(){["window:minimize","window:maximize","window:close","window:toggle-fullscreen"].forEach(t=>{O.ipcMain.removeAllListeners(t)}),console.log("ServiceManager: 窗口IPC处理器注销完成")}unregisterShellHandlers(){["shell:open-path"].forEach(t=>{O.ipcMain.removeAllListeners(t)}),console.log("ServiceManager: Shell IPC处理器注销完成")}async autoFixDatabasePaths(){try{console.log("ServiceManager: 开始重新加载图书数据...");const e=this.databaseManager.getDatabase(),t=require("fs"),r=require("path");console.log("ServiceManager: 清空现有图书数据..."),e.prepare("DELETE FROM books").run();const o=r.join(process.cwd(),"books"),n=["txt","epub","pdf"];let i=0;for(const a of n){const l=r.join(o,a);if(t.existsSync(l)){console.log(`ServiceManager: 扫描 ${a.toUpperCase()} 文件...`);const f=t.readdirSync(l).filter(m=>m.endsWith(`.${a}`));for(const m of f)try{const y=r.join(l,m),w=t.statSync(y),h=new Date().toISOString(),E=m.replace(`.${a}`,"").replace(/^\d+_/,""),g=r.join(l,"covers"),_=m.replace(`.${a}`,"_cover.svg"),b=r.join(g,_),u=t.existsSync(b);e.prepare(`
                INSERT INTO books (
                  title, author, file_path, file_format, file_size,
                  cover_image, description, language, reading_status,
                  current_page, reading_progress_percent, import_time,
                  tags, created_at, updated_at, sync_version
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              `).run(E,"未知作者",y,a,w.size,u?b:null,`${E} - ${a.toUpperCase()}格式`,"zh-CN","unread",0,0,h,JSON.stringify([a]),h,h,1),i++,console.log(`ServiceManager: ✅ 添加图书: ${E} (${a})`)}catch(y){console.error(`ServiceManager: ❌ 添加文件失败 ${m}:`,y)}}else console.log(`ServiceManager: ⚠️ ${a.toUpperCase()} 目录不存在: ${l}`)}console.log(`ServiceManager: 图书重新加载完成！共添加 ${i} 本图书`)}catch(e){console.error("ServiceManager: 重新加载图书数据失败:",e)}}getServices(){return{databaseManager:this.databaseManager,fileManager:this.fileManager,windowManager:this.windowManager,bookService:this.bookService}}}class $o{constructor(){R(this,"windowManager");R(this,"menuManager");R(this,"serviceManager");this.windowManager=new Jn,this.menuManager=new Qn,this.serviceManager=new Xo(this.windowManager)}async initialize(){try{await this.serviceManager.initialize(),console.log("服务管理器初始化完成"),this.menuManager.setupMenu(),console.log("应用菜单设置完成"),console.log("Yu Reader 应用初始化完成")}catch(e){throw console.error("应用初始化失败:",e),e}}async createMainWindow(){return this.windowManager.createMainWindow()}async cleanup(){try{await this.serviceManager.cleanup(),console.log("服务管理器清理完成"),console.log("应用清理完成")}catch(e){console.error("应用清理失败:",e)}}}const Ae=new $o;O.app.whenReady().then(async()=>{try{console.log("Electron 应用已准备就绪，开始初始化..."),await Ae.initialize(),console.log("应用初始化完成，创建主窗口...");const s=await Ae.createMainWindow();console.log("主窗口创建完成，应用启动成功"),console.log("窗口ID:",s.id)}catch(s){console.error("应用启动失败:",s),console.error("错误堆栈:",s.stack),O.app.quit()}});O.app.on("window-all-closed",()=>{process.platform!=="darwin"&&O.app.quit()});O.app.on("activate",async()=>{O.BrowserWindow.getAllWindows().length===0&&await Ae.createMainWindow()});O.app.on("before-quit",async s=>{s.preventDefault(),await Ae.cleanup(),O.app.exit()});process.on("uncaughtException",s=>{var e,t,r;console.error("🚨 未捕获的异常:",s),console.error("错误堆栈:",s.stack),(e=s.message)!=null&&e.includes("EADDRINUSE")||(t=s.message)!=null&&t.includes("EACCES")||(r=s.message)!=null&&r.includes("Cannot find module")?(console.error("💥 严重错误，应用将退出"),O.app.exit(1)):console.warn("⚠️ 非严重错误，应用继续运行")});process.on("unhandledRejection",(s,e)=>{var t,r;console.error("🚨 未处理的 Promise 拒绝:",s,"at:",e),s instanceof Error&&console.error("Promise拒绝错误堆栈:",s.stack),(t=s==null?void 0:s.toString())!=null&&t.includes("FATAL")||(r=s==null?void 0:s.toString())!=null&&r.includes("CRITICAL")?(console.error("💥 严重Promise拒绝，应用将退出"),O.app.exit(1)):console.warn("⚠️ 非严重Promise拒绝，应用继续运行")});
