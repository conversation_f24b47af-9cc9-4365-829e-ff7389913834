{"name": "yu-reader-desktop", "version": "1.0.0", "description": "Yu Reader - 电子书阅读器桌面应用", "main": "dist/main/app.js", "author": "Yu Reader Team", "license": "MIT", "private": true, "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\" \"npm run dev:preload\"", "dev:main": "vite build --config vite.main.config.ts --mode development --watch", "dev:renderer": "vite --config vite.renderer.config.ts", "dev:preload": "vite build --config vite.preload.config.ts --mode development --watch", "build": "npm run build:main && npm run build:renderer && npm run build:preload", "build:main": "vite build --config vite.main.config.ts", "build:renderer": "vite build --config vite.renderer.config.ts && node scripts/fix-fs-imports.js", "build:preload": "vite build --config vite.preload.config.ts", "start": "npm run build && electron .", "start:dev": "wait-on dist/main/app.js && cross-env NODE_ENV=development electron .", "pack": "electron-builder --dir", "dist": "electron-builder", "dist:win": "electron-builder --win", "dist:mac": "electron-builder --mac", "dist:linux": "electron-builder --linux", "test": "vitest", "test:ui": "vitest --ui", "test:database": "vitest --config vitest.database.config.ts", "test:database:run": "vitest run --config vitest.database.config.ts", "lint": "eslint src --ext .ts,.vue --fix", "format": "prettier --write \"src/**/*.{ts,vue,json}\"", "type-check": "vue-tsc --noEmit", "prepare": "husky install", "generate-txt-books": "node scripts/generateTxtBooks.js", "generate-txt-books:clean": "node scripts/generateTxtBooks.js --clean"}, "dependencies": {"@types/better-sqlite3": "^7.6.13", "@types/iconv-lite": "^0.0.1", "@types/jszip": "^3.4.0", "@types/uuid": "^9.0.8", "@xmldom/xmldom": "^0.9.8", "adm-zip": "^0.5.16", "archiver": "^7.0.1", "better-sqlite3": "^9.6.0", "canvas": "^3.1.2", "dayjs": "^1.11.13", "electron-updater": "^6.1.4", "iconv-lite": "^0.6.3", "jszip": "^3.10.1", "rimraf": "^6.0.1", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "vue3-pdf-app": "^1.0.3", "xml2js": "^0.6.2"}, "devDependencies": {"@electron/rebuild": "^3.3.0", "@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@vitejs/plugin-vue": "^4.4.0", "@vitest/ui": "^0.34.6", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.6", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "electron": "^37.2.4", "electron-builder": "^24.6.4", "element-plus": "^2.4.0", "eslint": "^8.50.0", "eslint-plugin-vue": "^9.17.0", "glob": "^11.0.3", "happy-dom": "^18.0.1", "husky": "^8.0.3", "lint-staged": "^14.0.1", "pinia": "^2.1.6", "prettier": "^3.0.3", "ts-node": "^10.9.2", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^0.34.6", "vue": "^3.4.0", "vue-router": "^4.2.5", "vue-tsc": "^1.8.15", "wait-on": "^7.0.1"}, "build": {"appId": "com.yureader.desktop", "productName": "<PERSON>", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "build/", "to": "build/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "build/icon.ico"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "build/icon.icns", "category": "public.app-category.productivity"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "build/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "lint-staged": {"*.{ts,vue}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}