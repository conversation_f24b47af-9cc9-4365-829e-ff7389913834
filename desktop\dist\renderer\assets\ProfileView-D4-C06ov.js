import{d as c,e as v,c as u,f as s,a as o,w as l,u as a,an as p,r as _,ao as f,ap as m,j as k,o as w,_ as C}from"./index-DS8Rgqx0.js";const g={class:"profile-view"},x={class:"profile-content"},V={class:"quick-nav"},B={class:"nav-icon"},$={class:"nav-icon"},N={class:"nav-icon"},P=c({__name:"ProfileView",setup(b){const r=k(),e=i=>{r.push(i)};return v(()=>{console.log("个人中心页面已加载")}),(i,n)=>{const t=_("el-icon");return w(),u("div",g,[n[6]||(n[6]=s("div",{class:"page-header"},[s("h1",{class:"page-title"},"个人中心"),s("p",{class:"page-description"},"用户信息和统计")],-1)),s("div",x,[s("div",V,[s("div",{class:"nav-card",onClick:n[0]||(n[0]=d=>e("/profile/reports"))},[s("div",B,[o(t,null,{default:l(()=>[o(a(p))]),_:1})]),n[3]||(n[3]=s("div",{class:"nav-content"},[s("h3",null,"学习报告"),s("p",null,"查看学习统计")],-1))]),s("div",{class:"nav-card",onClick:n[1]||(n[1]=d=>e("/profile/account"))},[s("div",$,[o(t,null,{default:l(()=>[o(a(f))]),_:1})]),n[4]||(n[4]=s("div",{class:"nav-content"},[s("h3",null,"账号设置"),s("p",null,"管理账号信息")],-1))]),s("div",{class:"nav-card",onClick:n[2]||(n[2]=d=>e("/profile/network"))},[s("div",N,[o(t,null,{default:l(()=>[o(a(m))]),_:1})]),n[5]||(n[5]=s("div",{class:"nav-content"},[s("h3",null,"网络设置"),s("p",null,"配置网络连接")],-1))])])])])}}}),q=C(P,[["__scopeId","data-v-1947eb2a"]]);export{q as default};
