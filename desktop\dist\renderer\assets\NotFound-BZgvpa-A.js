import{d as c,c as l,a as e,w as o,r as n,l as u,j as d,o as p,_ as i}from"./index-DS8Rgqx0.js";const m={class:"not-found"},f=c({__name:"NotFound",setup(x){const s=d(),a=()=>{s.push("/library")};return(b,t)=>{const r=n("el-button"),_=n("el-result");return p(),l("div",m,[e(_,{icon:"warning",title:"404","sub-title":"抱歉，您访问的页面不存在"},{extra:o(()=>[e(r,{type:"primary",onClick:a},{default:o(()=>t[0]||(t[0]=[u("返回首页")])),_:1,__:[0]})]),_:1})])}}}),N=i(f,[["__scopeId","data-v-9acdf3bd"]]);export{N as default};
